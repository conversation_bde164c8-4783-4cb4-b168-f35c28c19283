银企直联接口文档公共说明

邮箱：<EMAIL>

1. 前言

本文档作为民生银行银企直联项目测试依据，企业客户端根据本文档组织报文发送给民生

银行相应系统，民生银行相应系统根据此接口文档返回给企业客户端。

2.请求/响应模式

本系统运行在基于请求/响应模式的客户机/服务器体系下，采用 HTTP 1.0/1.1 作为企业系

统与银行系统之间的通信协议，传输文件格式符合 XML 1.0 规范。

3.说明

企业接入银企直连测试，都先要企业提交测试申请，在其中填明需要测试的业务功能点，

准入测试后方可开始测试。测试完成，由总行科技根据其提交的测试报告予以验证，并给

与是否通过测试的审核 。

测试报告中，需要填写与如上功能测试匹配的 CFCA 代理软件的业务日志，该日志可从

C:\Program Files\CMBC\Security Proxy 目录下获取，文件名例如：

SecurityProxy_20130417.Log。

4.交换数据的结构

企业客户端通过 HTTP POST 向民生银行的网上银行（以下简称网上银行）服务器发送请

求命令，网上银行处理企业客户端发出的请求，并生成响应内容，请求与响应的报文结构

包括：

HTTP headers

MIME type application/x-NS-BDES

XML declaration

CMBC declaration

CMBC XML block

以上 1-2 遵照 HTTP 协议规范(www.w3c.org)，3-5 部分为请求或响应文件内容，依照 HTTP

协议这两部分内容之间通过一个空行(CR LF)分隔。

HTTP headers 中 Content-type 应设置为：application/x-NS-BDES

5.请求/响应示例

请求：

<?xml version="1.0" encoding="GB2312" standalone="no" ?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="xfer">

<requestHeader>

...

</requestHeader>

<xDataBody>

...

</xDataBody>

</CMBC>

响应：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="xfer">

<responseHeader>

...

</responseHeader>

<xDataBody>

...

</xDataBody>

</CMBC>

6.文件格式

文件格式包括 xml 声明，必须在最开始，xml 声明如下所示：

<?xml version="1.0" encoding="UTF-8" ?>

其中，encoding 标示了本 XML 报文的编码方式。可选：UTF-8、GBK、GB2312 。建

议使用 UTF-8。注意：若报文中包含生僻字，请务必使用 UTF-8 字符集。（民生银行早

期使用 chs，chs 等同于 GB2312）

接下来是 CMBC 协议的 XML 顶层块

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="qryXfer" >

其中：

header 为 CMBC 协议声明的版本代号，目前为 1.00 版，表示为"100"。

version 为 CMBC 协议的版本号，目前为 1.00 版，表示为"100"。

security 为采用的安全模式，这一版本的 BDES 在 CMBC 协议层未提供安全实现，数

据的安全依赖安全代理(HTTP 代理)或传输时的签名与加密行为来实现客户端与服务器之

间的通信安全。因此目前应选择"none"。

lang 表示客户端，希望获得的语言类型，主要用于服务器响应信息，对于客户提交的

信息，服务器不进行语言的转换。HTTP 相应代码对应的信息按照 HTTP 1.1 或 HTTP

1.0 的定义，LANG 的作用范围仅限 CMBC 报文块。

trnCode 表示客户端发起请求的交易代码，trnCode 作用范围仅限 CMBC 报文块。

如，查询余额为：qryBal；单笔转账为：Xfer；查询明细为：qryDtl。

7.XML 块结构

CMBC XML 块采用层次化结构 XML 数据块作为请求与响应的数据文件内容，层次结构如

下：

XML 块顶层<CMBC>

请求报文头 requestHeader（或者响应报文头 responseHeader）

交换数据部分 xDataBody

CMBC 的标记以及列举数据值采用首字母小写后续单词首字母大写的方式描述。每一个请

求或者响应的报文都包括报文头部分和报文体部分，其中报文头部分描述请求或者响应的

系统信息，报文体部分描述具体的数据交换信息。

8.整体结构

CMBC 的请求整体结构：

  标记(Tag)

<CMBC>

说明

开始 CMBC XML 块

 <requestHeader>

请求头信息

 操作员信息

包括时间，操作员代码等信息

 </requestHeader>

请求头信息结束

 <xDataBody>

数据交换信息集

 CMBC 交易请求消息

0-n 个交易的请求消息

 </xDataBody>

数据交换信息集 XML 结束

</CMBC>

CMBC XML 结束

CMBC 的响应整体结构：

  标记(Tag)

<CMBC>

说明

开始 CMBC XML 块

 <responseHeader>

请求头信息

 状态信息

包括时间，操作员代码等信息

 </responseHeader>

请求头信息结束

 <xDataBody>

数据交换信息集

 <CMBC 交易响应消息>

0-n 个交易的响应消息

 </xDataBody>

数据交换信息集 XML 结束

</CMBC>

CMBC XML 结束

CMBC 属性说明

属性

说明

(attribute)

 header

请求头信息

 version

版本信息

 security

安全信息

 lang

语言（本处与 4.2 章节 xml 节点中 encoding 相同即可。）

 trnCode

交易请求代码，对于不同的交易，请求代码是不同的，

如： qryHistoryBal-历史余额查询 qryTrsDtl-交易明细查询 xfer-转

账 qryxfer-对账 qryBalNew-新余额查询交易

8.1.请求/响应头信息

登录消息用于控制对服务访问的控制，以及客户服务器之间进行通信的会话管理。登录消

息 logon-rq/logon-rs 包装在 msg-logon-1 中。每个 CMBC 请求必须并仅能包含一个 logon-

rq，每个 CMBC 响应必须并仅能包含一个 logon-rs。

8.2.请求头信息(requestHeader)

   标记  

说明

<requestHeader> 登录请求消息

 <dtClient>

客户端日期时间，YYYY-MM-DD HH:MM:SS（★）

 <clientId>

企业客户号（★）

 <userId>

登录操作员用户号（★）

 <userPswd>

登录密码（★）

 <language>

希望服务器响应信息使用的语言的字符集编码，可选：UTF-8、

GBK、GB2312 。 建议使用 UTF-8。注意：若报文中包含生僻

字，请务必使用 UTF-8 字符集。 （民生银行早期使用 chs，chs

等同于 GB2312）

 <appId>

应用程序编码，目前取 nsbdes

 <appVer>

应用程序版本 nnn，目前取 201

</requestHeader>

8.3.响应头信息(responseHeader)

   标记 

说明

< responseHeader >

登录响应消息

<status>  

<code>  

<severity>  

<message>

</status>

常见状态码 code：

0：成功；

WEC32：当是查询交易表示查询条件错误，一般指起始

记录数<=0; WEC02：转账的时候出现网络异常，具体

转账成败未知； 其他：失败代码，表示交易失败。

E1602：单笔对账交易，若在报文头 code 字段返回

E1602，则表示该笔交易银行未受理，可以视为交易失

败 描述信息： ok，交易成功。

其他：交易描述

 <dtServer>

服务端日期时间，YYYY-MM-DD HH:MM:SS（★）

 <userKey>

默认值 N

 <dtDead>

userkey 的有效时间(服务器时间)

 <language>

服务器响应信息使用的语言，目前仅提供 chs(中文简

体)，可选

</responseHeader>

code、severity、message 描述交易进行的情况，其中 code 为交易处理结果码，在响

应消息中包括有效的响应内容；

severity 为处理结果安全信息，含义如下：

  取值

含义

 Info

响应消息有效

 Warn

响应消息有效，但含有警告信息(安全提示)

 Error

响应消息无效；message 中的信息为警告或错误的解释文本

9.数据交换(xDataBody)

1.xDataBody 是 CMBC 的工作单元，每一个请求和响应都包括消息 xDataBody，

xDataBody 描述交易中交换数据需要的信息。

2.其中每个交易都包括 trnId、cltcookie(可选)、insId(部分交易必选)。

3.trnId 为客户端的技术流水号（最长 64 位字母和数字的组合），一个交易对应一个

trnId（无论是资金交易还是查询交易），服务器应针对每个交易给出响应，因此响应内

容中包含的 trnId 与请求中的会保持一致；客户应将此字段设置为全局唯一字段。不同的

交易，建议使用不同的 trnId。

4.cltcookie 为客户端的 cookie 值，当请求中包括 cltcookie 时，服务器在响应中将

包含相同的内容。

5.insId 是业务流水号（最长 64 位字母和数字的组合），一般转账等资金类交易需要

上送本字段，该流水号标识一笔业务。如发起转账时，上送该字段，之后再发起转账结果

查询时，需再次上送该字段的原值，用于查询该转账业务的状态。

例如，一个交易的格式如下：

  标记

<xDataBody>

说明

交易请求的开始

 <trnId>123456</trnId>

本次交易的客户端技术流水号。

 <insId>E20210101</insId>

本次交易的客户端的业务流水号。

 <cltcookie>COOKIE VALUE </cltcookie> 客户端 cookie，可选

 ……

</xDataBody>

响应数据格式为：

  标记

请求内容

交易的结束

说明

< xDataBody >

交易请求的开始

 <trnId>123456</trnId>

本次交易的客户端技术流水号。

 <insId>E20210101</insId>

本次交易的客户端业务流水号。

 <cltcookie>  COOKIE

VALUE  </cltcookie>

 ……

</xDataBody>

客户端 cookie，可选

响应内容

交易的结束

10.完整 HTTP 报文示例

请求报文

POST /eweb/b2e/connect.do HTTP/1.1

User-Agent: Java/1.6.0_22

Host: ***********:8080

Accept: */*

Content-type: application/x-NS-BDES

Content-Length: 997

<?xml version="1.0" encoding="GBK"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="TransferXfer">

<requestHeader>

<dtClient>2021-03-17 10:00:01</dtClient>

<clientId>*********0</clientId>

<userId>*********0001</userId>

<userPswd>111</userPswd>

<language>GBK</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>f0uui0pu</trnId>

<insId>**********</insId>

<acntNo>*********</acntNo>

<acntName>厦门测试 *********</acntName>

<acntToNo>****************</acntToNo>

<acntToName>王晓成</acntToName>

<externBank>0</externBank>

<localFlag>1</localFlag>

<rcvCustType>1</rcvCustType>

<bankName>中国民生银行股份有限公司北京林萃路支行</bankName>

<bankAddr>北京</bankAddr>

<amount>3411.00</amount>

<explain>测试 111</explain>

<actDate>2021-03-17</actDate>

</xDataBody>

</CMBC>

响应报文：

HTTP/1.1 200 OK

Date: Wed, 17 Mar 2021 02:00:02 GMT

Server: Apache

Content-Length: 487

Set-Cookie: ESESSIONID=f5w96W3woBPGLL-

5azwDCSXeP7q3XUhWcTOQKaUJ_oCgr5tO_VcZ!*********; path=/; HttpOnly

Connection: close

Content-Type: text/plain

Set-Cookie:

BIGipServerebank_gerenwangyinbanbenyanzheng_WEB_55902_pool=!CZycf

8oupC3rr1/kF8Ydnljza0+IbY5F+uXwuEY4AEdH1L5tV2sN+VNvwfEO4f2NohnSj

CEE9DWD; path=/

<?xml version="1.0" encoding="GBK"?>

<CMBC trnCode="TransferXfer" security="none" lang="chs" header="100"

version="100" >

<responseHeader>

<status>

<code>DM004006</code>

<severity>Error</severity>

<message>账号在系统中不存在</message>

</status>

<dtServer>2021-03-17 10:00:02</dtServer>

<dtDead></dtDead>

<language>GBK</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>f0uui0pu</trnId>

<svrId></svrId>

<insId>**********</insId>

</transfer>

</xDataBody>

</CMBC>