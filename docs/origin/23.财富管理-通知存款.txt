银企直联接口文档

（账户管理-账户查询）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 25

目录

民生银行银企直联

目录 .............................................................................................2

1. 定期存款列表查询(QUERYREGULARLIST) ........................................... 4

1.1. 请求(QUERYREGULARLIST) ...........................................................4

1.2. 响应(QUERYREGULARLIST) ...........................................................4

1.3. 例子 ...................................................................................... 7

2. 活期转定期交易(REGULARD2B) ........................................................ 9

2.1. 请求(REGULARD2B) ...................................................................9

2.2. 响应(REGULARD2B) .................................................................10

2.3. 例子 .................................................................................... 10

3. 通知存款通知撤销(DEPOSITNOTICECANCEL) .................................... 12

3.1. 请求(DEPOSITNOTICECANCEL) .................................................... 12

3.2. 响应(DEPOSITNOTICECANCEL) .................................................... 12

3.3. 例子 .................................................................................... 13

4. 新通知存款通知(DEPOSITNOTICENEW) ........................................... 15

4.1. 请求(DEPOSITNOTICENEW) ........................................................ 15

4.2. 响应(DEPOSITNOTICENEW) ........................................................ 16

4.3. 例子 .................................................................................... 16

5. 通知存款通知查询(QUERYNOTICE) .................................................. 18

5.1. 请求(QUERYNOTICE) ................................................................ 18

5.2. 响应(QUERYNOTICE) ................................................................ 19

5.3. 例子 .................................................................................... 19

6. 定期转活期交易(QUERYREGULARB2D) ............................................ 21

6.1. 请求(QUERYREGULARB2D) ........................................................22

6.2. 响应(QUERYREGULARB2D) ........................................................22

2 / 25

民生银行银企直联
6.3. 例子 .................................................................................... 23

3 / 25

民生银行银企直联

1.定期存款列表查询(queryRegularList)

本部分更新日期:2021-04-02

业务逻辑：

1：请求条数最高为 20 条；

2：当开始条数不是 1 的时候 totalNum 为 0；

3：查询本公司定期存款。类型详见<deptType>；

4：可以查询外币定期账户。

常见问题：

Q1：定期存款全部支取后是否可以通过网银查到原定期存款信息？

A1：定期存款全部支取后，该定期账户即被销户，不会再被查询到。

1.1.请求(queryRegularList)

  标记

说明

长度

<xDataBody>

 <inqStaNo>

开始条数（★）

 <pageRecNum> 请求条数（★）

</xDataBody>

1.2.响应(queryRegularList)

  标记

说明

长度

<xDataBody>

 <totalNum>

总条数

4 / 25

 <balList>

列表开始

  <balInfo>

0…n

  <acctName>

账户名称

  <account>

  <currCode>

账号

币种

  <acctBal>

账户余额

  <frzBal>

冻结金额

  <avlBal>

可用余额

  <openDate>

开户日期

  <intrStaDate>

起息日

  <dueDate>

到期日

  <rate>

利率

  <deptType>

存期类型

00-储蓄活期

01-储蓄整存整取

02-储蓄定活两便

03-储蓄存本取息

04-储蓄零存整取

05-储蓄通知存款

06-教育储蓄

09-钱生钱

10-对公活期

11-对公整存整取

15-对公通知存款

民生银行银企直联

64

32

16

15,2

15,2

15,2

8

10

8

9,7

32

5 / 25

民生银行银企直联

16

  <termCode>

25-对公保证金存款"

存期代码

200-活期

201-一天

207-七天

101-一月

102-三月

106-六月

001-一年

002-二年

003-三年

005-五年

006-六年

  <acctStasCode>

账户状态

1

0-正常

1-销户

2-只收不付冻结

3-封闭冻结

4-删除

5-未使用

6-结清

7-打印

8-碰库

9-不动户

A-不动户转收益

B-死亡户

C-报案户

D-请与开户行接洽

E-不能在他行销记户

6 / 25

民生银行银企直联

F-准客户

G-未复核

H-久悬未取户

R-被当日冲正

S-被隔日冲正

  <openBranchCode> 开户机构

80

  </balInfo>

 </balList>

列表结束

</xDataBody>

1.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="queryRegularList">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<inqStaNo>1</inqStaNo>

<pageRecNum>2</pageRecNum>

</xDataBody>

</CMBC>

响应报文

7 / 25

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="queryRegularList" security="none" lang="chs"

民生银行银企直联

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-12-02 14:14:23</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<totalNum>20</totalNum>

<balList>

<balInfo>

<acctName>测试 **********</acctName>

<account>*********</account>

<currCode>01</currCode>

<acctBal>10330.00</acctBal>

<frzBal>0.00</frzBal>

<avlBal>10330.00</avlBal>

<openDate>********</openDate>

<intrStaDate>********</intrStaDate>

<dueDate>********</dueDate>

<rate>3.3000000</rate>

<deptType>11</deptType>

<termCode>001</termCode>

<acctStasCode>0</acctStasCode>

<openBranchCode>3307</openBranchCode>

</balInfo>

</balList>

</xDataBody>

</CMBC>

8 / 25

2.活期转定期交易(regularD2B)

民生银行银企直联

本部分更新日期:2021-04-02

企业定期存款业务逻辑：

针对本客户下已追加网银账户，通过本功能将活期账户资金转存为定期资金，转为定期

存款后，该笔资金不会在活期账户中体现，系统自动生成定期存款账户。

存期：

1、人民币存期包括：3 月、6 月、1 年、2 年、3 年、5 年、1 天通知和 7 天通知；

2、外币存期包括：1 月、3 月、6 月、1 年、2 年和 7 天通知。

起存金额限制：

1：普通定期存款：起存金额大于等于 10000；

2：通知存款：起存金额大于等于 500000；

可通过 qryFixAcct（总公司定期存款查询交易）和 queryRegularList （定期存款列

表查询）两支交易查询本客户所有定期存款账户。

2.1.请求(regularD2B)

  标记

说明

<xDataBody>

 <insId>

交易流水号（唯一）（★）

 <payerAcct>

活期账户（★）

 <deptCode>

定期储种（★）5 位数字（参考注释）

 <payerAmt>

金额（★）

 <maturityFlag> 本金到期处理方式

1 本金续存

2 本息转出

长度

64

32

5

15,2

1

9 / 25

定期储种（deptCode）为通知存款（15201 ，

15207 ）时暂不支持 2（本息转出）

民生银行银企直联

</xDataBody>

注释：deptCode ：

11101 企业定期存款一月(不支持人民币，银企直联不支持)
11103 企业定期存款三月
11106 企业定期存款六月
11001 企业定期存款一年
11002 企业定期存款二年(不支持人民币，银企直联不支持)
15201 企业通知存款一天
15207 企业通知存款七天

2.2.响应(regularD2B)

长度

32

15,2

15,2

10

9,7

  标记

说明

<xDataBody>

 <assurAcct>

定期存款账号

 <payerAcctBal> 转出账户余额

 <acctBal>

定期余额

 <startDate>

开户日期

 <fixRate>

利率

</xDataBody>

2.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="regularD2B">

10 / 25

民生银行银企直联

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>fae4576y5</insId>

<payerAcct>0101014830000648</payerAcct>

<deptCode>11001</deptCode>

<payerAmt>11342</payerAmt>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none" trnCode="regularD2B"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2010-01-06 15:58:59</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<assurAcct>0101014280003313</assurAcct>

<payerAcctBal>61314598.64</payerAcctBal>

<acctBal>11311.00</acctBal>

<startDate>20100107</startDate>

<fixRate>2.2500000</fixRate>

11 / 25

</xDataBody>

</CMBC>

民生银行银企直联

3.通知存款通知撤销(DepositNoticeCancel)

本部分更新日期:2021-04-02

只支持对公通知存款账户；

撤销的通知必须存在且状态正常。

3.1.请求(DepositNoticeCancel)

  标记

说明

<xDataBody>

 <trnId>

交易流水号（唯一）（★）

 <insId>

交易流水号（唯一）（★）

 <infoNo>

通知序号（★）

 <certNo>

企业自制凭证号

 <extendData> 备用字段(暂不支持)

</xDataBody>

3.2.响应(DepositNoticeCancel)

长度

64

64

  标记

说明

长度

<xDataBody>

 <trnId>

交易流水号（唯一）（★）

 <insId>

交易流水号（唯一）（★）

64

12 / 25

民生银行银企直联

7

32

3

10

10

 <acNo>

通知存款账号

 <custName>

客户名称

 <currNo>

币种代号(待定)

 <trsAmt>

交易金额

 <availBal>

可用余额

 <infoDate>

通知日期

 <drawDate>

支取日期

 <curTurnFlag> 现转标志

 <cetMonNet>

取款网点

 <infoNo>

柜员流水号

 <extendData> 扩展信息格式采用：key1=v1

key2=

v2

</xDataBody>

3.3.例子

请求

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="DepositNoticeCancel">

<requestHeader>

<dtClient>2012-10-23 18:44:12</dtClient>

<clientId>2200026470</clientId>

<userId>2200026470001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

13 / 25

民生银行银企直联

</requestHeader>

<xDataBody>

<trnId>2018050700001</trnId>

<insId>2018050700001</insId>

<infoNo>00000000000000001948</infoNo>

<certNo>1234</certNo>

</xDataBody>

</CMBC>

响应：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="DepositNoticeCancel" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2018-05-14 10:03:32</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>2018051200002</trnId>

<insId>2018051200002</insId>

<acNo>*********</acNo>

<custName>哈尔滨合聚数字法律担保公司</custName>

<currNo>01</currNo>

<trsAmt>500000.00</trsAmt>

<availBal>500000.00</availBal>

<infoDate>2018-05-15</infoDate>

<drawDate>2018-05-08</drawDate>

<curTurnFlag>1</curTurnFlag>

<cetMonNet></cetMonNet>

<infoNo>00000000000000001948</infoNo>

<tranAcNo></tranAcNo>

<extendData></extendData>

14 / 25

</xDataBody>

</CMBC>

民生银行银企直联

4.新通知存款通知(DepositNoticeNew)

本部分更新日期:2021-04-02

通知存款通知交易是通知银行何时进行通知存款的支取的交易。该交易有以下业务逻

辑：

1、通知存款账号是该客户在做活期转定期通知存款的时候得到的定期账号；

2、通知存款的支取日期必须在通知存款期限之外且在 20 天之内；

3、通知存款支取金额不能低于 100000 元人民币；

4、支取后留存金额不能低于起存金额 500000 元人民币；

5、暂不支持外币。

4.1.请求(DepositNoticeNew)

  标记

说明

<xDataBody>

 <trnId>

交易流水号（唯一）（★）

 <insId>

交易流水号（唯一）（★）

 <acntNo>

通知存款账号

 <acntToNo>

企业收款活期账号

 <amount>

金额（★）

 <drawDate>

通知存款支取日期（★）

 <extendData> 备用字段(暂不支持)

</xDataBody>

长度

64

64

32

32

15,2

8

15 / 25

4.2.响应(DepositNoticeNew)

民生银行银企直联

  标记

说明

长度

<xDataBody>

 <trnId>

交易流水号（唯一）（★）

 <insId>

交易流水号（唯一）（★）

 <acNo>

账号

 <custName>

客户名称

 <currencyNo> 币种代号(待定)

 <trsAmt>

交易金额

 <availBal>

可用余额

 <infoDate>

通知日期

 <drawDate>

支取日期

 <curTurnFlag> 现转标志

 <cetMonNet>

取款网点

 <infoNo>

柜员流水号

64

7

32

3

10

10

 <extendData> 扩展信息格式采用：key1=v1

key2=v2

</xDataBody>

4.3.例子

请求

16 / 25

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="DepositNoticeNew">

<requestHeader>

<dtClient>2018-05-10 18:44:12</dtClient>

民生银行银企直联

<clientId>2200019419</clientId>

<userId>2200019419001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>201805080019</insId>

<acntNo>*********</acntNo>

<acntToNo>*********</acntToNo>

<amount>100000.00</amount>

<drawDate>2018-05-15</drawDate>

</xDataBody>

</CMBC>

响应：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="DepositNoticeNew" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2018-05-14 10:00:58</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<insId>201805080019</insId>

17 / 25

民生银行银企直联

<acNo>*********</acNo>

<custName>票据测试 002</custName>

<currencyNo>01</currencyNo>

<trsAmt>100000.00</trsAmt>

<availBal>900000.00</availBal>

<infoDate>2018-05-11</infoDate>

<drawDate>2018-05-15</drawDate>

<curTurnFlag>2</curTurnFlag>

<cetMonNet>3307</cetMonNet>

<infoNo>00000000000000001964</infoNo>

<extendData></extendData>

</xDataBody>

</CMBC>

5.通知存款通知查询(QueryNotice)

本部分更新日期:2021-04-02

业务逻辑：

查询发起通知存款通知的记录。

5.1.请求(QueryNotice)

  标记

说明

长度

<xDataBody>

 <inqStaNo>

开始条数（★）

 <pageRecNum> 请求条数（★）

</xDataBody>

18 / 25

民生银行银企直联

长度

7

32

3

32

15,2

10

10

5.2.响应(QueryNotice)

  标记

说明

<xDataBody>

 <totalNum>

总条数

 <balList>

列表开始

  <balInfo>

0…n

  <account>

通知存款账号

  <termCode> 通知期限

  <infoNo>

通知序号

  <infoAmt>

通知金额

  <infoDate>

通知日期

  <drawDate> 支取日期

  </balInfo>

 </balList>

列表结束

</xDataBody>

5.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QueryNotice">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

19 / 25

民生银行银企直联

<clientId>**********</clientId>

<userId>************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<inqStaNo>1</inqStaNo>

<pageRecNum>5</pageRecNum>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QueryNotice" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-01-11 14:55:10</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<totalNum>1</totalNum>

<balList>

<balInfo>

<account>********<account>

<termCode>201<termCode>

<infoNo>600221<infoNo>

<infoAmt>5000000.00<infoAmt>

<infoDate>2014-02-09<infoDate>

<drawDate>2014-12-18<drawDate>

20 / 25

民生银行银企直联

</balInfo>

</balList>

</xDataBody>

</CMBC>

6.定期转活期交易(QueryRegularB2D)

本部分更新日期:2022-05-16

业务逻辑：

1.本功能可以将定期存款账户里的定期存款转回活期账户。

2.上送的定期存款账户必须是有效的定期账户

3.类型上，支持普通存款账户、通知存款账户（即未通知支取）

4.币种上，支持人民币账户、外币账户

5.转出的活期账户是自己公司的有效活期账户，币种与定期账户一致

6.支取次数：普通定期存款只能进行一次提前支取，第二次支取为全部支取；通知存款无

限制。

7.如果该笔定期存款已经在柜台打印过凭证，则无法通过此交易转活期

8.可通过 qryFixAcct（总公司定期存款查询交易）和 queryRegularList （定期存款列

表查询）两支交易查询本客户所有定期存款账户。

9.转出后的定期存款的最少留存金额

币种

普通存款 通知存款

人民币 RMB/CNY

10000

500000

港币 HKD

美元 USD

12000

600000

1600

80000

加拿大元 CAD

2000

100000

瑞士法郎 CHF

1410

70500

21 / 25

新加坡元 SGD

2000

100000

日元 JPY

英镑 GBP

欧元 EUR

120000

6000000

1000

50000

1200

60000

澳大利亚元 AUD

2000

100000

6.1.请求(QueryRegularB2D)

  标记

说明

<xDataBody>

 <insId>

交易流水号（唯一）（★）

 <payerAcct> 定期账号（★）

 <payerAmt> 金额（★）

 <payeeAcct> 活期账号（★）

</xDataBody>

6.2.响应(QueryRegularB2D)

  标记

说明

<xDataBody>

 <payerAcctBal> 定期账户余额

</xDataBody>

民生银行银企直联

长度

64

32

15,2

32

长度

15,2

22 / 25

民生银行银企直联

6.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QueryRegularB2D">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>ertewe213e</insId>

<payerAcct>0101014280003321</payerAcct>

<payerAmt>10000</payerAmt>

<payeeAcct>0101014830000648</payeeAcct>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="QueryRegularB2D"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2010-01-06 16:21:27</dtServer>

<userKey>N</userKey>

<dtDead>

23 / 25

民生银行银企直联

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<payerAcctBal>1344.00</payerAcctBal>

</xDataBody>

</CMBC>

24 / 25

