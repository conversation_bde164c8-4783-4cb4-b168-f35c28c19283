银企直联接口文档

（财富管理-银证银期）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

V2.0.0 2024-09-

银转证，证转银，余额查询，交易明细

14

查询接口增加入参 pdGhNo 字段

1 / 35

目录

民生银行银企直联

目录 .............................................................................................2

1. 银证签约信息查询(QRYBANKBONDACCSIGNINFO) ..............................4

1.1. 请求(QRYBANKBONDACCSIGNINFO) ...............................................4

1.2. 响应(QRYBANKBONDACCSIGNINFO) ...............................................4

1.3. 例子 ...................................................................................... 5

2. 银证转账银转证(BANKBONDTRANSB2E) ........................................... 7

2.1. 请求(BANKBONDTRANSB2E) ........................................................7

2.2. 响应(BANKBONDTRANSB2E) ........................................................8

2.3. 例子 ...................................................................................... 8

3. 银证转账证转银(BONDBANKTRANSB2E) ........................................... 9

3.1. 请求(BONDBANKTRANSB2E) ...................................................... 10

3.2. 响应(BONDBANKTRANSB2E) ...................................................... 11

3.3. 例子 .................................................................................... 11

4. 证券资金账户余额查询(QRYFUNDACCBALINFO) .................................12

4.1. 请求(QRYFUNDACCBALINFO) ..................................................... 13

4.2. 响应(QRYFUNDACCBALINFO) ..................................................... 13

4.3. 例子 .................................................................................... 14

5. 证券资金账户交易明细查询(QRYFUNDACCTRANSDETAIL) ....................16

5.1. 请求(QRYFUNDACCTRANSDETAIL) ............................................... 16

5.2. 响应(QRYFUNDACCTRANSDETAIL) ............................................... 17

5.3. 例子 .................................................................................... 18

6. 转账交易状态查询(QRYBANKBONDTRANSSTATE) .........错误！未定义书签。

6.1. 请求(QRYBANKBONDTRANSSTATE) ......................... 错误！未定义书签。

6.2. 响应(QRYBANKBONDTRANSSTATE) ......................... 错误！未定义书签。

2 / 35

民生银行银企直联
6.3. 例子 ................................................................ 错误！未定义书签。

7. 银期签约信息查询(BANKFUTURESSIGNMESSQRY) ............................ 20

7.1. 请求(BANKFUTURESSIGNMESSQRY) .............................................20

7.2. 响应(BANKFUTURESSIGNMESSQRY) .............................................20

7.3. 例子 .................................................................................... 21

8. 银期余额查询(BANKFUTURESBALQRY) ........................................... 23

8.1. 请求(BANKFUTURESBALQRY) ..................................................... 23

8.2. 响应(BANKFUTURESBALQRY) ..................................................... 23

8.3. 例子 .................................................................................... 24

9. 银期明细查询(BANKFUTURESDTLQRY) ........................................... 25

9.1. 请求(BANKFUTURESDTLQRY) ..................................................... 25

9.2. 响应(BANKFUTURESDTLQRY) ..................................................... 26

9.3. 例子 .................................................................................... 27

10. 银转期交易(BANKTOFUTURETRANS) ............................................29

10.1. 请求(BANKTOFUTURETRANS) ................................................... 29

10.2. 响应(BANKTOFUTURETRANS) ................................................... 30

10.3. 例子 ...................................................................................30

11. 期转银交易(FUTURETOBANKTRANS) ............................................31

11.1. 请求(FUTURETOBANKTRANS) ................................................... 32

11.2. 响应(FUTURETOBANKTRANS) ................................................... 32

11.3. 例子 ...................................................................................33

3 / 35

1.银证签约信息查询(QryBankBondAccSignInfo)

民生银行银企直联

本部分更新日期:2021-04-02

证券账户签约信息查询。

1.1.请求(QryBankBondAccSignInfo)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

32

1

8

8

 <acNo>

银行账号

 <busiType>

业务类型 1-三方存管，2-融资融券，3-银衍转账

 <currentIndex> 起始笔数（★）

 <pageSize>

查询笔数（★）

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

1.2.响应(QryBankBondAccSignInfo)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <allNum>

总笔数(★)

8

4 / 35

 <List>

  <Map>

   <acNo>

银行账号

   <acName>

账户名称

民生银行银企直联

32

120

   <busiType>

业务类型 1-三方存管，2-融资融券，3-银衍转账

1

   <dealerName> 券商名称

   <dealerNo>

券商编号

   <fundAccount> 资金账号

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

  <Map>

 <List>

</xDataBody>

1.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryBankBondAccSignInfo">

<requestHeader>

<dtClient>2019-03-04 16:57:15</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

120

8

20

5 / 35

民生银行银企直联

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<acNo>*********</acNo>

<busiType></busiType>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryBankBondAccSignInfo" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 14:34:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<allNum>2</allNum>

<List>

<Map>

<acNo>*********</acNo>

<acName>测十一</acName>

<busiType>1</busiType>

<dealerNo>********</dealerNo>

<dealerName>国信存管</dealerName>

<fundAccount>****************</fundAccount>

</Map>

<Map>

6 / 35

民生银行银企直联

<acNo>*********</acNo>

<acName>测十一</acName>

<busiType>3</busiType>

<dealerNo>********</dealerNo>

<dealerName>方正银衍</dealerName>

<fundAccount>********</fundAccount>

</Map>

</List>

</xDataBody>

</CMBC>

2.银证转账银转证(BankBondTransB2e)

本部分更新日期:2021-04-02

银证转账是指将银行的储蓄系统与证券营业部的股票交易委托系统进行实时联网，股民通
过电话委托或自助交易终端，对证券保证金账户和活期储蓄账户之间的资金进行调拨的一
项金融服务业务。特点是安全快捷、操作简便，无需去柜台。

银转证是由指定的银行账户转款到证券公司账号。

2.1.请求(BankBondTransB2e)

  标记

说明

<xDataBody>

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★） 64

 <acNo>

银行账号(★)

 <dealerNo>

券商编号(★)

 <busiType>

业务类别 (★)1-三方存管，2-融资融券

 <amount>

金额(★)

32

8

1

13.2

7 / 35

<pdGhNo>

证券资金账号

如券商没有支持一卡多签，则非必输。

如券商支持一卡多签，则必输(★)

民生银行银企直联

20

 <extFields1> 备用字段（未启用）

 <extFields2> 备用字段（未启用）

</xDataBody>

2.2.响应(BankBondTransB2e)

  标记

说明

<xDataBody>

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <svrId>

服务器该笔交易的标识（★）(大小额返回，网银互联不返回)

32

 <insId>

指令 ID，请求时给出的 ID（★）

64

</xDataBody>

2.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BankBondTransB2e">

<requestHeader>

<dtClient>2015-05-08 15:18:40</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

8 / 35

民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>20190402wasdf</trnId>

<insId>201xz90sac73sa1000027</insId>

<acNo>*********</acNo>

<dealerNo>********</dealerNo>

<busiType>1</busiType>

<amount>1</amount>

</xDataBody>

</CMBC>

响应报文:

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="BankBondTransB2e" header="100"

lang="chs"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 14:44:44</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>20190402wasdf</trnId>

<svrId>31301201908227963371232313000000</svrId>

<insId>****************</insId>

</xDataBody>

</CMBC>

3.银证转账证转银(BondBankTransB2e)

本部分更新日期:2021-04-02

9 / 35

银证转账是指将银行的储蓄系统与证券营业部的股票交易委托系统进行实时联网，股民通

过电话委托或自助交易终端，对证券保证金账户和活期储蓄账户之间的资金进行调拨的一

民生银行银企直联

项金融服务业务。特点是安全快捷、操作简便，无需去柜台。

证转银是指将证券账户的资金转到银行账户，当天卖出股票所得的资金当天可用不可取，

转出要到第二个交易日。

3.1.请求(BondBankTransB2e)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★）

 <acNo>

银行账号(★)

 <dealerNo>

券商编号(★)

 <busiType>

业务类别 (★)1-三方存管，2-融资融券

 <fundPwd>

资金账号密码密文(★)

 <randNum> 密码密文随机数(★)

 <amount>

金额(★)

<pdGhNo>

证券资金账号

如券商没有支持一卡多签，则非必输。

如券商支持一卡多签，则必输(★)

 <extFields1> 备用字段（未启用）

 <extFields2> 备用字段（未启用）

</xDataBody>

长度

64

64

32

8

1

256

6

13.2

20

10 / 35

3.2.响应(BondBankTransB2e)

  标记

说明

<xDataBody>

民生银行银企直联

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <svrId>

服务器该笔交易的标识（★）(大小额返回，网银互联不返回)

32

 <insId>

指令 ID，请求时给出的 ID（★）

64

</xDataBody>

3.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BondBankTransB2e">

<requestHeader>

<dtClient>2015-05-08 15:18:40</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>20190402wasdf</trnId>

<insId>****************</insId>

<fundPwd>hWNg8IvDOqgYbkds2trLmYXv7HILJkccBXpmJzU82jVTRCfvTo14aw

YSBUHO8jOd9yaXPecdvW+eRYPWJGM0Q9SY8dl8pj7QWUl77GlDK1Bu8MqmS

hLd6vYxmU6PYBm5BeXwlsKd+pMszwhfjXBBV9TctHdgcWX2+okQ4UWAQNo

=</fundPwd>

11 / 35

民生银行银企直联

<randNum>yyNQAN</randNum>

<acNo>*********</acNo>

<dealerNo>********</dealerNo>

<busiType>1</busiType>

<amount>1</amount>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="BondBankTransB2e" header="100"

lang="chs"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 14:44:44</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>20190402wasdf</trnId>

<svrId>31301201908227963377512313000000</svrId>

<insId>****************</insId>

</xDataBody>

</CMBC>

4.证券资金账户余额查询(QryFundAccBalInfo)

本部分更新日期:2021-04-02

证券资金账户余额查询。

含三方存管和融资融券业务账户余额查询，其是指证券公司客户证券交易结算资金交由银

行存管账户。

12 / 35

4.1.请求(QryFundAccBalInfo)

  标记

说明

<xDataBody>

民生银行银企直联

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <acNo>

银行账号(★)

 <dealerNo>

券商编号(★)

 <busiType>

业务类别 (★)1-三方存管，2-融资融券

 <fundPwd>

资金账号密码密文(★)

 <randNum> 密码密文随机数(★)

<pdGhNo>

证券资金账号

如券商没有支持一卡多签，则非必输。

如券商支持一卡多签，则必输(★)

 <extFields1> 备用字段（未启用）

 <extFields2> 备用字段（未启用）

</xDataBody>

4.2.响应(QryFundAccBalInfo)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <acNo>

银行账号

32

8

1

256

6

20

长度

64

32

13 / 35

民生银行银企直联

120

20

1

120

13,2

13,2

 <acName>

账户名称

 <fundAccount> 资金账号

 <busiType>

业务类型 业务类型 1-三方存管，2-银衍转账

 <fundName>

券商名称

 <availBal>

可用余额

 <wdBal>

可取余额

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

4.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryFundAccBalInfo">

<requestHeader>

<dtClient>2019-03-04 16:57:15</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<fundPwd>hWNg8IvDOqgYbkds2trLmYXv7HILJkccBXpmJzU82jVTRCfvTo14aw

YSBUHO8jOd9yaXPecdvW+eRYPWJGM0Q9SY8dl8pj7QWUl77GlDK1Bu8MqmS

14 / 35

hLd6vYxmU6PYBm5BeXwlsKd+pMszwhfjXBBV9TctHdgcWX2+okQ4UWAQNo

民生银行银企直联

=</fundPwd>

<randNum>yyNQAN</randNum>

<acNo>*********</acNo>

<dealerNo>********</dealerNo>

<busiType>1</busiType>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryFundAccBalInfo" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 14:52:39</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<acNo>*********</acNo>

<acName></acName>

<fundAccount>****************</fundAccount>

<busiType>1</busiType>

<fundName>国信存管</fundName>

<availBal>0.00</availBal>

<wdBal>0.00</wdBal>

</xDataBody>

</CMBC>

15 / 35

民生银行银企直联
5.证券资金账户交易明细查询(QryFundAccTransDet

ail)

本部分更新日期:2021-04-02

证券资金账户交易明细查询，查询结果支持下载、打印、发送邮件。

5.1.请求(QryFundAccTransDetail)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <acNo>

银行账号(★)

 <dealerNo>

券商编号(★)

 <busiType>

业务类别 (★)1-三方存管，2-融资融券

 <qryStartDate> 开始日期（含）格式为 YYYY-MM-DD（★）

 <qryEndDate> 截止日期（含）格式为 YYYY-MM-DD（★）

 <currentIndex> 起始笔数（★）

 <pageSize>

查询笔数（★）

<pdGhNo>

证券资金账号

如券商没有支持一卡多签，则非必输。

如券商支持一卡多签，则必输(★)

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

32

8

1

10

10

8

8

20

16 / 35

</xDataBody>

5.2.响应(QryFundAccTransDetail)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <allNum>

总记录数（★）

 <List>

  <Map>

   <fundName>

券商名称

   <fundAccount>

资金账号

   <serialNo>

银行流水号

   <busiTypeName> 交易名称

   <transDate>

交易时间

   <amount>

交易金额

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

  </Map>

 </List>

</xDataBody>

民生银行银企直联

长度

64

8

120

20

32

30

120

13,2

17 / 35

5.3.例子

民生银行银企直联

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryFundAccTransDetail">

<requestHeader>

<dtClient>2015-05-08 15:18:40</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>20190402wasdf</trnId>

<acNo>*********</acNo>

<dealerNo>********</dealerNo>

<busiType>1</busiType>

<qryStartDate>2019-06-29</qryStartDate>

<qryEndDate>2019-08-01</qryEndDate>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

返回报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryFundAccTransDetail" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 14:54:40</dtServer>

18 / 35

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<allNum>28</allNum>

<List>

<Map>

<fundName>国信存管</fundName>

<fundAccount>****************</fundAccount>

<serialNo>26</serialNo>

<busiTypeName>银转证</busiTypeName>

<transDate>2019-07-31</transDate>

<amount>200.00</amount>

</Map>

<Map>

<fundName>国信存管</fundName>

<fundAccount>****************</fundAccount>

<serialNo>27</serialNo>

<busiTypeName>银转证</busiTypeName>

<transDate>2019-07-31</transDate>

<amount>200.00</amount>

</Map>

<Map>

<fundName>国信存管</fundName>

<fundAccount>****************</fundAccount>

<serialNo>33</serialNo>

<busiTypeName>银转证</busiTypeName>

<transDate>2019-07-31</transDate>

<amount>10.00</amount>

</Map>

</List>

</xDataBody>

</CMBC>

19 / 35

6.银期签约信息查询(BankFuturesSignMessQry)

民生银行银企直联

本部分更新日期:2021-04-02

期货所有签约资金账户查询，其签约状态有：正常、挂失、销户、待销户、待换卡、空

户、限制 7 种。

6.1.请求(BankFuturesSignMessQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <extFields1> 备用字段（未启用）

</xDataBody>

6.2.响应(BankFuturesSignMessQry)

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志（★）

 <totalNum>

总记录数（★）

 <extFields1>

备用字段

 <List>

  <Map>

   <totalNum>

客户端交易的唯一标志（★）

长度

64

长

度

64

8

20 / 35

   <status>

签约状态（★） 0:正常 2:挂失 3:销户 6:待销户 7:待

1

民生银行银企直联

换卡 8:空户 9:限制

   <currency>

币种（★）

   <futrueNo>

期货公司编号（★）

   <futrueName> 期货公司名称（★）

   <acNo>

签约账号（★）

   <acName>

签约账户名称（★）

   <bourseAcNo> 期货资金账号（★）

   <extFields2>

备用字段

   <extFields3>

备用字段

  <Map>

 <List>

</xDataBody>

6.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BankFuturesSignMessQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

3

8

60

32

128

20

21 / 35

民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>123456789012345678901234567890</trnId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="BankFuturesSignMessQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-20 15:34:00</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>123456789012345678901234567890</trnId>

<totalNum>1</totalNum>

<extFields1/>

<List>

<Map>

<status>0</status>

<currency>USD</currency>

<futrueNo>********</futrueNo>

<futrueName>招商期货</futrueName>

<acNo>*********</acNo>

<acName>招商期货</acName>

<bourseAcNo>444101</bourseAcNo>

<extFields2/>

<extFields3/>

22 / 35

</Map>

</List>

</xDataBody>

</CMBC>

7.银期余额查询(BankFuturesBalQry)

本部分更新日期:2021-04-02

支持查看期货资金账号余额。

7.1.请求(BankFuturesBalQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <bourseAcNo> 期货资金账号（★）

 <futrueNo>

期货编号（★）

 <transKey>

交易密码（★）

 <randNum>

随机数（★）

 <extFields1>

备用字段（未启用）

</xDataBody>

7.2.响应(BankFuturesBalQry)

  标记

说明

长度

<xDataBody>

民生银行银企直联

长度

64

20

8

256

6

23 / 35

民生银行银企直联

 <trnId>

客户端交易的唯一标志（★） 64

 <balance>

余额（★）

 <bourseAcNo> 期货资金账号（★）

 <extFields2>

备用字段（未启用）

13.2

20

</xDataBody>

7.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BankFuturesBalQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>342423dsxfssssaasad</trnId>

<bourseAcNo>444101</bourseAcNo>

<futrueNo>********</futrueNo>

<transKey>LgWEOsjN8vCkrg9bDyE5No9GFOuEV0rV/U/DdlkDidaybisSpLNhh2

b/4DEzYkQTg+cE/pLMNF1fkbomZBuy+R4f+LWPOfl9racatEkBpNWuKmaRVtb

CtfmJHcdFBJ2ZFE7cbg4h1cAo0Q+oYMswjCsowDWb5LSeG3+LpbIi8KI=</tran

sKey>

<randNum>kdbskc</randNum>

</xDataBody>

</CMBC>

24 / 35

响应报文

<?xml version="1.0" encoding="utf-8"?>

民生银行银企直联

<CMBC trnCode="BankFuturesBalQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-20 16:18:54</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>342423dsxfssssaasad</trnId>

<balance>0.00</balance>

<balance>444101</balance>

<extFields2/>

</xDataBody>

</CMBC>

8.银期明细查询(BankFuturesDtlQry)

本部分更新日期:2021-04-02

期货资金账户明细查询，支持打印、下载和发送邮件。

8.1.请求(BankFuturesDtlQry)

  标记

说明

长度

<xDataBody>

25 / 35

民生银行银企直联

64

20

8

10

10

4

4

 <trnId>

客户端产生的交易唯一标志（★）

 <bourseAcNo> 期货资金账号（★）

 <futrueNo>

期货编号（★）

 <qryStartDate> 查询起始日期（★）格式为 YYYY-MM-DD

 <qryEndDate> 查询结束日期（★）格式为 YYYY-MM-DD

 <pageNo>

页码(★)

 <qryNum>

查询笔数(★)

 <extFields1>

备用字段（未启用）

</xDataBody>

8.2.响应(BankFuturesDtlQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端交易的唯一标志（★）

 <totalCount>

查询时间区间总数（★）

 <List>

  <Map>

   <trsDate>

交易日期（★）

   <acNo>

签约账号（★）

   <bourseName> 期货公司名称（★）

   <bourseAcNo> 期货资金账号（★）

64

8

8

32

60

20

26 / 35

   <currency>

币种（★）

   <amount>

交易金额（★）

   <trsName>

交易名称（★）

民生银行银企直联

3

13.2

60

   <channel>

交易渠道（★）(0=自助 1=电话 4=柜台 7=

1

网银 8=移动交易 G=管理台 S=期货发起)

   <extFields2>

备用字段（未启用）

   <extFields3>

备用字段（未启用）

  <Map>

 <List>

</xDataBody>

8.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BankFuturesDtlQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<bourseAcNo>444101</bourseAcNo>

<futrueNo>********</futrueNo>

<qryStartDate>2019-08-01</qryStartDate>

27 / 35

<qryEndDate>2019-08-13</qryEndDate>

民生银行银企直联

<pageNo>1</pageNo>

<qryNum>1</qryNum>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="BankFuturesDtlQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-20 17:19:42</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<totalCount>65</totalCount>

<extFields1/>

<List>

<Map>

<trsDate>********</trsDate>

<acNo>*********</acNo>

<bourseAcNo>444101</bourseAcNo>

<trsName>银行转期货</trsName>

<currency>USD</currency>

<amount>106.07</amount>

<bourseName>招商期货</bourseName>

<channel>7</channel>

<extFields2/>

<extFields3/>

28 / 35

</Map>

</List>

</xDataBody>

</CMBC>

9.银转期交易(BankToFutureTrans)

本部分更新日期:2021-04-02

为用户提供银行账户向期货保证金账户之间的资金划转。

9.1.请求(BankToFutureTrans)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <insId>

交易流水号（不可重复）（★）

 <acNo>

签约账号（★）

 <bourseAcNo> 期货资金账号（★）

 <futrueNo>

期货编号（★）

 <amount>

交易金额（★）

 <certNo>

凭证号（8 位数字）

 <remark>

备注

 <extFields1>

备用字段（未启用）

</xDataBody>

民生银行银企直联

长度

64

64

32

20

8

13.2

8

50

29 / 35

9.2.响应(BankToFutureTrans)

民生银行银企直联

长度

64

15

13.2

13.2

8

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志（★）

 <jnlNo>

流水号（★）

 <useBal>

卡可用余额（★）

 <bourseBal> 交易所可取余额（★）

 <busiDate>

业务日期 （★）

 <extFields2> 备用字段（未启用）

</xDataBody>

9.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BankToFutureTrans">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>342423dscacsasad</trnId>

<insId>12345678901cas4567890167890</insId>

30 / 35

民生银行银企直联

<acNo>*********</acNo>

<bourseAcNo>444101</bourseAcNo>

<futrueNo>********</futrueNo>

<amount>503</amount>

<certNo>123456</certNo>

<remark>201908031237b2e</remark>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="BankToFutureTrans" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 16:03:39</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<trnId>342423dscacsasad</trnId>

<insId>201908221566461018519</insId>

<jnlNo>443</jnlNo>

<useBal>***********.18</useBal>

<bourseBal>0.00</bourseBal>

<busiDate>********</busiDate>

<extFields2/>

</xDataBody>

</CMBC>

10.期转银交易(FutureToBankTrans)

本部分更新日期:2021-04-02

31 / 35

期转银交易是指期货账户转银行账户。

10.1.请求(FutureToBankTrans)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <insId>

交易流水号（不可重复）（★）

 <acNo>

签约账号（★）

 <bourseAcNo> 期货资金账号（★）

 <futrueNo>

期货编号（★）

 <amount>

交易金额（★）

 <certNo>

凭证号（8 位数字）

 <remark>

客户留言

 <transKey>

交易密码（★）

 <randNum>

随机数（★）

 <extFields1>

备用字段（未启用）

</xDataBody>

10.2.响应(FutureToBankTrans)

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志（★）

民生银行银企直联

长度

64

64

32

20

8

13.2

8

50

256

6

长度

64

32 / 35

民生银行银企直联

15

13.2

13.2

8

 <jnlNo>

流水号（★）

 <useBal>

卡可用余额（★）

 <bourseBal> 交易所可取余额（★）

 <busiDate>

业务日期 （★）

 <extFields2> 备用字段（未启用）

</xDataBody>

10.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="FutureToBankTrans">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<insId>201908cacacssans</insId>

<acNo>*********</acNo>

<bourseAcNo>444101</bourseAcNo>

<futrueNo>********</futrueNo>

<amount>1871.01</amount>

<certNo></certNo>

<remark>201908031237b2e</remark>

<transKey>123aaa</transKey>

<randNum>123aaa</randNum>

33 / 35

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="FutureToBankTrans" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-08-22 16:02:56</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<insId>201908221566461266781</insId>

<jnlNo>444</jnlNo>

<useBal>***********.42</useBal>

<bourseBal>0.00</bourseBal>

<busiDate>********</busiDate>

<extFields2/>

</xDataBody>

</CMBC>

34 / 35

