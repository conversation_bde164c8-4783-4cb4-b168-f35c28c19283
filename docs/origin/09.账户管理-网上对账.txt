银企直连接口文档

（账户管理-网上对账）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-18 定义接口文档

V2.0.0 2025-04-11 网上对账开通信息维护

(DebtMaintainB2e)接口增加说明：

如需成员单位授权总部集中对账，需

单独开通"集团客户集中对账"业务。

1 / 32

目录

民生银行银企直联

目录 .............................................................................................2

１ 网上对账开通信息维护(DEBTMAINTAINB2E) ........................................ 4

1.1. 请求(DEBTMAINTAINB2E) ........................................................... 4

1.2. 响应(DEBTMAINTAINB2E) ........................................................... 5

1.3. 报文示例 ................................................................................. 5

２ 明细对账单查询(QRYDEBTDETIALLIST) ..............................................7

2.1. 请求(QRYDEBTDETIALLIST) ......................................................... 7

2.2. 响应(QRYDEBTDETIALLIST) ......................................................... 8

2.3. 报文示例 ................................................................................. 9

３ 已填写调节表查询(QRYDEBTRECHKDETAIL) ..................................... 11

3.1. 请求(QRYDEBTRECHKDETAIL) .................................................... 11

3.2. 响应(QRYDEBTRECHKDETAIL) .................................................... 12

3.3. 报文示例 ............................................................................... 14

４ 对账结果查询(QRYDEBTRESULTLIST) ..............................................16

4.1. 请求(QRYDEBTRESULTLIST) ....................................................... 16

4.2. 响应(QRYDEBTRESULTLIST) ....................................................... 18

4.3. 报文示例 ............................................................................... 19

５ 可填写对账单回执列表(QRYVERIFYACCRECLIST) ................................21

5.1. 请求(QRYVERIFYACCRECLIST) .................................................... 21

5.2. 响应(QRYVERIFYACCRECLIST) .................................................... 22

5.3. 报文示例： ............................................................................ 23

６ 对账日期查询(QRYVERIFYACCRECORDDATE) .................................. 25

6.1. 请求(QRYVERIFYACCRECORDDATE) ............................................. 25

6.2. 响应(QRYVERIFYACCRECORDDATE) ............................................. 26

2 / 32

民生银行银企直联
6.3. 报文示例 ............................................................................... 27

７ 对账单回执确认(VERIFYACCRECCONF) ............................................ 28

7.1. 请求(VERIFYACCRECCONF) ........................................................ 28

7.2. 响应(VERIFYACCRECCONF) ........................................................ 30

7.3. 报文示例 ............................................................................... 30

3 / 32

１ 网上对账开通信息维护(DebtMaintainB2e)

民生银行银企直联

本部分更新日期:2025-04-11

业务逻辑：

1、开通、关闭网上对账功能；

2、维护本公司网上对账的联系人姓名、公司电话、联系人手机号。

3、如需成员单位授权总部集中对账，需单独开通"集团客户集中对账"业务。

1.1. 请求(DebtMaintainB2e)

标记

说明

长度

描述

<xDataBody>

标记为★的为必填元素

<trnId>

客户端产生的交易唯一标志

64

(★)

<insId>

指令 ID，（★）

64

一条转账指令在客户端的唯

一标识

0：开通，

1：修改，

2：关闭

<contactName> 联系人名称（★）

<companyTel> 公司电话（★）

<phone>

联系人手机

<operateType> 操作类型（★）

42

20

11

1

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

<extFields3>

备用字段（未启用）

4 / 32

 
 
 
 
 
 
 
 
 
标记

说明

长度

描述

民生银行银企直联

</xDataBody>

1.2. 响应(DebtMaintainB2e)

标记

说明

描述

长

度

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标志

64

原值返回

（★）

<svrId>

交易流水号

32

只有跨行交易的大小额汇路返回，每

笔交易的该流水号唯一

<insId>

指令 ID，请求时给出

64

原值返回

的 ID（★）

<extFields1> 备用字段（未启用）

<extFields2> 备用字段（未启用）

<extFields3> 备用字段（未启用）

</xDataBody>

1.3. 报文示例

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="DebtMaintainB2e">

<requestHeader>

<dtClient>2019-03-04 16:57:15</dtClient>

5 / 32

 
 
 
 
 
 
民生银行银企直联

<clientId>2200015554</clientId>

<userId>2200015554003</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<insId>2020033000017</insId>

<contactName>123</contactName>

<companyTel>5453948</companyTel>

<phone>18310719524</phone>

<operateType>1</operateType>

<customNo></customNo>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="DebtMaintainB2e" header="100"

lang="chs"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-03-30 10:55:50</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<svrId>31301202003300965514808313000000</svrId>

<insId>2020033000018</insId>

6 / 32

</xDataBody>

</CMBC>

２ 明细对账单查询(QryDebtDetialList)

民生银行银企直联

本部分更新日期:2021-04-02

业务逻辑：

查询一段时间内本公司明细对账信息或查询一段时间内所填写的成员公司明细对账信息
(成员公司与本公司必须有签约关系)。 （即交易明细查询）

2.1. 请求(QryDebtDetialList)

标记

说明

长度

描述

<xDataBody>

标记为★的为必填元素

<trnId>

客户端产生的交易唯一标

64

志(★)

<acNo>

银行账号（★）

42

<qryStartDate> 开始日期（含）（★）

<qryEndDate>

截止日期（含）（★）

<currentIndex> 起始笔数（★）

<pageSize>

查询笔数（★）

<customNo>

成员客户号

6

6

8

8

yyyy-MM-dd

yyyy-MM-dd

此字段为空查询本公司明

细对账信息; 如果不为空,

则查询所填写的成员公司

明细对账信息;(成员公司与

7 / 32

      
 
 
 
 
 
 
 
标记

说明

长度

描述

民生银行银企直联

本公司必须有签约关系)

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

<extFields3>

备用字段（未启用）

</xDataBody>

2.2. 响应(QryDebtDetialList)

标记

说明

长度

描述

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标志（★）

64

<allNum>

总记录数（★）

8

<List>

<Map>

<transDate>

交易日期

<transAmt>

交易金额

<loanFlag>

借贷标志

<balance>

余额

<payeeAcNo>

对手方账号

10

10

1

13,2

32

0：借，

1：贷

8 / 32

      
 
 
 
 
 
 
  
   
   
   
   
   
标记

说明

长度

描述

民生银行银企直联

200

200

200

<payeeName>

对手方名称

<payeeOpBank> 对手方开户信息

<remark>

摘要

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

<extFields3>

备用字段（未启用）

</Map>

</List>

</xDataBody>

2.3. 报文示例

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryDebtDetialList">

<requestHeader>

<dtClient>2019-03-04 16:57:15</dtClient>

<clientId>**********</clientId>

<userId>**********851</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<acNo>*********</acNo>

9 / 32

   
   
   
   
   
   
  
 
民生银行银企直联

<customNo></customNo>

<qryStartDate>2019-09-17</qryStartDate>

<qryEndDate>2019-12-17</qryEndDate>

<currentIndex>1</currentIndex>

<pageSize>1</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryDebtDetialList" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-03-30 11:49:57</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<allNum>20</allNum>

<List>

<Map>

<transDate>2019-11-19</transDate>

<transAmt>50944.58</transAmt>

<loanFlag>1</loanFlag>

<balance>2353532.58</balance>

<payeeAcNo>*********</payeeAcNo>

<payeeName>西昌澄海鹏远汽车休闲娱乐公司</payeeName>

<payeeOpBank>中国民生银行长春民丰大街支行</payeeOpBank>

<remark>水电费</remark>

</Map>

</List>

10 / 32

</xDataBody>

</CMBC>

民生银行银企直联

３ 已填写调节表查询(QryDebtReChkDetail)

本部分更新日期:2021-04-02

业务逻辑：

对账不符的情况下，公司需要填写银企对账未达账项调节表。

因此，本交易用于查询本公司已填写调节表信息或所填写的成员公司已填写调节表信息
(成员公司与本公司必须有签约关系)。

3.1. 请求(QryDebtReChkDetail)

标记

说明

长度

描述

<xDataBody>

标记为★的为

必填元素

<trnId>

客户端产生的

64

交易唯一标志

(★)

<customNo>

成员客户号

此字段为空查询本公司已填写调节表

信息; 如果不为空,则查询所填写的成

员公司已填写调节表信息;(成员公司

与本公司必须有签约关系)

<acNo>

银行账号

32

（★）

11 / 32

 
      
 
 
 
标记

说明

长度

描述

民生银行银企直联

<checkDate>

对账日期

6

yyyyMM

（含）（★）

<extFields1>

备用字段（未

启用）

<extFields2>

备用字段（未

启用）

<extFields3>

备用字段（未

启用）

</xDataBody>

3.2. 响应(QryDebtReChkDetail)

标记

说明

长度

描述

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标志（★） 64

<acName>

账号名称

<acNo>

银行账号

<deptName>

开户机构

<currency>

币种

<bankDepositBal>

银行存款余额(1)

<compCreditTtl>

单位借方发生额合计(3)

200

32

200

20

13,2

13,2

12 / 32

 
      
 
 
 
 
 
 
 
 
 
 
 
标记

说明

长度

描述

民生银行银企直联

<compDebitTtl>

单位贷方发生额合计(4)

13,2

<compReBal>

单位调解后余额(1)+(3)-(4)

13,2

<CompList>

单位已记账，银行未记账

<Map>

<compDate>

交易日期

10

YYYY-MM-

DD

13,2

13,2

200

<compCreditAmt> 借方发生额

<compDebitAmt>

贷方发生额

<compremark>

备注

</Map>

</CompList>

<compBankDepositBal> 单位银行存款余额(2)

13,2

<bankCreditTtl>

单位银行借方发生额合计(5)

13,2

<bankDebitTtl>

单位银行贷方发生额合计(6)

13,2

<bankReBal>

单位银行调解后余额(2)-(5)+(6)

13,2

<BankList>

银行已记账，单位未记账

<Map>

<bankDate>

交易日期

10

YYYY-MM-

<bankCreditAmt> 借方发生额

13,2

DD

13 / 32

 
 
 
  
   
   
   
   
  
 
 
 
 
 
 
  
   
   
标记

说明

长度

描述

民生银行银企直联

<bankDebitAmt>

贷方发生额

<bankpremark>

备注

13,2

200

</Map>

</BankList>

<isAccord>

调节后结果

1

0:相符 1：不

相符

<diffBal>

不相符相差金额

<reasonType>

不相符原因

13,2

200

</xDataBody>

3.3. 报文示例

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryDebtReChkDetail">

<requestHeader>

<dtClient>2019-03-04 16:57:15</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<acNo>*********</acNo>

<customNo></customNo>

14 / 32

   
   
  
 
 
 
 
<checkDate>202001</checkDate>

民生银行银企直联

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryDebtReChkDetail" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-03-30 19:54:18</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<acName>测试咋好难过户</acName>

<acNo>*********-999999</acNo>

<deptName>中国民生银行股份有限公司广州天河支行</deptName>

<currency>人民币</currency>

<bankDepositBal>368.64</bankDepositBal>

<compCreditTtl>12.00</compCreditTtl>

<compDebitTtl>0.00</compDebitTtl>

<compReBal>380.64</compReBal>

<CompList>

<Map>

<compDate>2018-10-12</compDate>

<compCreditAmt>12.00</compCreditAmt>

<compDebitAmt></compDebitAmt>

<compremark></compremark>

</Map>

</CompList>

<compBankDepositBal>368.64</compBankDepositBal>

15 / 32

民生银行银企直联

<bankCreditTtl>0.00</bankCreditTtl>

<bankDebitTtl>12.00</bankDebitTtl>

<bankReBal>380.64</bankReBal>

<BankList>

<Map>

<bankDate>2018-10-12</bankDate>

<bankCreditAmt></bankCreditAmt>

<bankDebitAmt>12.00</bankDebitAmt>

<bankpremark></bankpremark>

</Map>

</BankList>

<isAccord>0</isAccord>

<diffBal></diffBal>

<reasonType></reasonType>

</xDataBody>

</CMBC>

４ 对账结果查询(QryDebtResultList)

本部分更新日期:2021-04-02

业务逻辑：

查询本公司的对账结果信息或查询所填写的成员公司对账结果信息;(成员公司与本公司必
须有签约关系)。

4.1. 请求(QryDebtResultList)

标记

说明

描述

长

度

<xDataBody>

<trnId>

客户端产生的交易

64

唯一标志（必输，

但无作用）(★)

16 / 32

 
标记

说明

描述

长

度

民生银行银企直联

<customNo>

成员客户号

此字段为空查询本公司对账结果信息; 如

果不为空,则查询所填写的成员公司对账

结果信息;(成员公司与本公司必须有签约

关系)

<qryType>

查询类型 (★)

1

0:按对账期数查询， 1：按对账账号查询

<acNo>

银行账号

32 按对账账号查询时输入且必输

yyyyMM

yyyyMM 按对账账号查询时输入且必输

6

6

8

8

<qryStartDate> 开始日期(对账周

期)（含）（★）

<qryEndDate> 截止日期（含）

<currentIndex> 起始笔数（★）

<pageSize>

查询笔数（★）

<extFields1>

备用字段（未启

用）

<extFields2>

备用字段（未启

用）

<extFields3>

备用字段（未启

用）

</xDataBody>

17 / 32

 
 
 
 
 
 
 
 
 
 
4.2. 响应(QryDebtResultList)

民生银行银企直联

标记

说明

描述

长

度

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标

64

志（★）

<allNum>

总记录数（★）

8

<List>

<Map>

<acNo>

银行账户

<currency>

币种

<acType>

账号性质

32

10

32

<balance>

余额

13,2

<status>

对账单状态

1

0：未填写 1：已填写等待审核

2：已提交银行 3：驳回

<authDate>

对账日期

<checkDate>

对账周期

<checkResult> 对账结果

<reasonType> 不相符原因

<isAccord>

调节后结果

10

10

1

1

1

0:相符 1：不相符

1:未达账项 2:银行错误 3:单位错误

4:未知原因

0:相符 1：不相符

18 / 32

 
 
 
  
   
   
   
   
   
   
   
   
   
   
标记

说明

描述

长

度

民生银行银企直联

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

<extFields3>

备用字段（未启用）

</Map>

</List>

</xDataBody>

4.3. 报文示例

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryDebtResultList">

<requestHeader>

<dtClient>2019-03-04 16:57:15</dtClient>

<clientId>2200034070</clientId>

<userId>2200034070306</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>32332ds43asd72</trnId>

<qryType>0</qryType>

<acNo></acNo>

<customNo></customNo>

<qryStartDate>201903</qryStartDate>

<qryEndDate></qryEndDate>

19 / 32

   
   
   
  
 
民生银行银企直联

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryDebtResultList" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-03-30 17:14:43</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<allNum>1</allNum>

<List>

<Map>

<acNo>*********</acNo>

<currency>人民币</currency>

<acType>活期</acType>

<balance>4.01</balance>

<status>2</status>

<authDate>2014-12-31</authDate>

<checkDate>201903</checkDate>

<checkResult>0</checkResult>

<isAccord></isAccord>

<tableFlag>0</tableFlag>

</Map>

</List>

</xDataBody>

</CMBC>

20 / 32

５ 可填写对账单回执列表(QryVerifyAccRecList)

民生银行银企直联

本部分更新日期:2021-04-02

业务逻辑：

1、一次最多只能查询 100 条数据；

2、查询本公司待填写的对账回执列表信息;或查询所填写的成员公司待填写的对账回执列
表信息;(成员公司与本公司必须有签约关系)。

5.1. 请求(QryVerifyAccRecList)

标记

说明

描述

长

度

<xDataBody>

标记为★的为

必填元素

<trnId>

客户端产生的

64 必输，原值返回，数字字母组成；可做为客

交易唯一标志

户端自己的交易标识

4

2

只能查询两年之内的对账日期

MM 格式

（★）

<qryYear>

查询年份

（★）

<qryMonth>

查询月份

（★）

<startNo>

起始笔数

（★）

<queryRows> 查询笔数

（★）

<customNo>

成员客户号

此字段为空查询本公司对账回执列表信息; 如

果不为空,则查询所填写的成员公司对账回执

21 / 32

 
 
 
 
 
 
 
标记

说明

描述

长

度

民生银行银企直联

列表信息; (成员公司与本公司必须有签约关

系)

<extendReq1> 备用字段 1

备用字段预留，暂无用

<extendReq2> 备用字段 2

<extendReq3> 备用字段 3

<Youy>

</xDataBody>

5.2. 响应(QryVerifyAccRecList)

标记

说明

描述

长

度

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标

64

原值返回

志（★）

<allNum>

描述总笔数

<List>

<CheckSeq>

对账单顺序号（★） 14

<Currency>

币种（★）

<BankAcType> 账户性质

<AcNo>

账号

3

8

32

22 / 32

 
 
 
 
 
 
 
 
  
  
  
  
民生银行银企直联

标记

说明

<Balance>

余额

<DeptName>

开户银行

描述

长

度

15.2

128

<OpenDate>

开户日期

10

格式：yyyy-MM-dd

<status>

对账单状态

1

0：未填写 1：已填写等待复核 2：

已提交银行 3：驳回

<extendRep1> 备用字段 1

备用字段预留，暂不返回

<extendRep2> 备用字段 2

</List>

<extendRep3>

备用字段 1

<extendRep4>

备用字段 2

<extendRep5>

备用字段 3

</xDataBody>

5.3. 报文示例：

请求报文：

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryVerifyAccRecList">

<requestHeader>

<clientId>**********</clientId>

<userId>**********882</userId>

<userPswd>111111</userPswd>

<language>chs</language>

23 / 32

  
  
  
  
  
  
 
 
 
 
民生银行银企直联

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<customNo></customNo>

<qryYear>2019</qryYear>

<qryMonth>11</qryMonth>

<startNo>1</startNo>

<queryRows>3</queryRows>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="QryVerifyAccRecList" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-04-10 11:23:37</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<allNum>1</allNum>

<List>

<Map>

<OpenDate>2012-09-06</OpenDate>

<Currency>RMB</Currency>

<AcNo>*********</AcNo>

<BankAcType>活期</BankAcType>

24 / 32

<Balance>8810.00</Balance>

<DeptName>中国民生银行北京广安门支行</DeptName>

<CheckSeq>***************</CheckSeq>

民生银行银企直联

<status>3</status>

</Map>

</List>

</xDataBody>

</CMBC>

６ 对账日期查询(QryVerifyAccRecordDate)

本部分更新日期:2021-04-02

业务逻辑：

查询本公司待对账日期信息或查询所填写的成员公司待对账日期信息;(成员公司与本公司
必须有签约关系)。

这样客户能够通过年份查询到对应月份的待对账记录。

6.1. 请求(QryVerifyAccRecordDate)

标记

说明

长度

描述

<xDataBody>

标记为★的为

必填元素

<trnId>

客户端产生的

64

必输，原值返回，数字字母组成；可做

交易唯一标志

为客户端自己的交易标识

（★）

<qryYear>

查询年份

4

只能查询两年之内的对账日期

（★）

<customNo>

成员客户号

此字段为空查询本公司对账日期信息; 如

果不为空,则查询所填写的成员公司对账

25 / 32

 
 
 
标记

说明

长度

描述

民生银行银企直联

日期信息;(成员公司与本公司必须有签约

关系)

<extendReq1> 备用字段 1

备用字段预留，暂无用

<extendReq2> 备用字段 2

<extendReq3> 备用字段 3

</xDataBody>

6.2. 响应(QryVerifyAccRecordDate)

标记

说明

长度 描述

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标志（★） 64

原值返回

<beQryYear>

查询年份（★）

<List>

<beQryMonth> 可查月份（★）

4

2

YYYY 格式

MM 格式

<extendRep1> 备用字段 1

备用字段预留，暂不返回

<extendRep2> 备用字段 2

</List>

<extendRep3>

备用字段 1

<extendRep4>

备用字段 2

<extendRep5>

备用字段 3

26 / 32

 
 
 
 
 
 
  
  
  
 
 
 
 
标记

说明

长度 描述

民生银行银企直联

</xDataBody>

6.3. 报文示例

请求报文：

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryVerifyAccRecordDate">

<requestHeader>

<clientId>**********</clientId>

<userId>**********882</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<qryYear>2020</qryYear>

<customNo></customNo>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="QryVerifyAccRecordDate" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-04-10 11:08:00</dtServer>

27 / 32

民生银行银企直联

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<beQryYear>2020</beQryYear>

<List>

<beQryMonth>01</beQryMonth>

</List>

</xDataBody>

</CMBC>

７ 对账单回执确认(VerifyAccRecConf)

本部分更新日期:2021-04-02

业务逻辑：

本公司对账回执信息确认;或所填写的成员公司对账回执信息确认;(成员公司与本公司必须
有签约关系)。

目前银企直联只支持相符的对账单确认，如果不相符，需登录网银进行操作。

7.1. 请求(VerifyAccRecConf)

标记

说明

描述

长

度

<xDataBody>

标记为★的为必填元

素

<trnId>

客户端产生的交易唯

64 必输，原值返回，数字字母组成；可

一标志（★）

做为客户端自己的交易标识

<insId>

指令 ID（★）

64 必输，原值返回，数字字母组成；做

为服务端交易唯一标识

28 / 32

 
 
标记

说明

描述

长

度

<confDate>

对账周期（★）

6

yyyyMM 格式

民生银行银企直联

<customNo>

成员客户号

此字段为空操作本公司对账回执信息

确认; 如果不为空,则操作所填写的成员

公司对账回执信息确认;(成员公司与本

公司必须有签约关系)

<confContent> 审核数据 竖线“|”分

割数据元素，以尖号

“^”为数据行分割符

（★）

<extendReq1> 备用字段 1

备用字段预留，暂无用

<extendReq2> 备用字段 2

<extendReq3> 备用字段 3

</xDataBody>

审核数据<confContent>字段说明：

域名称

说明

长

度

<对账结果

0 ：相符 目前银企直联只支持相符的对账单确认，如果不

1

（★）>

相符，需登录网银进行操作

<对账单顺序号

可填写对账单回执列表接口查询回的对账单顺序号

（★）>

<账号（★）>

可填写对账单回执列表接口查询回的对账单顺序

14

32

示例：

<confContent>0|***************|*********^0|201966666666692|*********</confContent>

29 / 32

 
 
 
 
 
 
 
 
 
民生银行银企直联

7.2. 响应(VerifyAccRecConf)

标记

说明

描述

长

度

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标

64

原值返回

志（★）

<insId>

指令 ID（★）

64

必输，原值返回，数字字母组成；做为

服务端交易唯一标识

<extendRep1> 备用字段 1

备用字段预留，暂不返回

<extendRep2> 备用字段 2

<extendRep3> 备用字段 3

</xDataBody>

7.3. 报文示例

请求报文：

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="VerifyAccRecConf">

<requestHeader>

<clientId>**********</clientId>

<userId>**********882</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

30 / 32

 
 
 
 
 
民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<insId>cascascasssaaaa</insId>

<confDate>201911</confDate>

<customNo></customNo>

<confContent>0|***************|*********</confContent>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="VerifyAccRecConf" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-04-10 11:28:28</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<insId>cascascasssaaaa</insId>

</xDataBody>

</CMBC>

31 / 32

