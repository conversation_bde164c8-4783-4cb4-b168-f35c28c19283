银企直联接口文档

（现金管理-募管通）

邮箱：<EMAIL>

民生银行银企直联

文档修改记录

版本

日期

说明

编写者 审核者

V1.0.0 2021-03-18

定义接口文档

1 / 41

目录

民生银行银企直联

目录 .............................................................................................2

１ 募管通产品账号管理(PRDACCTMNGT) ................................................ 4

1.1. 请求(PRDACCTMNGT) .................................................................4

1.2. 响应(PRDACCTMNGT) .................................................................5

1.3. 例子 ...................................................................................... 5

２ 募管通产品账号管理查询(PRDACCTMNGTQRY) ..................................... 7

2.1. 请求(PRDACCTMNGTQRY) ........................................................... 7

2.2. 响应(PRDACCTMNGTQRY) ........................................................... 8

2.3. 例子 ...................................................................................... 9

３ 募管通产品账号查询(PRDACCTQRY) ................................................. 10

3.1. 请求(PRDACCTQRY) ................................................................. 10

3.2. 响应(PRDACCTQRY) ................................................................. 11

3.3. 例子 .................................................................................... 12

４ 募管通白名单查询(PRDWHITEACCTQRY) .......................................... 13

4.1. 请求(PRDWHITEACCTQRY) .........................................................14

4.2. 响应(PRDWHITEACCTQRY) .........................................................14

4.3. 例子 .................................................................................... 15

５ 募管通产品账号委托人清单查询(PRDFINCDETAILQRY) .......................... 17

5.1. 请求(PRDFINCDETAILQRY) .........................................................17

5.2. 响应(PRDFINCDETAILQRY) .........................................................17

5.3. 例子 .................................................................................... 18

６ 募管通产品账号对委托人转账(PRDFINCTRANS) ....................................20

6.1. 请求(PRDFINCTRANS) ...............................................................20

6.2. 响应(PRDFINCTRANS) ...............................................................21

2 / 41

民生银行银企直联
6.3. 例子 .................................................................................... 22

７ 募管通产品账号明细查询(PRDTRANSDETAILQRY) ............................... 23

7.1. 请求(PRDTRANSDETAILQRY) ...................................................... 23

7.2. 响应(PRDTRANSDETAILQRY) ...................................................... 24

7.3. 例子 .................................................................................... 25

８ 募管通批量产品账号明细查询(PRDBATCHTRANSDETAILQRY) ................27

8.1. 请求(PRDBATCHTRANSDETAILQRY) .............................................. 27

8.2. 响应(PRDBATCHTRANSDETAILQRY) .............................................. 28

8.3. 例子 .................................................................................... 29

９ 募管通产品账号对外转账(PRDXFER) .................................................. 31

9.1. 请求(PRDXFER) ...................................................................... 31

9.2. 响应(PRDXFER) ...................................................................... 33

9.3. 例子 .................................................................................... 33

１０ 单笔转账交易结果查询(QRYXFER) .................................................. 35

10.1. 请求(QRYXFER) .....................................................................35

10.2. 响应(QRYXFER) .....................................................................35

10.3. 例子 ...................................................................................37

3 / 41

1.募管通产品账号管理(prdAcctMngt)

民生银行银企直联

本部分更新日期:2021-04-02

募管通产品账号管理交易,管理一个实体账号的产品子账号，包括产品子账号的开立、

修改。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限；

1.1.请求(prdAcctMngt)

  标记

说明

<xDataBody>

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

客户端产生的交易唯一标志（必输，但无作用）(★)

 <operFlag>

操作标识 0—新增，2—修改（★）

 <acNo>

实体账号（★）

 <prdAcNo>

产品账号（★）

 <prdAcName> 产品账户名称（★）

64

1

32

6

60

 <acState>

产品账户状态：0 - 正常，1 - 关闭(operFlag=2 时有效

1

且 operFlag=2 时必输)

 <inherit>

是否遵从实账户计息:0 - 否，1 - 是（★）

 <accrualFlag> 存款计息标识：0-不计息 1-计息(inherit=0 时有效且

inherit=0 必输)

1

1

4 / 41

 <crRateType> 存款计息方式：0-固定利率(accrualFlag=0 时必输)

1

民生银行银企直联

 <crRate>

存款固定利率：crRateType=0 时有效，存款固定利率

大于 0 小于 1(最多支持小数点后 4 位)

 <mngrName> 管理员姓名（★）

 <mngrId>

管理人证件（★）

 <corpName>

法人姓名（★）

 <actDate>

备用字段(暂不支持)

</xDataBody>

1.2.响应(prdAcctMngt)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

 <svrId>

服务器该笔交易的标识（★）

 <insId>

客户端产生的交易标志，原值返回

</xDataBody>

1.3.例子

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdAcctMngt">

<requestHeader>

长度

32

5 / 41

<dtClient>2010-03-13 17:44:12</dtClient>

民生银行银企直联

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>2018011000011</trnId>

<insId>2018011000011</insId>

<operFlag>2</operFlag>

<acNo>*********</acNo>

<prdAcNo>000222</prdAcNo>

<prdAcName>募管通优化测试</prdAcName>

<mngrName>募管通优化测试</mngrName>

<mngrId>11111111111111</mngrId>

<corpName>募管通优化测试</corpName>

<acState>0</acState>

<inherit>0</inherit>

<accrualFlag>1</accrualFlag>

<crRateType>0</crRateType>

<crRate>0.0131</crRate>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdAcctMngt" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2018-01-10 17:15:25</dtServer>

<userKey>N</userKey>

6 / 41

民生银行银企直联

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>2018011000011</trnId>

<svrId></svrId>

<insId>2018011000011</insId>

</xDataBody>

</CMBC>

2.募管通产品账号管理查询(prdAcctMngtQry)

本部分更新日期:2021-04-02

募管通产品账号管理查询交易,查询一个实体账号下产品账号的管理信息。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银；

2：操作员必须对该产品账号具备查询权限。

2.1.请求(prdAcctMngtQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <acNo>

实体账号(★)

 <prdAcNo>

产品账号(★)

 <actDate>

备用字段(暂不支持)

32

6

备用字段

(暂不支持)

7 / 41

</xDataBody>

2.2.响应(prdAcctMngtQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

 <prdAcNo>

产品账号

 <prdAcName> 产品账号名称

 <selfBal>

自身余额

 <acNo>

清算账号

 <acName>

清算账号户名

 <mngrName> 管理员姓名

 <mngrId>

管理人证件

 <corpName>

法人姓名

 <acState>

产品账户状态：0 - 正常，1 - 关闭

 <inherit>

是否遵从实账户计息:0 - 否，1 - 是

 <accrualFlag> 存款计息标识：0-不计息 1-计息

 <crRateType> 存款计息方式：0-固定利率

 <crRate>

存款固定利率；

</xDataBody>

民生银行银企直联

长度

64

6

60

15,2

1

1

1

8 / 41

民生银行银企直联

2.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdAcctMngtQry">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>20170811000001</trnId>

<acNo>*********</acNo>

<prdAcNo>000222</prdAcNo>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdAcctMngtQry" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2018-01-10 17:01:30</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

9 / 41

民生银行银企直联

</responseHeader>

<xDataBody>

<trnId>20170811000001</trnId>

<prdAcNo>000222</prdAcNo>

<prdAcName>募管通优化测试</prdAcName>

<selfBal>0.00</selfBal>

<acNo>*********</acNo>

<acName>上存下拨分开 45</acName>

<mngrName>募管通优化测试</mngrName>

<mngrId>11111111111111</mngrId>

<corpName>募管通优化测试</corpName>

<acState>0</acState>

<accrualFlag>0</accrualFlag>

<crRateType>0</crRateType>

<crRate>0.00000</crRate>

</xDataBody>

</CMBC>

3.募管通产品账号查询(prdAcctQry)

本部分更新日期:2021-04-02

募管通产品账号管理查询交易,查询一个实体账号下产品账号信息。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银；

2：操作员必须对该产品账号具备查询权限。

3.1.请求(prdAcctQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

64

10 / 41

民生银行银企直联

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <acNo>

实体账号(★)

32

 <prdAcNo>

产品账号(未输入时，返回实体账号下的所有产品账号)

6

 <startDate>

开户起始日期(yyyy-MM-dd)

 <endDate>

开户截至日期(yyyy-MM-dd)

 <currentIndex> 起始位置（★）

 <pageSize>

查询笔数（★）(查询笔数不能超过 500)

10

10

 <actDate>

备用字段(暂不支持)

</xDataBody>

3.2.响应(prdAcctQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <recordNum>

总数

 <List>

  <Map>

   <prdAcNo>

产品账号

   <prdAcName> 产品账号名称

   <selfBal>

产品账号余额

6

60

15，2

11 / 41

民生银行银企直联

15，2

15，2

   <raiseBal>

募集总金额

   <redeemBal> 赎回总金额

   <openDate>

开户日

  </Map>

 </List>

</xDataBody>

3.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdAcctQry">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>20170811000001</trnId>

<acNo>*********</acNo>

<prdAcNo>000014</prdAcNo>

<startDate>2017-07-21</startDate>

<endDate>2017-08-23</endDate>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

12 / 41

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdAcctQry" security="none" lang="chs" header="100"

民生银行银企直联

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-08-29 16:34:04</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>20170811000001</trnId>

<recordNum>1</recordNum>

<List>

<Map>

<prdAcNo>000014</prdAcNo>

<prdAcName>白名单测试</prdAcName>

<selfBal>0.00</selfBal>

<raiseBal>0.00</raiseBal>

<redeemBal>0.00</redeemBal>

<openDate>20170823</openDate>

</Map>

</List>

</xDataBody>

</CMBC>

4.募管通白名单查询(prdWhiteAcctQry)

本部分更新日期:2021-04-02

13 / 41

4.1.请求(prdWhiteAcctQry)

民生银行银企直联

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

 <acNo>

实体账号（★）

 <prdAcNo>

产品账号（★）

 <actDate>

备用字段(暂不支持)

</xDataBody>

4.2.响应(prdWhiteAcctQry)

长度

64

32

10

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <List>

  <Map>

   <acNoFlag>

账户标识：0-有效，1-无效

   <prdAcNo>

产品账号

   <itemNo>

项目号

   <oppAcNo>

对方账号

   <oppAcName> 对方账号名称

1

6

14 / 41

   <payFalg>

收付款标识：0-收款，1 付款

2

民生银行银企直联

   <openDate>

开通日期

  </Map>

 </List>

</xDataBody>

4.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdWhiteAcctQry">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>20170811000001</trnId>

<acNo>*********</acNo>

<prdAcNo>147258</prdAcNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdWhiteAcctQry" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

15 / 41

民生银行银企直联

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-08-29 16:35:18</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>20170811000001</trnId>

<List>

<Map>

<acNoFlag>0</acNoFlag>

<prdAcNo>147258</prdAcNo>

<itemNo>5.000000</itemNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>上存下拨分开 44</oppAcName>

<payFalg>01</payFalg>

<openDate>20161108</openDate>

</Map>

<Map>

<acNoFlag>0</acNoFlag>

<prdAcNo>147258</prdAcNo>

<itemNo>9.000000</itemNo>

<oppAcNo>654321</oppAcNo>

<oppAcName>募管通二期产品账号对外转账</oppAcName>

<payFalg>01</payFalg>

<openDate>20161122</openDate>

</Map>

<Map>

<acNoFlag>0</acNoFlag>

<prdAcNo>147258</prdAcNo>

<itemNo>33.000000</itemNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>上存下拨分开 48</oppAcName>

<payFalg>01</payFalg>

16 / 41

<openDate>20170324</openDate>

民生银行银企直联

</Map>

</List>

</xDataBody>

</CMBC>

5.募管通产品账号委托人清单查询(prdFincDetailQry)

本部分更新日期:2021-04-02

5.1.请求(prdFincDetailQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标（必输，但无作用）

 <insId>

流水号

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <acNo>

实体账户（★）

 <prdAcNo>

产品账号（★）

 <currentIndex> 起始位置

 <pageSize>

查询笔数

 <actDate>

备用字段(暂不支持)

</xDataBody>

5.2.响应(prdFincDetailQry)

  标记

说明

长度

64

64

32

6

长度

17 / 41

<xDataBody>

服务消息集

 <trnId>

客户端产生的交易唯一标志（★）

 <recordNumber>

总数

 <List>

  <Map>

   <transDate>

来账日期(yyyy-MM-dd)

   <investAmt>

投资总金额

   <redeemAmt>

赎回总金额

   <oppAcNo>

对方账户

   <oppAcName>

对方户名

   <oppBankName> 对方开户行名称

   <oppBankNo>

对方开户行行号

民生银行银企直联

32

32

18,2

39

180

60

  <Map>

 <List>

</xDataBody>

5.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdFincDetailQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

18 / 41

民生银行银企直联

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

</requestHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<insId>sfasdf12313</insId>

<acNo>*********</acNo>

<prdAcNo>147258</prdAcNo>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

<actDate></actDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdFincDetailQry" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-08-29 17:07:58</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<recordNumber>2</recordNumber>

<List>

<Map>

<transDate>2016-11-07</transDate>

<investAmt>120.00</investAmt>

19 / 41

民生银行银企直联

<redeemAmt>55.00</redeemAmt>

<oppAcNo></oppAcNo>

<oppAcName></oppAcName>

<oppBankName></oppBankName>

<oppBankNo></oppBankNo>

</Map>

<Map>

<transDate>2016-11-08</transDate>

<investAmt>10000.00</investAmt>

<redeemAmt>1014.00</redeemAmt>

<oppAcNo>*********</oppAcNo>

<oppAcName>集中测试 01</oppAcName>

<oppBankName></oppBankName>

<oppBankNo></oppBankNo>

</Map>

</List>

</xDataBody>

</CMBC>

6.募管通产品账号对委托人转账(prdFincTrans)

本部分更新日期:2021-04-02

6.1.请求(prdFincTrans)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★）

64

 <acntNo>

付款账号(格式：实账户-产品账户)（★）

20 / 41

民生银行银企直联

 <acntName>

付款人名称（★）

 <oppAcNo>

收款账号（★）

 <oppAcName>

收款人名称（★）

 <oppBankNo>

收款人开户行行号（★）

 <oppBankName> 收款人开户行名称

 <amount>

赎回金额（★）

 <explain>

附言

 <certNo>

企业自制凭证号

 <actDate>

备用字段

</xDataBody>

6.2.响应(prdFincTrans)

  标记

说明

<xDataBody> 服务消息集

 <transfer>

 <trnId>

客户端交易的唯一标志（★）

 <cltcookie> 如果客户端发送 cookie，同步的历史记录不包括原有的

cltcookie（★）

 <svrId>

服务器该笔交易的标识（★）

 <insId>

指令 ID，请求时给出的 ID（★）

 <transfer>

60

32

60

12

80

15,2

22

8

长度

32

64

21 / 41

民生银行银企直联

</xDataBody>

6.3.例子

请求报文

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdFincTrans">

<requestHeader>

<dtClient>2015-05-08 15:18:40</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>14d3263f15d1</trnId>

<insId>***************</insId>

<acntNo>*********-147258</acntNo>

<acntName>苦咖啡</acntName>

<oppAcNo>654321</oppAcNo>

<oppAcName>募管通二期产品账号对外转账</oppAcName>

<oppBankNo>************</oppBankNo>

<oppBankName>中国建设银行</oppBankName>

<amount>1.0</amount>

<certNo>1234</certNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdFincTrans" security="none" lang="chs" header="100"

version="100">

<responseHeader>

22 / 41

民生银行银企直联

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-08-29 10:05:53</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>14d3263f15d1</trnId>

<insId>***************</insId>

</xDataBody>

</CMBC>

7.募管通产品账号明细查询(prdTransDetailQry)

本部分更新日期:2021-04-02

查询募管通产品账号的交易明细。

7.1.请求(prdTransDetailQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标（必输，但无作用）

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <acNo>

实体账户（★）

 <prdAcNo>

产品账号（★）

 <beginDate>

起始日期(格式：yyyy-MM-dd)（★）

32

6

6

23 / 41

 <endDate>

结束日期(格式：yyyy-MM-dd)（★）

民生银行银企直联

 <currentIndex> 起始位置

 <pageSize>

查询笔数

 <actDate>

备用字段(暂不支持)

</xDataBody>

7.2.响应(prdTransDetailQry)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端产生的交易唯一标志（★）

 <recordNumber>

总笔数

 <List>

  <Map>

   <transDate>

交易日期(yyyy-MM-dd)

   <serialNo>

交易流水号

   <subSerialNo>

交易子流水号

   <transTime>

交易时间

   <loanAmount>

借方金额

   <lendAmount>

贷方金额

   <slfBalance>

自身余额

   <sertNo>

凭证号

长度

32

32

8

15,2

15,2

15,2

8

24 / 41

   <oppAcNo>

对方账户

   <oppAcName>

对方户名

   <oppBankName> 对方开户行名称

   <remark>

备注

民生银行银企直联

39

180

60

  <Map>

 <List>

</xDataBody>

7.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdTransDetailQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<cltcookie></cltcookie>

<beginDate>2017-08-01</beginDate>

<endDate>2017-08-24</endDate>

<acNo>*********</acNo>

<prdAcNo>147258</prdAcNo>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

25 / 41

民生银行银企直联

<actDate></actDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdTransDetailQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-08-29 17:36:34</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<recordNumber>1</recordNumber>

<List>

<Map>

<transDate>2017-08-24</transDate>

<serialNo></serialNo>

<subSerialNo></subSerialNo>

<transTime>110036</transTime>

<loanAmount></loanAmount>

<lendAmount>3.30</lendAmount>

<slfBalance>9433.18</slfBalance>

<sertNo></sertNo>

<oppAcNo>654321</oppAcNo>

<oppAcName>募管通二期产品账号对外转账</oppAcName>

<oppBankName>中国建设银行</oppBankName>

<remark></remark>

</Map>

</List>

26 / 41

</xDataBody>

</CMBC>

民生银行银企直联

8.募管通批量产品账号明细查询(prdBatchTransDeta

ilQry)

本部分更新日期:2021-04-02

支持查询募管通多个产品账号的交易明细。

8.1.请求(prdBatchTransDetailQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标（必输，但无作用）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <acNo>

实体账户（★）

 <prdAcNo>

产品账号 多个产品账号 以“|”隔开

 <beginDate>

起始日期(格式：yyyy-MM-dd)（★）

 <endDate>

结束日期(格式：yyyy-MM-dd)（★）

 <currentIndex> 起始位置

 <pageSize>

查询笔数

 <actDate>

备用字段(暂不支持)

</xDataBody>

长度

64

32

6

6

27 / 41

8.2.响应(prdBatchTransDetailQry)

民生银行银企直联

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端产生的交易唯一标志（★）

 <recordNumber>

总笔数

 <List>

  <Map>

   <transDate>

交易日期(yyyy-MM-dd)

   <serialNo>

交易流水号

   <subSerialNo>

交易子流水号

   <prdAcNo>

产品账号

   <transTime>

交易时间

   <loanAmount>

借方金额

   <lendAmount>

贷方金额

   <slfBalance>

自身余额

   <sertNo>

凭证号

   <oppAcNo>

对方账户

   <oppAcName>

对方户名

   <oppBankName> 对方开户行名称

   <postscript>

附言

长度

32

32

6

8

15,2

15,2

15,2

8

39

180

255

28 / 41

   <purpose>

摘要

民生银行银企直联

60

  <Map>

 <List>

</xDataBody>

8.3.例子

请求字段：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdBatchTransDetailQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<cltcookie></cltcookie>

<insId>sfasdf12313</insId>

<beginDate>2019-03-01</beginDate>

<endDate>2019-03-01</endDate>

<acNo>*********</acNo>

<prdAcNo>000005</prdAcNo>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

<actDate></actDate>

</xDataBody>

</CMBC>

返回字段：

29 / 41

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdBatchTransDetailQry" security="none" lang="chs"

民生银行银企直联

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-03-11 09:25:10</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>342423dsxfsad</trnId>

<recordNumber>7</recordNumber>

<List>

<Map>

<transDate>2019-03-01</transDate>

<serialNo>********</serialNo>

<subSerialNo>31301201903017961344606503660061</subSerialNo>

<transTime>151851</transTime>

<prdAcNo>000005</prdAcNo>

<loanAmount>1000.00</loanAmount>

<lendAmount></lendAmount>

<slfBalance>********.23</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>上存下拨分开 46</oppAcName>

<oppBankName>中国民生银行股份有限公司北京正义路支行

</oppBankName>

<postscript>子账簿 000005 付款</postscript>

<purpose>子账簿 000005 付款</purpose>

</Map>

<Map>

30 / 41

<transDate>2019-03-01</transDate>

<serialNo>********</serialNo>

民生银行银企直联

<subSerialNo>31301201903017961345306503660949</subSerialNo>

<transTime>162419</transTime>

<prdAcNo>000005</prdAcNo>

<loanAmount>1000.00</loanAmount>

<lendAmount></lendAmount>

<slfBalance>********.23</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>上存下拨分开 46</oppAcName>

<oppBankName>中国民生银行股份有限公司北京正义路支行

</oppBankName>

<postscript>子账簿 000005 付款</postscript>

<purpose>子账簿 000005 付款</purpose>

</Map>

</List>

</xDataBody>

</CMBC>

9.募管通产品账号对外转账(prdXfer)

本部分更新日期:2024-02-20

备注：使用此交易后需使用对账查询，查询此交易的最终状态

9.1.请求(prdXfer)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

31 / 41

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★）

64

民生银行银企直联

 <acntNo>

付款账号(格式：实账户-产品账户)（★）

 <acntName>

付款人名称（★）

 <acntToNo>

收款账号（★）

 <acntToName> 收款人名称（★）

 <externBank>

是否跨行,0:同行；1：跨行；（★）

 <localFlag>

汇路： 行内转账时汇路输入空值 跨行转账：2:小

额;3 大额; 5:网银互联; （注： 当选择同行转账

时， 汇路字段请输入空值 当选择跨行转账时， 汇路

2 代表不落地小额汇路； 汇路 3 代表不落地大额汇

路； 汇路 5 网银互联（由于网银互联汇路转账是异

步处理操作，无法即时得出最终的转账结果,所以需要

通过 qryXfer 接口进行对账得出人行最终转账结

果） 注意：行内转账时不需要要求指定行号；跨行转

账时必须提供完整的行号（★）

 <rcvCustType> 收款人账户类型：1:对公；2:对私；

 <bankCode>

收款人开户行行号(跨行转账时此项必填)

 <bankName>

收款人开户行名称(跨行转账时此项必填)

 <bankAddr>

收款人开户行地址

 <areaCode>

收款行地区编号(可选)

 <amount>

转账金额（★）

 <explain>

摘要/用途

 <certNo>

企业自制凭证号

60

32

60

1

1

1

12

80

15,2

50

8

32 / 41

民生银行银企直联

 <actDate>

备用字段

</xDataBody>

9.2.响应(prdXfer)

  标记

说明

<xDataBody> 服务消息集

 <transfer>

 <trnId>

客户端交易的唯一标志（★）

 <cltcookie> 如果客户端发送 cookie，同步的历史记录不包括原有的

cltcookie（★）

 <svrId>

服务器该笔交易的标识（★）

 <insId>

指令 ID，请求时给出的 ID（★）

 <transfer>

</xDataBody>

9.3.例子

请求报文

长度

32

64

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="prdXfer">

<requestHeader>

<dtClient>2015-05-08 15:18:40</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

33 / 41

民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>14d3263f15d1</trnId>

<insId>201705010www2</insId>

<acntNo>*********-147258</acntNo>

<acntName>147258</acntName>

<acntToNo>654321</acntToNo>

<acntToName>募管通二期产品账号对外转账</acntToName>

<externBank>1</externBank>

<localFlag>2</localFlag>

<rcvCustType>1</rcvCustType>

<bankCode>************</bankCode>

<bankName>中国建设银行</bankName>

<bankAddr></bankAddr>

<amount>5.00</amount>

<explain>募管通对外转账测试</explain>

<certNo></certNo>

<actDate></actDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="prdXfer" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-08-29 16:37:53</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

34 / 41

民生银行银企直联

<trnId>14d3263f15d1</trnId>

<insId>201705010www2</insId>

</xDataBody>

</CMBC>

10.单笔转账交易结果查询(qryXfer)

本部分更新日期:2022-10-19

根据流水号（insId）查询一笔转账交易是否成功

请注意，单笔对账交易，若在报文头 code 字段返回 E1602，则表示该笔交易银行未受
理，可以视为交易失败 。

10.1.请求(qryXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

 <cltcookie> 可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★）

 <svrId>

服务器返回转账消息的标识（非必输）

长度

64

64

32

</xDataBody>

10.2.响应(qryXfer)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★）

长度

64

64

35 / 41

 <svrId>

服务器对转账消息的标识（★）

 <statusId>

 <statusCode>

状态码：

民生银行银企直联

32

1

0: 原交易成功；

2: 原交易失败；

3: 对账因为网络原因失败，请过一会再试，原转账

交易状态未知；

4: 原交易处理中

5: 交易成功（已退汇）（仅白名单客户返回）

 <statusSeverity>

当状态码为 0 时，返回 ok；

16

当状态码为 2 时，返回 W6191；

当状态码为 3 时，返回 F

当状态码为 4 时 所有汇路

10 —— 等待银行审批

12 —— 银行审批通过，等待发送核心

当汇路是大、小额时

1 —— 已受理

5 —— 冲账

当汇路是网银互联时

PR00 —— 已转发

PR02 —— 已付款

PR06 —— 待处理

PR07 —— 已处理

PR08 —— 已撤销

PR10 —— 已确认

当状态码为 5 时，返回 PS15

 <statusErrMsg>

36 / 41

 </statusId>

描述信息（★）

民生银行银企直联

</xDataBody>

说明：对账响应描述信息

在 xFerStatus 中

10.3.例子

请求报文

<?xml version = "1.0" encoding = "utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="qryXfer">

<requestHeader>

<dtClient>20020615 10:20:45</dtClient>

<clientId>2200003220</clientId>

<userId>2200003220001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>00sa01ds01006861</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

</xDataBody>

</CMBC>

响应报文

1.请求的流水号不存在

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="chs" security="none" trnCode="qryXfer"

version="100">

<responseHeader>

37 / 41

民生银行银企直联

<status>

<code>E1602</code>

<severity>Error</severity>

<message>此流水号不存在,请查证</message>

</status>

<dtServer>2008-09-02 14:05:58</dtServer>

<dtDead></dtDead>

<lanaguge>utf-8</lanaguge>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>46tr456</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

<statusId>

<statusCode></statusCode>

<statusSeverity></statusSeverity>

<statusErrMsg></statusErrMsg>

</statusId>

</xDataBody>

</CMBC>

3.请求的流水号存在且转账成功的交易

<<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="utf-8" security="none" trnCode="qryXfer"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-02 14:11:48</dtServer>

<dtDead></dtDead>

<lanaguge>utf-8</lanaguge>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>8053qwe</insId>

38 / 41

民生银行银企直联

<svrId>jVv2Hc3ZHAzX</svrId>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>转账交易已成功！</statusErrMsg>

</statusId>

</xDataBody>

</CMBC>

4.请求的流水号已退汇

<<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="qryXfer" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2022-07-14 14:44:28</dtServer>

<userKey>N</userKey>

<dtDead />

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>22071410264600000000000203428461</insId>

<svrId>31301202207146127589058313000000</svrId>

<statusId>

<statusCode>5</statusCode>

<statusSeverity>PS15</statusSeverity>

<statusErrMsg>交易成功（已退汇）</statusErrMsg>

</statusId>

</xDataBody>

</CMBC>

报文头的<code>节点返回错误码说明

39 / 41

1、E1602 —— 则表示该笔交易银行未受理，可以视为交易失败

2、EYQ13 —— 此汇路目前不支持状态未知交易的对账查询，原交易的状态为未知

民生银行银企直联

3、E6031 —— 转账状态未知

该交易存在的问题：

1、目前上海同城汇路交易如果转账时候接口明确返回成功或者失败，此处可以返回处理
结果，如果转账当时返回超时（WEC02）异常，此处无法调核心接口进行查询。

2：对于原交易状态未知的情况，大、小额、网银互联和行内转账能够从核心获得原交易
的状态，其他汇路，目前不能获得原交易的终态

40 / 41

