银企直联接口文档

（财富管理-定期存款）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

V2.0.0 2024-12-

qryFixAcct(总公司定期存款列表查

05

询) 接口更新：

1. 入参增加 qryDeptTypeAndCifInfo

（是否查询定期类型信息）字段，则出

参中返回 deptType（存期类型）、

termCode（存期代码）、cifNo（客户

号）和 cifName（客户名称）；若

qryDeptTypeAndCifInfo 不上送或上

送非 0 其他值，则不返回上述字段；

2. deptType（存期类型）、

termCode（存期代码）字段与定期存

款列表查询(queryRegularList)接口出

参中字段内容一致；

3. cifNo（客户号）和 cifName（客户

名称）字段为该账户实际所属企业的企

业名称。

1 / 21

目录

民生银行银企直联

目录 .............................................................................................2

1. 总公司定期存款查询交易(QRYFIXACCT) ...............................................3

1.1. 请求(QRYFIXACCT) .................................................................... 3

1.2. 响应(QRYFIXACCT) .................................................................... 3

1.3. 例子 ...................................................................................... 6

2. 定期转活期交易(QUERYREGULARB2D) .............................................. 9

2.1. 请求(QUERYREGULARB2D) ........................................................10

2.2. 响应(QUERYREGULARB2D) ........................................................10

2.3. 例子 .................................................................................... 10

3. 定期存款列表查询(QUERYREGULARLIST) ......................................... 12

3.1. 请求(QUERYREGULARLIST) .........................................................12

3.2. 响应(QUERYREGULARLIST) .........................................................12

3.3. 例子 .................................................................................... 15

4. 活期转定期交易(REGULARD2B) ...................................................... 17

4.1. 请求(REGULARD2B) .................................................................17

4.2. 响应(REGULARD2B) .................................................................18

4.3. 例子 .................................................................................... 19

2 / 21

1.总公司定期存款查询交易(qryFixAcct)

民生银行银企直联

本部分更新日期:2024-12-05

业务逻辑：

查询出本公司以及本行授权账户的定期存款列表（授权定期账户列表）。

1.1.请求(qryFixAcct)

  标记

<xDataBody>

说明

长度

<qryDeptTypeAndCifInfo>

是否查询定期类型信息：

若不上送或上送非 0 其他

值，则不返回:

<deptType>（存期类型）、

<termCode>（存期代码）、

<cifNo>（客户号）、

<cifName>（客户名称）

</xDataBody>

1.2.响应(qryFixAcct)

  标记

说明

长度

<xDataBody>

 <acctList>

账户列表开始

3 / 21

  标记

说明

  <acctInfo>

0..n

  <acctName>

账户名称（★）

  <acctNo>

账号（★）

  <currNo>

币种（★）

  <acctBal>

账户余额（★）

  <freezeBal>

冻结金额（★）

  <avlBal>

可用余额（★）

  <openDate>

开户日（★）

  <startDate>

起息日（★）

  <dueDate>

到期日（★）

  <rate>

利率（★）

  <term>

存期（★）

  <status>

账户状态（★）

0-正常

1-销户

2-只收不付冻结

3-封闭冻结

4-删除

5-未使用

6-结清

7-打印

8-碰库 |

9-不动户

民生银行银企直联

长度

64

32

16

15,2

15,2

15,2

10

10

10

9,7

3

1

4 / 21

  标记

说明

长度

民生银行银企直联

A-不动户转收益

B-死亡户

C-报案户

D-请与开户行接洽

E-不能在他行销记户

F-准客户

G-未复核

H-久悬未取户

R-被当日冲正

S-被隔日冲正

  <openBranch> 开户机构（★）

80

<deptType>

存期类型：

00-储蓄活期

01-储蓄整存整取

02-储蓄定活两便

03-储蓄存本取息

04-储蓄零存整取

05-储蓄通知存款

06-教育储蓄

09-钱生钱

10-对公活期

11-对公整存整取

15-对公通知存款

25-对公保证金存款"

<termCode>

存期代码：

200-活期

201-一天

5 / 21

  标记

说明

长度

民生银行银企直联

207-七天

101-一月

103-三月

106-六月

001-一年

002-二年

003-三年

005-五年

006-六年

<cifNo>

客户号：

实际所属企业客户号

<cifName>

客户名称：

实际所属企业的企业名称

  </acctInfo>

 </acctList>

</xDataBody>

1.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="qryFixAcct">

<requestHeader>

<dtClient>20020615 10:20:45</dtClient>

<clientId>2001660306</clientId>

<userId>200166030601</userId>

<userPswd>111111</userPswd>

<language>chs</language>

6 / 21

民生银行银企直联

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none" trnCode="qryFixAcct"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-18 09:42:14</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<acctList>

<acctInfo>

<acctName>a</acctName>

<acctNo>0101014270001797</acctNo>

<currNo>01</currNo>

<acctBal>10000.00</acctBal>

<freezeBal>0.00</freezeBal>

<avlBal>10000.00</avlBal>

<openDate>20051111</openDate>

<startDate>20051111</startDate>

<dueDate>20060511</dueDate>

<rate>2.0700000</rate>

<term>六月</term>

<status>正常</status>

<openBranch>中国民生银行总行营业部</openBranch>

7 / 21

民生银行银企直联

</acctInfo>

<acctInfo>

<acctName>a</acctName>

<acctNo>0101014260001521</acctNo>

<currNo>01</currNo>

<acctBal>10000.00</acctBal>

<freezeBal>0.00</freezeBal>

<avlBal>10000.00</avlBal>

<openDate>20051111</openDate>

<startDate>20051111</startDate>

<dueDate>20060211</dueDate>

<rate>1.7100000</rate>

<term>三月</term>

<status>正常</status>

<openBranch>中国民生银行总行营业部</openBranch>

</acctInfo>

<acctInfo>

<acctName>a</acctName>

<acctNo>0101014260001513</acctNo>

<currNo>01</currNo>

<acctBal>10000.00</acctBal>

<freezeBal>0.00</freezeBal>

<avlBal>10000.00</avlBal>

<openDate>20051111</openDate>

<startDate>20051111</startDate>

<dueDate>20060211</dueDate>

<rate>1.7100000</rate>

<term>三月</term>

<status>正常</status>

<openBranch>中国民生银行总行营业部</openBranch>

</acctInfo>

</acctList>

</xDataBody>

</CMBC>

8 / 21

2.定期转活期交易(QueryRegularB2D)

民生银行银企直联

本部分更新日期:2022-05-16

业务逻辑：

1.本功能可以将定期存款账户里的定期存款转回活期账户。

2.上送的定期存款账户必须是有效的定期账户

3.类型上，支持普通存款账户、通知存款账户（即未通知支取）

4.币种上，支持人民币账户、外币账户

5.转出的活期账户是自己公司的有效活期账户，币种与定期账户一致

6.支取次数：普通定期存款只能进行一次提前支取，第二次支取为全部支取；通知存款无

限制。

7.如果该笔定期存款已经在柜台打印过凭证，则无法通过此交易转活期

8.可通过 qryFixAcct（总公司定期存款查询交易）和 queryRegularList （定期存款列

表查询）两支交易查询本客户所有定期存款账户。

9.转出后的定期存款的最少留存金额

币种

普通存款 通知存款

人民币 RMB/CNY

10000

500000

港币 HKD

12000

600000

美元 USD

1600

80000

加拿大元 CAD

2000

100000

瑞士法郎 CHF

1410

70500

新加坡元 SGD

2000

100000

日元 JPY

120000

6000000

英镑 GBP

1000

50000

9 / 21

币种

普通存款 通知存款

欧元 EUR

1200

60000

澳大利亚元 AUD

2000

100000

2.1.请求(QueryRegularB2D)

  标记

说明

<xDataBody>

 <insId>

交易流水号（唯一）（★）

 <payerAcct> 定期账号（★）

 <payerAmt> 金额（★）

 <payeeAcct> 活期账号（★）

</xDataBody>

2.2.响应(QueryRegularB2D)

  标记

说明

<xDataBody>

 <payerAcctBal> 定期账户余额

</xDataBody>

2.3.例子

请求报文

民生银行银企直联

长度

64

32

15,2

32

长度

15,2

10 / 21

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QueryRegularB2D">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

民生银行银企直联

<clientId>2001660306</clientId>

<userId>200166030601</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>ertewe213e</insId>

<payerAcct>0101014280003321</payerAcct>

<payerAmt>10000</payerAmt>

<payeeAcct>0101014830000648</payeeAcct>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="QueryRegularB2D"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2010-01-06 16:21:27</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<payerAcctBal>1344.00</payerAcctBal>

11 / 21

</xDataBody>

</CMBC>

3.定期存款列表查询(queryRegularList)

民生银行银企直联

本部分更新日期:2021-04-02

业务逻辑：

1.请求条数最高为 20 条；

2.当开始条数不是 1 的时候 totalNum 为 0；

3.查询本公司定期存款。类型详见<deptType>；

4.可以查询外币定期账户。

常见问题：

Q1：定期存款全部支取后是否可以通过网银查到原定期存款信息？

A1：定期存款全部支取后，该定期账户即被销户，不会再被查询到。

3.1.请求(queryRegularList)

  标记

说明

长度

<xDataBody>

 <inqStaNo>

开始条数（★）

 <pageRecNum> 请求条数（★）

</xDataBody>

3.2.响应(queryRegularList)

  标记

说明

长度

12 / 21

  标记

说明

<xDataBody>

 <totalNum>

总条数

 <balList>

列表开始

  <balInfo>

0…n

  <acctName>

账户名称

  <account>

  <currCode>

  <acctBal>

  <frzBal>

  <avlBal>

账号

币种

账户余额

冻结金额

可用余额

  <openDate>

开户日期

  <intrStaDate>

  <dueDate>

  <rate>

  <deptType>

起息日

到期日

利率

存期类型

00-储蓄活期

01-储蓄整存整取

02-储蓄定活两便

03-储蓄存本取息

04-储蓄零存整取

05-储蓄通知存款

06-教育储蓄

民生银行银企直联

长度

64

32

16

15,2

15,2

15,2

8

10

8

9,7

32

13 / 21

  标记

说明

民生银行银企直联

长度

  <termCode>

16

09-钱生钱

10-对公活期

11-对公整存整取

15-对公通知存款

25-对公保证金存款"

存期代码

200-活期

201-一天

207-七天

101-一月

103-三月

106-六月

001-一年

002-二年

003-三年

005-五年

006-六年

  <acctStasCode>

账户状态

1

0-正常

1-销户

2-只收不付冻结

3-封闭冻结

4-删除

5-未使用

6-结清

7-打印

8-碰库

9-不动户

14 / 21

  标记

说明

民生银行银企直联

长度

A-不动户转收益

B-死亡户

C-报案户

D-请与开户行接洽

E-不能在他行销记户

F-准客户

G-未复核

H-久悬未取户

R-被当日冲正

S-被隔日冲正

  <openBranchCode>

开户机构

80

  </balInfo>

 </balList>

列表结束

</xDataBody>

3.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="queryRegularList">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

15 / 21

民生银行银企直联

<xDataBody>

<inqStaNo>1</inqStaNo>

<pageRecNum>2</pageRecNum>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="queryRegularList" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-12-02 14:14:23</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<totalNum>20</totalNum>

<balList>

<balInfo>

<acctName>测试 **********</acctName>

<account>*********</account>

<currCode>01</currCode>

<acctBal>10330.00</acctBal>

<frzBal>0.00</frzBal>

<avlBal>10330.00</avlBal>

<openDate>********</openDate>

<intrStaDate>********</intrStaDate>

<dueDate>********</dueDate>

<rate>3.3000000</rate>

<deptType>11</deptType>

<termCode>001</termCode>

<acctStasCode>0</acctStasCode>

16 / 21

<openBranchCode>3307</openBranchCode>

民生银行银企直联

</balInfo>

</balList>

</xDataBody>

</CMBC>

4.活期转定期交易(regularD2B)

本部分更新日期:2021-04-02

企业定期存款业务逻辑：

针对本客户下已追加网银账户，通过本功能将活期账户资金转存为定期资金，转为定期存

款后，该笔资金不会在活期账户中体现，系统自动生成定期存款账户。

存期：

1、人民币存期包括：3 月、6 月、1 年、2 年、3 年、5 年、1 天通知和 7 天通知；

2、外币存期包括：1 月、3 月、6 月、1 年、2 年和 7 天通知。

起存金额限制：

1：普通定期存款：起存金额大于等于 10000；

2：通知存款：起存金额大于等于 500000；

可通过 qryFixAcct（总公司定期存款查询交易）和 queryRegularList （定期存款列表

查询）两支交易查询本客户所有定期存款账户。

4.1.请求(regularD2B)

  标记

说明

<xDataBody>

 <insId>

交易流水号（唯一）（★）

 <payerAcct>

活期账户（★）

 <deptCode>

定期储种（★）5 位数字（参考注释）

长度

64

32

5

17 / 21

  标记

说明

 <payerAmt>

金额（★）

 <maturityFlag> 本金到期处理方式

1 本金续存

民生银行银企直联

长度

15,2

1

2 本息转出 定期储种（deptCode）为通知存款

（15201 ，15207 ）时暂不支持 2（本息转出）

</xDataBody>

注释：deptCode ：

11101 企业定期存款一月(不支持人民币，银企直联不支持)

11103 企业定期存款三月

11106 企业定期存款六月

11001 企业定期存款一年

11002 企业定期存款二年(不支持人民币，银企直联不支持)

15201 企业通知存款一天

15207 企业通知存款七天

4.2.响应(regularD2B)

  标记

说明

<xDataBody>

 <assurAcct>

定期存款账号

 <payerAcctBal> 转出账户余额

 <acctBal>

定期余额

 <startDate>

开户日期

 <fixRate>

利率

长度

32

15,2

15,2

10

9,7

18 / 21

民生银行银企直联

长度

  标记

说明

</xDataBody>

4.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="regularD2B">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>2001660306</clientId>

<userId>200166030601</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>fae4576y5</insId>

<payerAcct>0101014830000648</payerAcct>

<deptCode>11001</deptCode>

<payerAmt>11342</payerAmt>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none" trnCode="regularD2B"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

19 / 21

<dtServer>2010-01-06 15:58:59</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<assurAcct>0101014280003313</assurAcct>

<payerAcctBal>61314598.64</payerAcctBal>

<acctBal>11311.00</acctBal>

<startDate>20100107</startDate>

<fixRate>2.2500000</fixRate>

</xDataBody>

</CMBC>

20 / 21

