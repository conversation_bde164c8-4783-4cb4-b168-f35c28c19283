银企直联接口文档

（新一代电子票据）

邮箱：<EMAIL>

版本

日期

说明

编写者 审核者

文档修改记录

民生银行银企直联

V1.0.0

2021-03-18

定义接口文档

V1.0.1

2024-08-21

追索通知

(B2eNbsRecourseRequest)

入参，<rcvgOrgCode>被追索

人统一社会信用代码字段改

为非必输。

V1.0.2

2024-11-13

持有票据查询

（B2eNbsDraftHoldingBillsQ

ry） 入参 custAccount 说明修

改为“签约账号/授权账

号”；

票据详细信息查询

（B2eNbsDraftDetail）入参

custAccount 说明修改为“签

约账号/授权账号”；

票据交易状态查询

（B2eNbsQryDraftTransStat

us）入参 custAccount 说明修

改为“签约账号/授权账

号”；

V1.0.3

2024-11-20

票据交易状态查询，返回字段删

除‘承兑日期’ 。

V1.1.0

2024-12-05

出票登记、提示承兑、通用签收

（提示承兑签收）新增商票承兑

限额校验。

V 2.1.0

2025-03-25

修改贴现申请入账信息说明。

1 / 293

目录

民生银行银企直联

目录 .............................................................................................2

1. 新增出票 .......................................................................................9

1.1. 票据信息新增（出票新增）(B2ENBSDRAFTISSUEADD) ......................... 9

1.1.1. 请求(B2eNbsDraftIssueAdd) .............................................................. 9

1.1.2. 响应(B2eNbsDraftIssueAdd) ............................................................ 11

1.1.3. 例子 ...................................................................................................12

1.2. 可出票登记票据列表查询(B2ENBSDRAFTISSUEQRY) .......................... 14

1.2.1. 请求(B2eNbsDraftIssueQry) .............................................................14

1.2.2. 响应(B2eNbsDraftIssueQry) .............................................................15

1.2.3. 例子 ...................................................................................................18

1.3. 票据信息删除（出票删除）(B2ENBSDRAFTISSUEDELETE) ....................21

1.3.1. 请求(B2eNbsDraftIssueDelete) ........................................................ 21

1.3.2. 响应(B2eNbsDraftIssueDelete) ........................................................ 22

1.3.3. 例子 ...................................................................................................23

1.4. 出票登记(B2ENBSDRAFTISSUEAPPLY) .......................................... 24

1.4.1. 请求(B2eNbsDraftIssueApply) ......................................................... 25

1.4.2. 响应(B2eNbsDraftIssueApply) ......................................................... 26

1.4.3. 例子 ...................................................................................................27

1.5. 可撤票列表查询(B2ENBSQUERYDESTRUCTABLEDRAFT) ...................... 28

1.5.1. 请求(B2eNbsQueryDestructableDraft) ............................................ 29

1.5.2. 响应(B2eNbsQueryDestructableDraft) ............................................ 30

1.5.3. 例子 ...................................................................................................32

1.6. 撤票申请(B2ENBSDESTRUCTIONREQUEST) ..................................... 35

1.6.1. 请求(B2eNbsDestructionRequest) ................................................... 35

1.6.2. 响应(B2eNbsDestructionRequest) ................................................... 36

1.6.3. 例子 ...................................................................................................37

2. 提示承兑 ..................................................................................... 40

2 / 293

民生银行银企直联
2.1. 可提示承兑票据列表查询(B2ENBSQUERYACCEPTANCEBLEDRAFT) .......... 40

2.1.1. 请求(B2eNbsQueryAcceptancebleDraft) ......................................... 40

2.1.2. 响应(B2eNbsQueryAcceptancebleDraft) ......................................... 41

2.1.3. 例子 ...................................................................................................43

2.2. 提示承兑(B2ENBSACCEPTANCEBLEDRAFT) ..................................... 46

2.2.1. 请求(B2eNbsAcceptancebleDraft) ................................................... 46

2.2.2. 响应(B2eNbsAcceptancebleDraft) ................................................... 47

2.2.3. 例子 ...................................................................................................48

3. 出票交付 ..................................................................................... 51

3.1. 可出票交付票据列表查询(B2ENBSISSUEDELIVERPRE) ........................ 51

3.1.1. 请求(B2eNbsIssueDeliverPre) .......................................................... 51

3.1.2. 响应(B2eNbsIssueDeliverPre) .......................................................... 52

3.1.3. 例子 ...................................................................................................54

3.2. 出票交付(B2ENBSISSUEDELIVER) ................................................ 58

3.2.1. 请求(B2eNbsIssueDeliver) ............................................................... 59

3.2.2. 响应(B2eNbsIssueDeliver) ............................................................... 60

3.2.3. 例子 ...................................................................................................61

4. 背书 ........................................................................................... 63

4.1. 可背书票据列表查询(B2ENBSDRAFTENDORSEMENTPRE) .....................63

4.1.1. 请求(B2eNbsDraftEndorsementPre) ................................................ 63

4.1.2. 响应(B2eNbsDraftEndorsementPre) ................................................ 64

4.1.3. 例子 ...................................................................................................67

4.2. 背书申请(B2ENBSENDORSEMENT) ............................................... 70

4.2.1. 请求(B2eNbsEndorsement) ..............................................................70

4.2.2. 响应(B2eNbsEndorsement) ..............................................................71

4.2.3. 例子 ...................................................................................................73

5. 提示付款 ..................................................................................... 75

5.1. 可提示付款票据列表查询(B2ENBSSENDAUTHRECEIPTPRE) .................. 75

3 / 293

民生银行银企直联
5.1.1. 请求(B2eNbsSendAuthReceiptPre) ..................................................75

5.1.2. 响应(B2eNbsSendAuthReceiptPre) ..................................................76

5.1.3. 例子 ...................................................................................................79

5.2. 提示付款申请(B2ENBSSENDAUTHRECEIPT) .................................... 83

5.2.1. 请求(B2eNbsSendAuthReceipt) ....................................................... 83

5.2.2. 响应(B2eNbsSendAuthReceipt) ....................................................... 84

5.2.3. 例子 ...................................................................................................85

6. 贴现 .......................................................................................... 88

6.1. 可贴现票据列表查询(B2ENBSELECTRONICDISCOUNTPRE) ................... 88

6.1.1. 请求(B2eNbsElectronicDisCountPre) ............................................... 88

6.1.2. 响应(B2eNbsElectronicDisCountPre) ............................................... 89

6.1.3. 例子 ...................................................................................................92

6.2. 贴现金额试算（B2ENBSCALELEDISCOUNTINTRST） .......................... 95

6.2.1. 请求(B2eNbsCalEleDiscountIntrst) .................................................. 95

6.2.2. 响应(B2eNbsCalEleDiscountIntrst) .................................................. 97

6.2.3. 例子 ...................................................................................................98

6.3. 贴现申请(B2ENBSDRAFTDISCOUNT) ........................................... 101

6.3.1. 请求(B2eNbsDraftDiscount) ...........................................................101

6.3.2. 响应(B2eNbsDraftDiscount) ...........................................................104

6.3.3. 例子 .................................................................................................106

6.4. 贴现线上协议查询(B2ENBSQUERYDISCOUNTTREATY) ...................... 109

6.4.1. 请求(B2eNbsQueryDiscountTreaty) .............................................. 109

6.4.2. 响应(B2eNbsQueryDiscountTreaty) .............................................. 110

6.4.3. 例子 .................................................................................................111

6.5. 贴现线上协议明细查询(B2ENBSQUERYDISCOUNTTREATYDETAILS) .......113

6.5.1. 请求(B2eNbsQueryDiscountTreatyDetails) ................................... 114

6.5.2. 响应(B2eNbsQueryDiscountTreatyDetails) ................................... 114

6.5.3. 例子 .................................................................................................116

7. 质押 ........................................................................................ 119

4 / 293

民生银行银企直联
7.1. 可质押票据列表查询(B2ENBSQUERYIMPAWNAPPLY) ........................119

7.1.1. 请求(B2eNbsQueryImpawnApply) ................................................. 119

7.1.2. 响应(B2eNbsQueryImpawnApply) ................................................. 120

7.1.3. 例子 .................................................................................................123

7.2. 质押申请(B2ENBSDRAFTIMPAWN) ............................................. 127

7.2.1. 请求(B2eNbsDraftImpawn) ............................................................ 127

7.2.2. 响应(B2eNbsDraftImpawn) ............................................................ 128

7.2.3. 例子 .................................................................................................129

8. 解质押 ......................................................................................132

8.1. 可解质押票据列表查询（B2ENBSDISIMPAWNAPPLLYPRE） ................ 132

8.1.1. 请求(B2eNbsDisImpawnAppllyPre) ................................................132

8.1.2. 响应(B2eNbsDisImpawnAppllyPre) ................................................133

8.1.3. 例子 .................................................................................................136

8.2. 解质押申请(B2ENBSDISIMPAWNAPPLLY) ..................................... 137

8.2.1. 请求(B2eNbsDisImpawnApplly) ..................................................... 138

8.2.2. 响应(B2eNbsDisImpawnApplly) ..................................................... 139

8.2.3. 例子 .................................................................................................140

9. 保证申请 ................................................................................... 142

9.1. 可保证申请票据列表查询(B2ENBSQUERYGUARANTEEABLEDRAFT) ....... 142

9.1.1. 请求(B2eNbsQueryGuaranteeableDraft) .......................................142

9.1.2. 响应(B2eNbsQueryGuaranteeableDraft) .......................................144

9.1.3. 例子 .................................................................................................146

9.2. 保证申请(B2ENBSGUARANTEEREQUEST) ......................................149

9.2.1. 请求(B2eNbsGuaranteeRequest) ...................................................149

9.2.2. 响应(B2eNbsGuaranteeRequest) ...................................................151

9.2.3. 例子 .................................................................................................152

10. 追索 ...................................................................................... 154

10.1. 可追索通知票据列表查询（B2ENBSRECOURSENOTICEPRE） ............. 154

10.1.1. 请求(B2eNbsRecourseNoticePre) ................................................ 155

5 / 293

民生银行银企直联
10.1.2. 响应(B2eNbsRecourseNoticePre) ................................................ 156

10.1.3. 例子 .............................................................................................. 158

10.2. 查询可被追索对象(B2ENBSCANCLEABLERECOURSEQUERY) .............. 162

10.2.1. 请求(B2eNbsCancleableRecourseQuery) .................................... 162

10.2.2. 响应(B2eNbsCancleableRecourseQuery) .................................... 163

10.2.3. 例子 .............................................................................................. 165

10.3. 追索通知(B2ENBSRECOURSEREQUEST) ......................................167

10.3.1. 请求(B2eNbsRecourseRequest) ...................................................168

10.3.2. 响应(B2eNbsRecourseRequest) ...................................................169

10.3.3. 例子 .............................................................................................. 170

11. 同意清偿 ................................................................................. 173

11.1. 可同意清偿申请列表查询（B2ENBSAGREEDISCHARGESIGNUPPRE） ... 173

11.1.1. 请求(B2eNbsAgreeDischargeSignUpPre) .................................... 173

11.1.2. 响应(B2eNbsAgreeDischargeSignUpPre) .................................... 174

11.1.3. 例子 .............................................................................................. 178

11.2. 同意清偿申请(B2ENBSAGREEDISCHARGESIGNUP) ........................ 181

11.2.1. 请求(B2eNbsAgreeDischargeSignUp) ......................................... 181

11.2.2. 响应(B2eNbsAgreeDischargeSignUp) ......................................... 182

11.2.3. 例子 .............................................................................................. 184

12. 不可转让撤销 ............................................................................186

12.1. 可进行不可转让撤销申请票据列表查询(B2ENBSNOTENDORSEREPEALPRE)186

12.1.1. 请求(B2eNbsNotEndorseRepealPre) ............................................186

12.1.2. 响应(B2eNbsNotEndorseRepealPre) ............................................187

12.1.3. 例子 .............................................................................................. 190

12.2. 不可转让撤销申请（B2ENBSNOTENDORSEREPEAL） ...................... 193

12.2.1. 请求(B2eNbsNotEndorseRepeal) ................................................. 193

12.2.2. 响应(B2eNbsNotEndorseRepeal) ................................................. 194

12.2.3. 例子 .............................................................................................. 195

13. 供票结算 ................................................................................. 198

6 / 293

民生银行银企直联
13.1. 可供票结算签收的供应链票据列表查询（B2ENBSDEDUCTCONFIRMPRE）198

13.1.1. 请求(B2eNbsDeductConfirmPre) ................................................. 198

13.1.2. 响应(B2eNbsDeductConfirmPre) ................................................. 199

13.1.3. 例子 .............................................................................................. 200

13.2. 供票结算确认签收(B2ENBSDEDUCTCFMSIGNUP) .......................... 202

13.2.1. 请求(B2eNbsDeductCfmSignUp) ................................................. 202

13.2.2. 响应(B2eNbsDeductCfmSignUp) ................................................. 205

13.2.3. 例子 .............................................................................................. 206

14. 通用签收 ................................................................................. 208

14.1. 可通用签收票据列表查询(B2ENBSQRYSTAYSIGNUPDRAFTS) ............208

14.1.1. 请求(B2eNbsQryStaySignUpDrafts) ............................................ 208

14.1.2. 响应(B2eNbsQryStaySignUpDrafts) ............................................ 210

14.1.3. 例子 .............................................................................................. 214

14.2. 通用签收(B2ENBSDRAFTSIGNUP) ............................................ 218

14.2.1. 请求(B2eNbsDraftSignUp) ........................................................... 218

14.2.2. 响应(B2eNbsDraftSignUp) ........................................................... 222

14.2.3. 例子 .............................................................................................. 223

15. 通用撤销 ................................................................................. 226

15.1. 可通用撤销票据列表查询(B2ENBSREVOCABLEDRAFTSQRY) ..............226

15.1.1. 请求(B2eNbsRevocableDraftsQry) .............................................. 226

15.1.2. 响应(B2eNbsRevocableDraftsQry) .............................................. 228

15.1.3. 例子 .............................................................................................. 232

15.2. 通用撤销(B2ENBSDRAFTREVOCATION) ...................................... 238

15.2.1. 请求(B2eNbsDraftRevocation) .....................................................238

15.2.2. 响应(B2eNbsDraftRevocation) .....................................................240

15.2.3. 例子 .............................................................................................. 241

16. 通用查询 ................................................................................. 243

16.1. 持有票据查询(B2ENBSDRAFTHOLDINGBILLSQRY) .........................243

16.1.1. 请求(B2eNbsDraftHoldingBillsQry) ..............................................243

7 / 293

民生银行银企直联
16.1.2. 响应(B2eNbsDraftHoldingBillsQry) ..............................................244

16.1.3. 例子 .............................................................................................. 248

16.2. 票据详细信息查询(B2ENBSDRAFTDETAIL) .................................. 251

16.2.1. 请求(B2eNbsDraftDetail) ............................................................. 251

16.2.2. 响应(B2eNbsDraftDetail) ............................................................. 252

16.2.3. 例子 .............................................................................................. 259

16.3. 票据交易状态查询(B2ENBSQRYDRAFTTRANSSTATUS) ....................262

16.3.1. 请求(B2eNbsQryDraftTransStatus) ............................................. 262

16.3.2. 响应(B2eNbsQryDraftTransStatus) ............................................. 265

16.3.3. 例子 .............................................................................................. 271

16.4. 票据交易状态精确查询(B2ENBSCOMPREHENSIVEQUERYBYTRANSID) .. 275

16.4.1. 请求(B2eNbsComprehensiveQueryByTransId) ........................... 275

16.4.2. 响应(B2eNbsComprehensiveQueryByTransId) ........................... 276

16.4.3. 例子 .............................................................................................. 281

16.5. 开户行信息查询 (B2ENBSQUERYBANKINFO) ................................ 284

16.5.1. 请求(B2eNbsQueryBankInfo) ....................................................... 285

16.5.2. 响应(B2eNbsQueryBankInfo) ....................................................... 286

16.5.3. 例子 .............................................................................................. 287

17. 业务字典 ................................................................................. 289

17.1. 业务办理渠道代码 .................................................................289

17.2. 省份代码 ........................................................................... 290

8 / 293

1.新增出票

民生银行银企直联

1.1.票据信息新增（出票新增）(B2eNbsDraftIssueAdd)

本部分更新日期:2023-10-31

说明：

1. 本接口用于预登记票据信息，之后再使用《出票登记》接口出票。

2. 出票方式为二连、三连需要在企网【票据/电子汇票/电票签约管理】菜

单完成出票业务代客服务签约

a.二连：出票完成之后，系统直接向承兑人发出提示承兑，承兑人完

成承兑签收之后，需由出票人做出票交付操作。

b.三连：当承兑人与出票人一致时，出票人出票后，自动完成“提示承

兑-承兑签收-出票交付”三个动作；当承兑人与出票人不一致时，系统完成

“出票-提示承兑”动作，承兑人完成“承兑签收”后，系统自动完成出票交

付。

1.1.1.请求(B2eNbsDraftIssueAdd)

标记

<xDataBody>

<trnId>

<insId>

说明

是否

必输

长

度

Y

Y

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请

64

求请勿重复

9 / 293

  
 
 
民生银行银企直联

<custAccount>

<billType>

<remitDt>

<dueDt>

<billMoney>

<acptName>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankCode>

<acptMemberId>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankNo>

<pyeeBankCode>

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

N

Y

Y

Y

Y

N

签约账号

票据类型：

AC01：银承

AC02：商承

出票日(yyyy-MM-dd)

票面到期日(yyyy-MM-dd)(yyyy-

MM-dd)

票面金额

承兑人名称

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行机构代码

承兑人业务办理渠道代码

票面收款人名称

票面收款人账号

收款人账户名称

票面收款人开户行行号

票面收款人开户行机构代码

32

4

8

8

12

300

32

300

12

12

6

300

50

300

12

12

10 / 293

 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
民生银行银企直联

<pyeeMemberId>

<isAllowSplitBill>

<transCtrctNo>

<invoiceNo>

N

Y

N

N

票面收款人业务办理渠道代码

是否分包;

0：否

1：是

合同编号

发票号码

<banEndrsmtMark> Y

是否可以转让

<billRemark>

<cloneNumber>

<issueType>

N

N

Y

EM00：可转让

EM01：不可转让

备注

张数 不传默认 1

出票方式

1：手动出票

2：二连

3：三连

</xDataBody>

1.1.2.响应(B2eNbsDraftIssueAdd)

标记

是否必

说明

输

<xDataBody>

服务消息集

6

1

30

30

4

150

32

1

长

度

11 / 293

  
  
  
  
  
  
  
  
  
银行渠道交易流水号

32

民生银行银企直联

客户业务请求流水号，同一业务请求请

64

勿重复

客户技术请求流水号，同一客户请勿重

64

复

Y

Y

Y

<svrId>

<insId>

<trnId>

<List>

<Map>

<hldrId> Y

持有流水号

<billId>

Y

票据流水号

long

long

</Map>

</List>

</xDataBody>

1.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDraftIssueAdd">

<requestHeader>

<dtClient>2023-07-19 14:09:56</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

12 / 293

  
  
  
  
   
    
    
   
  
民生银行银企直联

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230717112044205</trnId>

<insId>CMBCINS20230717112044206</insId>

<custAccount>*********</custAccount>

<billType>AC01</billType>

<remitDt>2023-08-11</remitDt>

<dueDt>2023-08-12</dueDt>

<billMoney>10000.00</billMoney>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankNo>************</acptBankNo>

<pyeeName>奚峰圣企业公司</pyeeName>

<pyeeAcctName>奚峰圣企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankNo>************</pyeeBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<transCtrctNo>1</transCtrctNo>

<invoiceNo>2</invoiceNo>

<banEndrsmtMark>EM00</banEndrsmtMark>

<cloneNumber>1</cloneNumber>

<issueType>1</issueType>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDraftIssueAdd">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

13 / 293

<dtServer>2023-07-19 14:10:14</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T7702202307191410215623354000PVU</svrId>

<insId>CMBCINS20230717112044206</insId>

<List>

<Map>

<billId>****************</billId>

<hldrId>****************</hldrId>

</Map>

</List>

<trnId>CMBCTRN20230717112044205</trnId>

</xDataBody>

</CMBC>

1.2.可出票登记票据列表查询(B2eNbsDraftIssueQry)

本部分更新日期:2023-10-31

说明：

1.本接口用于查询已预登记的票据列表，可根据返回的信息进行出票登记。

1.2.1.请求(B2eNbsDraftIssueQry)

标记

是否必

说明

输

<xDataBody>

长

度

<trnId>

Y

客户技术请求流水号，同一客户请勿重

64

复

<custAccount>

Y

签约账号

32

14 / 293

  
 
 
当前页码(从 1 开始)，不传默认为 1

int

民生银行银企直联

每页数据条数（默认 10 条，最大每页

int

<pageNo>

<pageSize>

<billType>

N

N

Y

100 条）

票据类型：

AC01：银承

AC02：商承

<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

1.2.2.响应(B2eNbsDraftIssueQry)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

输

N

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

15 / 293

4

12,2

12,2

10

10

10

10

长

度

32

 
 
 
 
 
 
 
 
 
  
  
  
<total>

<List>

<Map>

<billId>

<billRangeStart>

<billRangeEnd>

<hldrId>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<isAllowSplitBill>

Y

总条数

Y

Y

Y

Y

Y

Y

Y

Y

登记中心票据流水号

子票区间起始

子票区间截止

持有流水号

持有人名称

持有人账号

持有人开户行行号

票据类型

AC01：银承

AC02：商承

<billClass>

Y

票据种类

<remitDt>

<dueDt>

<issueType>

Y

Y

Y

ME01：纸票

ME02：电票

出票日

票面到期日

出票方式:

1：手动出票

民生银行银企直联

int

30

12

12

18

60

60

12

4

4

8

8

1

16 / 293

  
  
   
    
    
    
    
    
    
    
    
    
    
    
    
<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<drwrBankCode>

<pyeeName>

<pyeeAcctName>

<pyeeAcctNo>

<pyeeBankName>

<pyeeBankNo>

<pyeeBankCode>

<pyeeMemberId>

<acptName>

<acptAcctNo>

<acptAcctName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

2：二连

3：三连

票据包金额

出票人全称

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行名称

出票人开户机构

票面收款人全称

票面收款人账户名称

票面收款人账号

票面收款人开户行行名

票面收款人开户行行号

票面收款人开户机构代码

票面收款人渠道代码

承兑人全称

承兑人账号

承兑人账户名称

12

180

32

180

12

180

32

180

180

32

180

12

32

32

180

32

180

17 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<acptBankNo>

<acptBankName>

<acptBankCode>

<acptMemberId>

Y

Y

Y

Y

承兑人开户行行号

承兑人开户行行名

承兑人开户机构代码

承兑人渠道代码

<banEndrsmtMark> Y

是否可转让

EM00：可转让

EM01：不可转让

<transCtrctNo>

<invoiceNo>

<invoiceNo>

<billRemark>

N

N

N

N

合同编号

发票号码

发票号码

备注

12

180

32

32

4

30

30

30

255

</Map>

</List>

</xDataBody>

1.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftIssueQry">

<requestHeader>

<dtClient>2023-06-02 12:21:10</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

18 / 293

    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202306021008364</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDraftIssueQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:47:24</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T77502023062513494072483540001D9</svrId>

19 / 293

民生银行银企直联

<total>472</total>

<List>

<Map>

<billId>****************</billId>

<billRangeStart>1</billRangeStart>

<billRangeEnd>12300</billRangeEnd>

<hldrId>****************</hldrId>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC02</billType>

<billClass>ME02</billClass>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<issueType>1</issueType>

<billMoney>123.00</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行</drwrBankName>

<drwrBankCode>*********</drwrBankCode>

<pyeeName>奚峰圣企业公司</pyeeName>

<pyeeAcctName>奚峰圣企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<pyeeBankCode>*********</pyeeBankCode>

<pyeeMemberId>100009</pyeeMemberId>

<acptName>殷林聪企业公司</acptName>

<acptAcctNo>*********</acptAcctNo>

<acptAcctName>殷林聪企业公司</acptAcctName>

<acptBankNo>************</acptBankNo>

20 / 293

<acptBankName>中国民生银行股份有限公司北京中关村支行</acptBankName>

民生银行银企直联

<acptBankCode>*********</acptBankCode>

<acptMemberId>100009</acptMemberId>

<banEndrsmtMark>EM00</banEndrsmtMark>

</Map>

</List>

<trnId>CMBCTRN202306021008364</trnId>

</xDataBody>

</CMBC>

1.3.票据信息删除（出票删除）(B2eNbsDraftIssueDele

te)

本部分更新日期:2023-10-31

说明：

1.本接口用于删除不需要的、未进行出票登记的票据信息

1.3.1.请求(B2eNbsDraftIssueDelete)

标记

是否必

说明

输

Y

Y

<xDataBody>

<trnId>

<insId>

长

度

客户技术请求流水号，同一客户请勿重

64

复

客户业务请求流水号，同一业务请求请

64

勿重复

<custAccount>

Y

签约账号

32

<List>

21 / 293

  
 
 
 
  
<Map>

<billId>

Y

登记中心票据流水号

<hldrId> Y

持有流水号

</Map>

</List>

</xDataBody>

1.3.2.响应(B2eNbsDraftIssueDelete)

标记

是否必

说明

输

<xDataBody>

服务消息集

民生银行银企直联

30

18

长

度

32

<svrId>

<insId>

<trnId>

<List>

<Map>

<billId>

<hldrId>

Y

Y

Y

Y

Y

银行渠道交易流水号

客户业务请求流水号，同一业务请求

64

请勿重复

客户技术请求流水号，同一客户请勿

64

重复

登记中心票据流水号

持有流水号

30

18

22 / 293

   
    
    
   
  
  
  
  
  
  
   
    
    
<retCode> Y

错误码 1：成功 0：失败

12

民生银行银企直联

<retMsg>

Y

返回信息 1：交易成功 0：具体失败错

12

误信息

</Map>

</List>

</xDataBody>

1.3.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftIssueDelete">

<requestHeader>

<dtClient>2023-07-18 15:26:18</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<custAccount>*********</custAccount>

<trnId>CMBCTRN20230717112044135</trnId>

<insId>CMBCINS20230717112044136</insId>

<List>

<Map>

<billId>****************</billId>

<hldrId>****************</hldrId>

</Map>

</List>

23 / 293

    
    
   
  
民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDraftIssueDelete">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 15:26:19</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082201193540000BP</svrId>

<insId>CMBCINS20230717112044136</insId>

<trnId>CMBCTRN20230717112044135</trnId>

<list>

<Map>

<billId>****************</billId>

<hldrId>****************</hldrId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</list>

</xDataBody>

</CMBC>

1.4.出票登记(B2eNbsDraftIssueApply)

本部分更新日期:2024-12-05

24 / 293

说明：

1.本接口用于出票

民生银行银企直联

2.出票后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询出票结果

3.出票登记校验商票承兑限额，若校验失败，则返回提示客户，提示文

案：您提交的票据金额超过您的本年可用商票出票限额，详询客户经

理。

1.4.1.请求(B2eNbsDraftIssueApply)

标记

是否必

说明

长

度

输

Y

Y

<xDataBody>

<trnId>

<insId>

客户技术请求流水号，同一客户请勿重

64

复

客户业务请求流水号，同一业务请求请

64

勿重复

<custAccount>

Y

签约账号

<List>

<Map>

<billId>

Y

登记中心票据流水号

<hldrId> Y

持有流水号

</Map>

32

30

18

25 / 293

  
 
 
 
  
   
    
    
   
</List>

</xDataBody>

1.4.2.响应(B2eNbsDraftIssueApply)

标记

是否必

说明

输

<xDataBody>

服务消息集

民生银行银企直联

长

度

32

银行渠道交易流水号

客户业务请求流水号，同一业务请求

64

请勿重复

客户技术请求流水号，同一客户请勿

64

重复

<svrId>

<insId>

<trnId>

<List>

<Map>

<billId>

<hldrId>

<transId>

Y

Y

Y

Y

Y

Y

登记中心票据流水号

持有流水号

交易流水号

<retCode> Y

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

30

18

long

12

250

26 / 293

  
  
  
  
  
  
   
    
    
    
    
    
民生银行银企直联

1：交易成功

0：具体失败错误信息

</Map>

</List>

</xDataBody>

1.4.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftIssueApply">

<requestHeader>

<dtClient>2023-07-19 15:25:58</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<custAccount>*********</custAccount>

<trnId>CMBCTRN20230717112044225</trnId>

<insId>CMBCINS20230717112044226</insId>

<List>

<Map>

<billId>****************</billId>

<hldrId>****************</hldrId>

</Map>

</List>

</xDataBody>

</CMBC>

27 / 293

   
  
响应报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftIssueApply">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-19 17:12:01</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300202307190481510031354000F3I</svrId>

<insId>CMBCINS20230717112044226</insId>

<List>

<Map>

<hldrId>****************</hldrId>

<transId>****************</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN20230717112044225</trnId>

</xDataBody>

</CMBC>

1.5.可撤票列表查询(B2eNbsQueryDestructableDraf

t)

本部分更新日期:2023-10-31
说明：

本接口用于查询可撤票的票据列表

28 / 293

民生银行银企直联

可撤票的票据范围

a. 出票登记成功，尚未发起提示保证、提示承兑；

b. 出票登记成功，已发起提示保证、提示承兑并已撤销对应的提示保证、提

示承兑操作，且收到撤销成功回复；

c. 已发起提示保证或提示承兑，已收到人行转发的对方签收或拒绝签收回

复，且未发起出票交付申请；

d. 已发起出票交付并已撤销出票交付操作，且已收到人行撤销成功回复；

e. 已发起出票交付，且已收到人行转发的对方拒绝签收回复。

1.5.1.请求(B2eNbsQueryDestructableDraft)

标记

是否必

说明

长

度

输

Y

Y

N

N

N

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

<minBillMoney>

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

30

4

15,2

29 / 293

  
 
 
 
 
 
 
 
民生银行银企直联

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

出票日止 yyyy-MM-dd

</xDataBody>

1.5.2.响应(B2eNbsQueryDestructableDraft)

标记

是否必

说明

输

<xDataBody>

服务消息集

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billRangeStart>

<billRangeEnd>

N

Y

Y

Y

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客

户请勿重复

总条数

票据(包)号码

子票区间起始

子票区间截止

15,2

10

10

10

10

长

度

32

64

int

30

12

12

30 / 293

 
 
 
 
 
  
  
  
  
  
   
    
    
    
<billType>

Y

票据类型

AC01：银承

AC02：商承

<billClass>

Y

票据种类

ME01：纸票

ME02：电票

持有人名称

持有人账号

持有人开户行行号

<hldrName>

<hldrAcctNo>

<hldrBankNo>

Y

Y

Y

<isAllowSplitBill> Y

是否分包

0：否

1：是

出票日

汇票到期日

票据包金额

出票人全称

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行名

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

30

4

60

60

12

1

8

8

12

180

32

180

12

180

31 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<pyeeName>

<pyeeAcctName>

<pyeeAcctNo>

<pyeeBankName>

<pyeeBankNo>

<acptName>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankName>

<billRemark>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

票面收款人全称

票面收款人账户名称

票面收款人账号

票面收款人开户行行名

票面收款人开户行行号

承兑人全称

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行行名

备注

</Map>

</List>

</xDataBody>

1.5.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryDestructableDraft">

<requestHeader>

180

180

32

180

12

180

32

180

12

180

255

32 / 293

    
    
    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

<dtClient>2023-06-25 11:00:00</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835576</trnId>

<insId>CMBCINS20230612143835577</insId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryDestructableDraft">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity> <message>交易成功</message>

</status>

<dtServer>2023-06-25 11:00:01</dtServer>

<userKey>N</userKey>

33 / 293

民生银行银企直联

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<billRangeStart>1</billRangeStart>

<billRangeEnd>1000000</billRangeEnd>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>10000.00</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<pyeeName>奚峰圣企业公司</pyeeName>

<pyeeAcctName>奚峰圣企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

34 / 293

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

民生银行银企直联

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankNo>************</acptBankNo>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

</Map>

</List>

<total>472</total>

<trnId>CMBCTRN20230612143835576</trnId>

</xDataBody>

</CMBC>

1.6.撤票申请(B2eNbsDestructionRequest)

本部分更新日期:2023-10-31

说明：

1.本接口用于对《可撤票列表查询》返回的票据列表进行撤票

2.撤票后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询撤票结果

1.6.1.请求(B2eNbsDestructionRequest)

标记

是否必

说明

输

长

度

35 / 293

  
民生银行银企直联

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

签约账号

<xDataBody>

<trnId>

<insId>

<custAccount>

<List>

<Map>

Y

Y

Y

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

Y

子票区间截止

</Map>

</List>

</xDataBody>

1.6.2.响应(B2eNbsDestructionRequest)

标记

是否必

说明

输

<xDataBody>

服务消息集

<svrId>

Y

银行渠道交易流水号

32

30

12

12

长

度

32

36 / 293

 
 
 
  
   
    
    
    
   
  
  
  
民生银行银企直联

客户业务请求流水号，同一业务

64

请求请勿重复

客户技术请求流水号，同一客户

64

请勿重复

总条数

Y

Y

Y

<insId>

<trnId>

<total>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

子票区间截止

交易流水号

错误码 1：成功 0：失败

返回信息 1：交易成功 0：具体

失败错误信息

int

30

12

12

long

12

60

</Map>

</List>

</xDataBody>

1.6.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

37 / 293

  
  
  
  
   
    
    
    
    
    
    
   
  
<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDestructionRequest">

民生银行银企直联

<requestHeader>

<dtClient>2023-07-18 15:42:52</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<custAccount>*********</custAccount>

<trnId>CMBCTRN20230717112044149</trnId>

<insId>CMBCINS20230717112044150</insId>

<List>

<Map>

<billNo>5************20230710001007291</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>1022</billRangeEnd>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDestructionRequest">

38 / 293

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 15:42:55</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082201303540000F1</svrId>

<insId>CMBCINS20230717112044150</insId>

<List>

<Map>

<billNo>5************20230710001007291</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>1022</billRangeEnd>

<transId>****************</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN20230717112044149</trnId>

</xDataBody>

</CMBC>

39 / 293

2.提示承兑

民生银行银企直联

2.1.可提示承兑票据列表查询(B2eNbsQueryAcceptanc

ebleDraft)

本部分更新日期:2023-10-31

说明：

1.本接口用于查询可提示承兑的票据列表。

2.1.1.请求(B2eNbsQueryAcceptancebleDraft)

标记

是否必

说明

输

Y

Y

N

N

N

Y

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

长

度

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

票据类型： AC01：银承 AC02：商承

30

4

40 / 293

  
 
 
 
 
 
 
民生银行银企直联

<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

2.1.2.响应(B2eNbsQueryAcceptancebleDraft)

标记

是否必

说明

输

<xDataBody>

服务消息集

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billRangeStart>

N

Y

Y

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客

户请勿重复

总条数

票据(包)号码

子票区间起始

15,2

15,2

10

10

10

10

长

度

32

64

int

30

12

41 / 293

 
 
 
 
 
 
  
  
  
  
  
   
    
    
<billRangeEnd>

Y

子票区间截止

<isAllowSplitBill> Y

是否可分包流转 0-否 1-是

民生银行银企直联

<billType>

<billClass>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrBankName>

<drwrBankNo>

<pyeeName>

<pyeeAcctNo>

<pyeeBankName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

12

1

4

4

60

60

12

12

12

票据类型 AC01：银票 AC02：

商票

票据种类 ME01：纸票 ME02：

电票

持有人名称

持有人账号

持有人开户行行号

出票日

票面到期日

票据（包）金额

15,2

出票人名称

出票人账号

出票人开户行行名

出票人开户行行号

票面收款人名称

票面收款人账号

票面收款人开户行行名

180

32

180

12

180

32

180

42 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<pyeeBankNo>

<acptName>

<acptAcctNo>

<acptBankNo>

<acptBankName>

<billRemark>

Y

Y

Y

Y

Y

N

票面收款人开户行行号

承兑人名称

承兑人账号

承兑人开户行行号

承兑人开户行行名

备注

12

180

32

12

180

255

</Map>

</List>

</xDataBody>

2.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryAcceptancebleDraft">

<requestHeader>

<dtClient>2023-06-02 18:10:23</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

43 / 293

    
    
    
    
    
    
   
  
<xDataBody>

<trnId>CMBCTRN2023060210083638</trnId>

<custAccount>*********</custAccount>

民生银行银企直联

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryAcceptancebleDraft">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:49:05</dtServer>

<userKey>N</userKey>

44 / 293

民生银行银企直联

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T774920230625135104881335400023B</svrId>

<total>55</total>

<List>

<Map>

<billNo>5********************000396603</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>88811</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>888.11</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>金梦浩企业公司</pyeeName>

45 / 293

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

民生银行银企直联

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<acptBankNo>************</acptBankNo>

</Map>

</List>

<trnId>CMBCTRN2023060210083638</trnId>

</xDataBody>

</CMBC>

2.2.提示承兑(B2eNbsAcceptancebleDraft)

本部分更新日期:2024-12-05

说明：

1.本接口用于对可提示承兑的票据进行承兑。

2.承兑后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询交易结果。

3.提示承兑校验商票承兑限额，若校验失败，则返回提示客户，提示文

案：您提交的票据金额超过您的本年可用商票出票限额，详询客户经

理。

2.2.1.请求(B2eNbsAcceptancebleDraft)

标记

是否必 说明

长

46 / 293

  
民生银行银企直联

度

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请

64

求请勿重复

签约账号

输

Y

Y

Y

<xDataBody>

<trnId>

<insId>

<custAccount>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

Y

子票区间截止

</Map>

</List>

</xDataBody>

2.2.2.响应(B2eNbsAcceptancebleDraft)

标记

是否必

说明

输

<xDataBody>

服务消息集

32

30

12

12

长

度

47 / 293

 
 
 
 
  
   
   
   
  
 
  
Y

Y

Y

民生银行银企直联

银行渠道交易流水号

32

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

<svrId>

<trnId>

<insId>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

30

12

12

long

12

子票区间截止

交易流水号

错误码 1：成功 0：失败

返回信息 1：交易成功 0：具体

250

失败错误信息

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

</Map>

</List>

</xDataBody>

2.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

48 / 293

  
  
  
  
   
    
    
    
    
    
    
   
  
<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsAcceptancebleDraft">

民生银行银企直联

<requestHeader>

<dtClient>2022-07-19 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060718373293</trnId>

<insId>CMBCINS2023060718373294</insId>

<custAccount>*********</custAccount>

<List>

<Map>

<billNo>5************20220920000330079</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>9000000</billRangeEnd>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsAcceptancebleDraft">

49 / 293

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 10:11:56</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082200303540000CN</svrId>

<insId>CMBCINS2023071711204460</insId>

<List>

<Map>

<billNo>5************20220920000330079</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>9000000</billRangeEnd>

<transId>2816077229550730</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN2023071711204459</trnId>

</xDataBody>

</CMBC>

50 / 293

3.出票交付

民生银行银企直联

3.1.可出票交付票据列表查询(B2eNbsIssueDeliverPre)

本部分更新日期:2023-10-31

说明：

1.本接口用于查询可出票交付的票据列表

3.1.1.请求(B2eNbsIssueDeliverPre)

标记

是否

说明

必输

长

度

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

<minBillMoney>

Y

Y

N

N

N

N

N

客户技术请求流水号，同一客户请勿重复 64

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页 100

int

条）

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

<maxBillMoney> N

票据金额范围止

30

4

15,2

15,2

51 / 293

  
 
 
 
 
 
 
 
 
<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

3.1.2.响应(B2eNbsIssueDeliverPre)

标记

是否必

说明

输

<xDataBody>

服务消息集

民生银行银企直联

10

10

10

10

长

度

32

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billClass>

<billType>

N

Y

Y

Y

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

记录总数

票据(包)号码

30

票据种类： ME01 纸票 ME02 电

4

票

票据类型：AC01 银承 AC02 商 4

52 / 293

 
 
 
 
  
  
  
  
  
   
    
    
    
<hldrName>

<hldrAcctNo>

<hldrBankNo>

Y

Y

Y

承

持有人名称

持有人账号

持有人开户行行号

<isAllowSplitBill> Y

是否分包 0：否 1：是

<billMoney>

<billRangeStart>

<billRangeEnd>

<remitDt>

<dueDt>

<drwrName>

<drwrAcctName>

<drwrAcctNo>

<drwrBankName>

<drwrBankNo>

<pyeeName>

<pyeeAcctName>

<pyeeAcctNo>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

票面(包)金额

子票区间起始

子票区间截止

出票日

票面到期日

出票人全称

出票人账户名称

出票人账号

出票人开户行名称

出票人开户行行号

票面收款人全称

票面收款人名称

票面收款人账号

民生银行银企直联

60

60

12

30

12

12

12

8

8

180

180

32

180

12

180

180

32

53 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<acptName>

<billRemark>

Y

Y

Y

Y

Y

Y

Y

N

民生银行银企直联

票面收款人开户行名称

180

票面收款人开户行行号

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

承兑人全称

备注

12

32

180

180

12

180

255

</Map>

</List>

</xDataBody>

3.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsIssueDeliverPre">

<requestHeader>

<dtClient>2023-06-19 16:50:54</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

54 / 293

    
    
    
    
    
    
    
    
   
  
民生银行银企直联

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835330</trnId>

<insId>CMBCINS20230612143835331</insId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsIssueDeliverPre">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 10:59:06</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

55 / 293

<List>

<Map>

民生银行银企直联

<billId>****************</billId>

<billClass>ME02</billClass>

<billType>AC01</billType>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<billMoney>10000.00</billMoney>

<billRangeStart>1</billRangeStart>

<billRangeEnd>1000000</billRangeEnd>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>奚峰圣企业公司</pyeeName>

<pyeeAcctName>奚峰圣企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

56 / 293

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

民生银行银企直联

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<acptBankNo>************</acptBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<billStatus>NULL</billStatus>

</Map>

<Map>

<billId>****************</billId>

<billClass>ME02</billClass>

<billType>AC01</billType>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<billMoney>10000.00</billMoney>

<billRangeStart>1</billRangeStart>

<billRangeEnd>1000000</billRangeEnd>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrAcctNo>*********</drwrAcctNo>

57 / 293

<drwrBankName>中国民生银行股份有限公司北京中关村支行

民生银行银企直联

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>奚峰圣企业公司</pyeeName>

<pyeeAcctName>奚峰圣企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<acptBankNo>************</acptBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<billStatus>NULL</billStatus>

</Map>

</List>

<trnId>CMBCTRN20230612143835574</trnId>

</xDataBody>

</CMBC>

3.2.出票交付(B2eNbsIssueDeliver)

本部分更新日期:2023-10-31

说明：

1.本接口用于对票据做出票交付操作。

58 / 293

民生银行银企直联
2.出票后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

客户技术请求流水号，同一客户请勿

64

重复

客户业务请求流水号，同一业务请求

64

请勿重复

签约账号

询交易结果。

3.2.1.请求(B2eNbsIssueDeliver)

标记

是否

说明

必输

Y

Y

Y

<xDataBody>

<trnId>

<insId>

<custAccount>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

Y

子票区间截止

</Map>

</List>

</xDataBody>

长

度

32

30

12

12

59 / 293

  
 
 
 
 
  
   
   
   
  
 
民生银行银企直联

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

3.2.2.响应(B2eNbsIssueDeliver)

标记

是否必

说明

输

N

Y

Y

<xDataBody>

<svrId>

<trnId>

<insId>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

子票区间截止

交易流水号

错误码 1：成功 0：失败

返回信息 1：交易成功 0：具体

失败错误信息

</Map>

</List>

12

12

12

long

12

12

60 / 293

  
  
  
  
  
   
    
    
    
    
    
    
   
  
民生银行银企直联

</xDataBody>

3.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsIssueDeliver">

<requestHeader>

<dtClient>2023-07-13 15:43:04</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023071216503767</trnId>

<insId>CMBCINS2023071216503768</insId>

<custAccount>*********</custAccount>

<List>

<Map>

<billNo>6********************000916972</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>90000</billRangeEnd>

</Map>

</List>

61 / 293

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsIssueDeliver">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 13:57:17</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********030822007135400001D</svrId>

<insId>CMBCINS2023071711204486</insId>

<List>

<Map>

<billNo>6********************000916972</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>90000</billRangeEnd>

<transId>****************</transId>

<retCode>1</retCode>

62 / 293

民生银行银企直联

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN2023071711204485</trnId>

</xDataBody>

</CMBC>

4.背书

4.1.可背书票据列表查询(B2eNbsDraftEndorsementP

re)

本部分更新日期:2023-10-31

说明：

1.本接口用于查询可背书的票据列表。

4.1.1.请求(B2eNbsDraftEndorsementPre)

标记

是否必

说明

输

Y

Y

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

长

度

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

63 / 293

  
 
 
 
<pageSize>

<billNo>

<billType>

<minBillMoney>

N

N

N

N

每页数据条数（默认 10 条，最大每页

int

民生银行银企直联

100 条）

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

4.1.2.响应(B2eNbsDraftEndorsementPre)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<total>

输

N

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

记录总数

64 / 293

30

4

15,2

15,2

10

10

10

10

长

度

32

 
 
 
 
 
 
 
 
 
  
  
  
  
<List>

<Map>

<billNo>

<billClass>

<billType>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

Y

Y

Y

Y

Y

Y

民生银行银企直联

票据(包)号码

30

票据种类： ME01 纸票 ME02 电

4

票

票据类型： AC01 银承 AC02 商

4

承

持有人名称(背书人)

持有人账号(背书人)

持有人开户行行号(背书人)

<isAllowSplitBill> Y

是否分包 0：否 1：是

<billMoney>

<transAmt>

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<acptName>

<drwrName>

Y

Y

Y

Y

Y

Y

Y

Y

票面(包)金额

交易金额

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

承兑人全称

出票人全称

60

60

12

30

12

12

32

180

180

12

180

180

65 / 293

  
   
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<drwrAcctName>

<drwrAcctNo>

<drwrBankName>

<drwrBankNo>

<pyeeName>

<pyeeAcctName>

<pyeeAcctNo>

<pyeeBankName>

<pyeeBankNo>

<remitDt>

<dueDt>

<billRemark>

<billRangeStart>

<billRangeEnd>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

出票人名称

出票人账号

出票人开户行名称

出票人开户行行号

票面收款人全称

票面收款人名称

票面收款人账号

票面收款人开户行名称

票面收款人开户行行号

出票日

票面到期日

备注

子票区间起始

子票区间截止

</Map>

</List>

</xDataBody>

民生银行银企直联

180

32

180

12

180

180

32

180

12

8

8

255

12

12

66 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
   
  
4.1.3.例子

请求报文

民生银行银企直联

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsDraftEndorsementPre">

<requestHeader>

<dtClient>2023-06-05 15:55:39</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083655</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

67 / 293

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsDraftEndorsementPre">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:50:40</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>T77492023062513520488143540002RF</svrId>

<total>590</total>

<List>

<Map>

<billNo>5********************000705943</billNo>

<billRangeStart>1000001</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC01</billType>

68 / 293

民生银行银企直联

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<billClass>ME02</billClass>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>90000.00</billMoney>

<transAmt>90000.00</transAmt>

<drwrName>工商测试 001</drwrName>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国工商银行股份有限公司</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptBankNo>************</acptBankNo>

<billStatus>CS03</billStatus>

69 / 293

民生银行银企直联

</Map>

</List>

<trnId>CMBCTRN2023060210083655</trnId>

</xDataBody>

</CMBC>

4.2.背书申请(B2eNbsEndorsement)

本部分更新日期:2024-01-22

说明：

1.本接口用于对票据做背书操作。

2.背书后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询交易结果。

4.2.1.请求(B2eNbsEndorsement)

标记

<xDataBody>

<trnId>

<insId>

<receiverName>

<receiverAcctNo>

说明

是否

必输

长

度

Y

Y

Y

Y

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请

64

求请勿重复

接收人客户名称

接收人账号

180

32

70 / 293

  
 
 
 
 
民生银行银企直联

接收人开户行银行名称

接收人开户行行号

接收人开户行机构代码

不得转让标记 EM00：可再转

让 EM01：不得转让

签约帐号

Y

Y

N

Y

Y

<receiverBank>

<receiverBankNo>

<receiverBankCode>

<banEndrsmtMark>

<custAccount>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transAmt>

<applyRemark>

Y

N

N

子票区间截止

交易金额,不可分包的票不用录入

背书备注

</Map>

</List>

</xDataBody>

4.2.2.响应(B2eNbsEndorsement)

标记

是否必

说明

输

180

12

32

4

30

12

12

150

长

度

71 / 293

 
 
 
 
 
 
  
   
   
   
   
   
  
 
  
<xDataBody>

服务消息集

民生银行银企直联

Y

Y

Y

银行渠道交易流水号

32

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

<svrId>

<trnId>

<insId>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

</Map>

</List>

</xDataBody>

30

12

12

long

12

子票区间截止

交易流水号

错误码 1：成功 0：失败

返回信息 1：交易成功 0：具体

250

失败错误信息

72 / 293

  
  
  
  
   
    
    
    
    
    
    
   
  
民生银行银企直联

4.2.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsEndorsement">

<requestHeader>

<dtClient>2023-07-18 16:09:06</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230717112044163</trnId>

<insId>CMBCINS20230717112044164</insId>

<custAccount>*********</custAccount>

<receiverName>杜仁台企业公司</receiverName>

<receiverAcctNo>*********</receiverAcctNo>

<receiverBank>中国民生银行股份有限公司北京中关村支行</receiverBank>

<receiverBankNo>************</receiverBankNo>

<receiverBankCode>*********</receiverBankCode>

<banEndrsmtMark>EM00</banEndrsmtMark>

<List>

<Map>

<billNo>5************20230710001005462</billNo>

73 / 293

<billRangeStart>1</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<transAmt>100000.00</transAmt>

民生银行银企直联

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsEndorsement">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 16:09:09</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082201383540000I4</svrId>

<insId>CMBCINS20230717112044164</insId>

<List>

<Map>

74 / 293

<billNo>5************20230710001005462</billNo>

民生银行银企直联

<billRangeStart>1</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<transId>2816112952558262</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN20230717112044163</trnId>

</xDataBody>

</CMBC>

5.提示付款

5.1.可提示付款票据列表查询(B2eNbsSendAuthReceip

tPre)

本部分更新日期:2023-10-31
说明：

1.本接口用于查询可提示付款的票据列表

5.1.1.请求(B2eNbsSendAuthReceiptPre)

标记

是否

说明

必输

<xDataBody>

长

度

75 / 293

  
客户技术请求流水号，同一客户请勿重复 64

民生银行银企直联

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

<minBillMoney>

Y

Y

N

N

N

N

N

签约账号

当前页码(从 1 开始)，不传默认为 1

每页数据条数（默认 10 条，最大每页

100 条）

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

5.1.2.响应(B2eNbsSendAuthReceiptPre)

标记

是否

说明

必输

<xDataBody>

服务消息集

<svrId>

N

银行渠道交易流水号

32

int

int

30

4

15,2

15,2

10

10

10

10

长

度

32

76 / 293

 
 
 
 
 
 
 
 
 
 
 
 
  
  
民生银行银企直联

客户技术请求流水号，同一客

64

<trnId>

<total>

<List>

<Map>

<billNo>

<billClass>

Y

Y

Y

Y

户请勿重复

总条数

票据(包)号码

票据种类：

ME01 纸票

ME02 电票

<billType>

Y

票据类型：

AC01 银承

AC02 商承

持有人名称(背书人)

持有人账号(背书人)

持有人开户行行号(背书人)

是否分包 0：否 1：是

票面(包)金额

承兑人账号

承兑人账号名称

承兑人开户行名称

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<isAllowSplitBill>

<billMoney>

<acptAcctNo>

<acptAcctName>

<acptBankName>

Y

Y

Y

Y

Y

Y

Y

Y

30

4

4

60

60

12

30

12

32

180

180

77 / 293

  
  
  
   
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<acptBankNo>

<acptName>

<drwrName>

<drwrAcctName>

<drwrAcctNo>

<drwrBankName>

<drwrBankNo>

<pyeeName>

<pyeeAcctName>

<pyeeAcctNo>

<pyeeBankName>

<pyeeBankNo>

<remitDt>

<dueDt>

<billRangeStart>

<billRangeEnd>

<billRemark>

<transDt>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

承兑人开户行行号

承兑人全称

出票人全称

出票人名称

出票人账号

出票人开户行名称

出票人开户行行号

票面收款人全称

票面收款人名称

票面收款人账号

票面收款人开户行名称

票面收款人开户行行号

出票日

票面到期日

子票区间起始

子票区间截止

备注

交易日期

12

180

180

180

32

180

12

180

180

32

180

12

8

8

12

12

255

8

78 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<transFromAcctNo>

Y

申请人账号

<transFromBankNo> Y

申请人开户行行号

<acptBankCode>

<transAmt>

<transFromName>

<pyeeMemberId>

<drwrBankCode>

<pyeeBankCode>

<banEndrsmtMark>

<acptMemberId>

Y

Y

Y

Y

Y

Y

Y

Y

承兑人银行代码

交易金额

申请人名称

收款人业务办理渠道

出票人代码

票面收款人代码

不得转让禁止背书标记 EM00

可转让 EM01 不可转让

承兑人业务办理渠道

32

12

32

12

180

32

32

32

4

32

</Map>

</List>

</xDataBody>

5.1.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsSendAuthReceiptPre">

<requestHeader>

79 / 293

    
    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

<dtClient>2023-06-05 15:55:54</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083656</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsSendAuthReceiptPre">

80 / 293

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:51:06</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<total>881</total>

<List>

<Map>

<billNo>5********************000705943</billNo>

<billRangeStart>1000001</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC01</billType>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<billClass>ME02</billClass>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>90000.00</billMoney>

81 / 293

民生银行银企直联

<drwrName>工商测试 001</drwrName>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国工商银行股份有限公司</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptBankNo>************</acptBankNo>

<transDt>********</transDt>

<transFromAcctNo>0</transFromAcctNo>

<billStatus>CS03</billStatus>

<transFromBankNo>************</transFromBankNo>

<acptBankCode>*********</acptBankCode>

<transAmt>90000.00</transAmt>

<transFromName>中国民生银行股份有限公司北京分行

</transFromName>

<pyeeMemberId>100009</pyeeMemberId>

<drwrBankCode>*********</drwrBankCode>

82 / 293

<pyeeBankCode>*********</pyeeBankCode>

<banEndrsmtMark>EM00</banEndrsmtMark>

<acptMemberId>100001</acptMemberId>

民生银行银企直联

</Map>

</List>

</xDataBody>

</CMBC>

5.2.提示付款申请(B2eNbsSendAuthReceipt)

本部分更新日期:2023-10-31

说明：

1.本接口用于对票据做提示付款操作。

2.提示付款后使用《票据交易状态查询(B2eNbsQryDraftTransStatu

s)》查询交易结果。

5.2.1.请求(B2eNbsSendAuthReceipt)

标记

是否必

说明

长

度

<xDataBody>

<trnId>

<insId>

<remark>

输

Y

Y

N

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请

64

求请勿重复

备注

255

83 / 293

  
 
 
 
民生银行银企直联

32

30

12

12

12

长

度

32

<custAccount>

Y

签约账号

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<billMoney>

Y

Y

子票区间截止

票面(包)金额

</Map>

</List>

</xDataBody>

5.2.2.响应(B2eNbsSendAuthReceipt)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<insId>

输

Y

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

84 / 293

 
 
  
   
   
   
   
  
 
  
  
  
  
<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

民生银行银企直联

30

12

12

long

12

250

</Map>

</List>

</xDataBody>

5.2.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsSendAuthReceipt">

<requestHeader>

<dtClient>2023-07-18 10:24:19</dtClient>

85 / 293

  
   
    
    
    
    
    
    
   
  
民生银行银企直联

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>******</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023071711204465</trnId>

<insId>CMBCINS2023071711204466</insId>

<custAccount>*********</custAccount>

<remark>测试</remark>

<List>

<Map>

<billNo>6********************000910163</billNo>

<transAmt>200.00</transAmt>

<billRangeStart>1</billRangeStart>

<billRangeEnd>20000</billRangeEnd>

<billMoney>200.00</billMoney>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsSendAuthReceipt">

86 / 293

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 10:24:22</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082200353540000E4</svrId>

<insId>CMBCINS2023071711204466</insId>

<List>

<Map>

<billNo>6********************000910163</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>20000</billRangeEnd>

<transId>2816078474550789</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN2023071711204465</trnId>

</xDataBody>

</CMBC>

87 / 293

6.贴现

民生银行银企直联

6.1.可贴现票据列表查询(B2eNbsDraftEndorsementP

re)

本部分更新日期:2024-01-22

说明：

1.本接口用于查询可贴现的票据列表

6.1.1.请求(B2eNbsDraftEndorsementPre)

标记

是否必

说明

输

Y

Y

N

N

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

长

度

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

票据类型 AC01：银承 AC02：商承

30

4

88 / 293

  
 
 
 
 
 
 
<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

N

N

N

N

N

<endDate>

<minTransDt>

<maxTransDt>

<acceptor>

<oppName>

</xDataBody>

票面到期日止 yyyy-MM-dd

签收日期起:yyyy-MM-dd

签收日期止:yyyy-MM-dd

承兑人/行 名称

交易前手客户名称

6.1.2.响应(B2eNbsDraftEndorsementPre)

标记

说明

是否

必输

<xDataBody>

服务消息集

民生银行银企直联

15,2

15,2

10

10

10

10

10

10

150

150

长

度

32

<svrId>

<trnId>

<total>

N

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

记录总数

89 / 293

 
 
 
 
 
 
 
 
 
 
  
  
  
  
<List>

<Map>

<billNo>

<billClass>

<billType>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<isAllowSplitBill>

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctName>

<drwrAcctNo>

<drwrBankName>

<drwrBankNo>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

票据(包)号码

票据种类： ME01 纸

票 ME02 电票

票据类型： AC01 银承 AC02

商承

持有人名称(背书人)

持有人账号(背书人)

持有人开户行行号(背书人)

是否分包 0：否 1：是

出票日

汇票到期日

票面(包)金额

出票人全称

出票人名称

出票人账号

出票人开户行名称

出票人开户行行号

30

4

4

60

60

12

30

8

8

12

180

180

32

180

12

90 / 293

  
   
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<pyeeName>

<pyeeAcctName>

<pyeeAcctNo>

<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<acptName>

<billRangeStart>

<billRangeEnd>

<billRemark>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

收款人全称

收款人名称

收款人账号

收款人开户行名称

收款人开户行行号

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

承兑人全称

子票区间起始

子票区间截止

备注

<banEndrsmtMark> Y

禁止背书标记

EM00 可转让

EM01 不可转让

</Map>

</List>

</xDataBody>

民生银行银企直联

180

180

32

180

12

32

180

180

12

180

12

12

255

255

91 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

6.1.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsDraftEndorsementPre">

<requestHeader>

<dtClient>2023-06-05 15:55:39</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083655</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

92 / 293

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsDraftEndorsementPre">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:50:40</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>T77492023062513520488143540002RF</svrId>

<total>590</total>

<List>

<Map>

<billNo>5********************000705943</billNo>

<billRangeStart>1000001</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC01</billType>

93 / 293

民生银行银企直联

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<billClass>ME02</billClass>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>90000.00</billMoney>

<transAmt>90000.00</transAmt>

<drwrName>工商测试 001</drwrName>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国工商银行股份有限公司</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptBankNo>************</acptBankNo>

<billStatus>CS03</billStatus>

94 / 293

民生银行银企直联

</Map>

</List>

<trnId>CMBCTRN2023060210083655</trnId>

</xDataBody>

</CMBC>

6.2.贴现金额试算（B2eNbsCalEleDiscountIntrst）

本部分更新日期:2023-10-31

说明：

1.本接口用于试算贴现金额。

6.2.1.请求(B2eNbsCalEleDiscountIntrst)

标记

是否必输

说明

长

度

<xDataBody>

<trnId>

<insId>

<custAccount>

<List>

<Map>

Y

Y

Y

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

签约账号

32

30

95 / 293

<billNo>

Y

票据（包）号码

  
 
 
 
 
  
   
<billRangeStart> Y

子票区间起始

民生银行银企直联

<billRangeEnd>

<dueDt>

<transAmt>

</Map>

</List>

<rate>

<discDt>

<payType>

Y

Y

Y

Y

Y

Y

子票区间截止

票据到期日：yyyyMMdd

贴现金额

贴现利率（例：如利率为 10%，

10

输入 10）

贴现日期:yyyyMMdd

付息方式

1：买方付息

2：卖方付息

3：协议付息

<buyPayPcet>

Y (付息方

协议付息比例（协议付息比例是

式为协议付

指贴现人承担的比例） 传值为

息时必输）

0~100 的整数，例如 30%传值为

30

</xDataBody>

12

12

：

10

12

8

1

96 / 293

   
   
   
   
  
 
 
 
 
 
6.2.2.响应(B2eNbsCalEleDiscountIntrst)

民生银行银企直联

标记

是否必

说明

输

<xDataBody>

服务消息集

<svrId>

<trnId>

<insId>

<interestAmt>

<discSumAmt>

<List>

<Map>

<billNo>

<billRangeStart>

<billRangeEnd>

<transAmt>

<interest>

<discMoney>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

长

度

32

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

卖方总利息

贴现实付总金额

票据(包)号码

子票区间起始

子票区间截止

贴现金额

报文利息

实收金额

30

12

12

12

12

12

97 / 293

  
  
  
  
  
  
  
   
    
    
    
    
    
    
<galeDate>

Y

计息到期日

<interestCalDays> Y

计息天数

<delayDays>

<buyerInterest>

<salerInterest>

<retCode>

<retMsg>

Y

Y

Y

N

N

顺延天数

买方利息

卖方利息

交易状态

返回信息

民生银行银企直联

8

10

10

12

12

12

250

</Map>

</List>

</xDataBody>

6.2.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsCalEleDiscountIntrst">

<requestHeader>

<dtClient>2022-07-19 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

98 / 293

    
    
    
    
    
    
    
   
  
民生银行银企直联

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>50</trnId>

<insId>50</insId>

<rate>1</rate>

<discDt>********</discDt>

<payType>2</payType>

<custAccount>*********</custAccount>

<List>

<Map>

<billNo>5********************000704098</billNo>

<transAmt>1000.00</transAmt>

<dueDt>********</dueDt>

<billRangeStart>1</billRangeStart>

<billRangeEnd>100000</billRangeEnd>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsCalEleDiscountIntrst">

<responseHeader>

<status>

<code>0</code>

99 / 293

民生银行银企直联

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 14:22:34</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>UYF-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********030822008435400002K</svrId>

<interestAmt>0</interestAmt>

<insId>CMBCINS2023071711204494</insId>

<trnId>CMBCTRN2023071711204493</trnId>

<discSumAmt>1000.00</discSumAmt>

<List>

<Map>

<billNo>5********************000704098</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>100000</billRangeEnd>

<transAmt>1000</transAmt>

<interest>0.00</interest>

<discMoney>1000.00</discMoney>

<galeDate>********</galeDate>

<interestCalDays>0</interestCalDays>

<delayDays>0</delayDays>

<buyerInterest>0</buyerInterest>

<salerInterest>0.00</salerInterest>

100 / 293

民生银行银企直联

</Map>

</List>

</xDataBody>

</CMBC>

6.3.贴现申请(B2eNbsDraftDiscount)

本部分更新日期:2024-01-22

说明：

1.本接口用于对票据做贴现申请

2.交易后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询交易结果

3.向我行贴现,目前需线下与我行签署贴现协议，具体流程请联系客户经

理；向他行贴现，请向他行咨询具体流程。

6.3.1.请求(B2eNbsDraftDiscount)

标记

是否

说明

必输

长

度

<xDataBody>

<trnId>

<insId>

<custAccount>

<discountBank>

Y

Y

Y

Y

客户技术请求流水号，同一客户请勿

64

重复

客户技术请求流水号，同一客户请勿

64

重复

签约账号

贴入人（贴现行）行内外标识 :

32

1

101 / 293

  
 
 
 
 
民生银行银企直联

1: 向民生银行贴现

0：向他行贴现

<receiverAcctNo>

Y

贴入人（贴现行）账号 如贴入人为接

32

<receiverBank>

<receiverBankCode>

<receiverBankNo>

<aoAcctNo>

<aoAcctName>

<aoBankNo>

<aoAcctBankCode>

<banEndrsmtMark>

Y

N

Y

Y

Y

Y

N

Y

入行或接入财务公司，填写‘0’； 其

他情况下，填写贴入人账号

贴入人（贴现行）行名

贴入人（贴现行）参与者机构代码

贴入人（贴现行）行号

入账（收款）账号

入账（收款）账户名称

（需与贴现申请人客户名称一致）

入账（收款）行号

（与贴现申请人为同一银行）

入账账号参与者代码

禁止背书标记

EM00：可转让

EM01：不可转让

<discType>

Y

贴现类型 RM00：

买断式贴现 RM01：回购式贴现(暂不

支持)

<sttlmMk>

Y

结算方式

180

32

12

32

180

32

32

4

4

4

102 / 293

 
 
 
 
 
 
 
 
 
 
 
民生银行银企直联

ST01：票款对付（线上清算）

（DVP）（暂不支持）

ST02：纯票过户（线下清算）

（FOP）

贴现利率（0-99.9999，如利率 10%，

10

传 10）

8

1

贴现日:yyyyMMdd（申请日当日）

付息方式

1：买方付息

2：卖方付息

3：协议付息

<discRate>

<discDt>

<payType>

Y

Y

Y

<buyPayPcet>

Y (付

（协议付息比例，协议付息比例是指

5

息方

贴现人承担的比例） 传值为 0~100 的

式为

整数，例如 30%传值为 30

协议

付息

时必

输）

<List>

<Map>

<billNo>

Y

票据（包）号码

<billRangeStart> Y

子票区间起始

30

12

103 / 293

 
 
 
 
 
  
   
   
<billRangeEnd>

<dueDt>

<transAmt>

</Map>

</List>

<remark>

<redeemOpenDt>

<redeemDueDt>

<redeemRate>

<redeemRateType>

<redeemAmt>

</xDataBody>

Y

Y

Y

N

N

N

N

N

N

子票区间截止

汇票到期:yyyyMMdd

交易金额(贴现金额)

备注

赎回开放日，赎回式贴现选填

赎回截止日，赎回式贴现选填

赎回利率，赎回式贴现选填

赎回利率类型，赎回式贴现选填

赎回金额，赎回式贴现选填

6.3.2.响应(B2eNbsDraftDiscount)

标记

是否必

说明

民生银行银企直联

12

10

12

255

10

10

10

10

12

长

度

32

<xDataBody>

<svrId>

<trnId>

输

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

104 / 293

   
   
   
  
 
 
 
 
 
 
 
  
  
  
民生银行银企直联

客户业务请求流水号，同一业务

64

请求请勿重复

贴现张数

实付贴现金额

Y

Y

Y

<insId>

<totalSize>

<totalMoney>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

返回信息

1：交易成功

0：具体失败错误信息

</Map>

</List>

</xDataBody>

30

12

12

long

12

250

105 / 293

  
  
  
  
   
    
    
    
    
    
    
   
  
民生银行银企直联

6.3.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsDraftDiscount">

<requestHeader>

<dtClient>2023-07-18 16:26:51</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230717112044177</trnId>

<insId>CMBCINS20230717112044178</insId>

<custAccount>*********</custAccount>

<discountBank>1</discountBank>

<receiverAcctNo>0</receiverAcctNo>

<receiverBank>中国民生银行股份有限公司广州分行营业部</receiverBank>

<receiverBankCode>*********</receiverBankCode>

<receiverBankNo>************</receiverBankNo>

<aoAcctNo>*********</aoAcctNo>

<aoAcctName>中国民生银行股份有限公司北京中关村支行</aoAcctName>

<aoBankNo>1</aoBankNo>

<aoAcctBankCode>*********</aoAcctBankCode>

106 / 293

<banEndrsmtMark>EM00</banEndrsmtMark>

民生银行银企直联

<discType>RM00</discType>

<sttlmMk>ST02</sttlmMk>

<discRate>1</discRate>

<discDt>********</discDt>

<payType>2</payType>

<buyPayPcet>1</buyPayPcet>

<remark>******** 测试</remark>

<redeemOpenDt></redeemOpenDt>

<redeemDueDt></redeemDueDt>

<redeemRate></redeemRate>

<redeemRateType></redeemRateType>

<redeemAmt></redeemAmt>

<List>

<Map>

<billNo>5************20230710001005479</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<dueDt>********</dueDt>

<transAmt>100000.00</transAmt>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

107 / 293

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="B2eNbsDraftDiscount">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 16:26:54</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082201473540000K0</svrId>

<insId>CMBCINS20230717112044178</insId>

<trnId>CMBCTRN20230717112044177</trnId>

<totalSize>1</totalSize>

<List>

<Map>

<billNo>5************20230710001005479</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<transId>****************</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

108 / 293

民生银行银企直联

</List>

</xDataBody>

</CMBC>

6.4.贴现线上协议查询(B2eNbsQueryDiscountTreaty)

本部分更新日期:2024-01-22

说明：

1.本接口用于查询贴现成功后生成的贴现数据列表

6.4.1.请求(B2eNbsQueryDiscountTreaty)

标记

是否必

说明

输

<xDataBody>

长

度

<trnId>

Y

客户技术请求流水号，同一客户请勿重

64

复

<custAccount> Y

签约账号

<treatyNo>

<pageNo>

<pageSize>

<discDtB>

<discDtE>

N

N

N

N

N

协议编号

当前页码(从 1 开始)，不传默认为 1

每页数据条数（默认 10 条，最大每页

100 条）

贴现申请开始日期 yyyyMMdd

贴现申请结束日期 yyyyMMdd

32

50

int

int

8

8

109 / 293

  
 
 
 
 
 
 
 
N

N

N

<payMoneyB>

<payMoneyE>

<insId>

</xDataBody>

贴现申请开始金额

贴现申请结束金额

原贴现申请客户业务请求流水号

6.4.2.响应(B2eNbsQueryDiscountTreaty)

标记

是否必

说明

输

<xDataBody>

服务消息集

民生银行银企直联

15,2

15,2

10

长

度

32

<svrId>

<trnId>

<total>

<List>

<Map>

<discMoney>

<treatyNo>

<insId>

<discDt>

Y

Y

Y

Y

Y

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

总条数

int

贴现申请金额

协议编号

15,2

12

原贴现申请客户业务请求流水

12

号

贴现申请日 yyyyMMdd

8

110 / 293

 
 
 
  
  
  
  
  
   
    
    
    
    
<payMoney>

<rate>

<receiverName>

Y

Y

Y

贴现实付金额

贴现利率

贴入人名称

<receiverBankNo> Y

贴入行行号

<settlementMark>

Y

线上清算

民生银行银企直联

15,2

12

500

12

6

</Map>

</List>

</xDataBody>

6.4.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryDiscountTreaty">

<requestHeader>

<dtClient>2023-08-21 17:03:36</dtClient>

<clientId>**********</clientId>

<userId>**********050</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

111 / 293

    
    
    
    
    
   
  
民生银行银企直联

<trnId>CMBCTRN2023081614523064</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<btchNo>NX********1654480000000488802</btchNo>

<treatyNo>********155824816**********07060086578300054421254</

treatyNo>

<discDtB>********</discDtB>

<discDtE>********</discDtE>

<payMoneyB>0</payMoneyB>

<payMoneyE>100</payMoneyE>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryDiscountTreaty">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-21 17:17:08</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

112 / 293

</responseHeader>

<xDataBody>

<svrId>3130020230821018273004235400000J</svrId>

民生银行银企直联

<total>1</total>

<List>

<Map>

<discMoney>43.86</discMoney>

<treatyNo>********155824816**********07060086578300054421254</

treatyNo>

<insId>NX********1654480000000488802</insId>

<discDt>********</discDt>

<payMoney>43.86</payMoney>

<rate>0.090000</rate>

<receiverName>中国民生银行股份有限公司</receiverName>

<receiverBankNo>************</receiverBankNo>

<settlementMark>ST02</settlementMark>

</Map>

</List>

<trnId>CMBCTRN2023081614523064</trnId>

</xDataBody>

</CMBC>

6.5.贴现线上协议明细查询(B2eNbsQueryDiscountTre

atyDetails)

本部分更新日期:2023-10-31

说明：

113 / 293

1.本接口用于查询贴现成功后生成的贴现明细信息

2.在使用本接口前，应先使用《贴现线上协议查询(B2eNbsQueryDisco

民生银行银企直联

untTreaty)》获取贴现列表

6.5.1.请求(B2eNbsQueryDiscountTreatyDetails)

标记

是否必

说明

输

<xDataBody>

长

度

<trnId>

Y

客户技术请求流水号，同一客户请勿重

64

复

<custAccount> Y

签约账号

<treatyNo>

<pageNo>

<pageSize>

Y

N

N

</xDataBody>

协议编号

当前页码(从 1 开始)，不传默认为 1

每页数据条数（默认 10 条，最大每页

100 条）

32

50

int

int

6.5.2.响应(B2eNbsQueryDiscountTreatyDetails)

标记

是否必

说明

长度

输

<xDataBody>

服务消息集

<svrId>

Y

银行渠道交易流水号

32

114 / 293

  
 
 
 
 
 
  
  
<trnId>

<total>

<List>

<Map>

<billNo>

<billType>

<billClass>

<issueDt>

<dueDt>

<billMoney>

<rate>

<discInterest>

<conferNo>

<invoiceNo>

<remitter>

<acceptor>

<curStatusName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

Y

Y

Y

Y

民生银行银企直联

客户技术请求流水号，同一客

64

户请勿重复

总条数

票据号码

票据类型

票据种类

出票日

票面到期日

票面金额

贴现利率

贴现利息

合同文本编号

发票号

出票人名称

承兑人名称

票据状态

int

30

4

4

8

8

15,2

12

15，

2

50

50

300

300

50

115 / 293

  
  
  
   
    
    
    
    
    
    
    
    
    
    
    
    
    
<discMoney>

<payMoney>

Y

Y

贴现申请金额

贴现实付金额

<receiverName>

Y

贴入人名称

<receiverBankNo> Y

贴入行行号

<billId>

<transId>

<discDt>

<processResult>

<billRangeStart>

<billRangeEnd>

Y

Y

Y

Y

Y

Y

票据 ID

交易 ID

贴现日期

处理结果

子票区间起始

子票区间截止

民生银行银企直联

15，

2

15，

2

500

12

long

long

8

300

12

12

</Map>

</List>

</xDataBody>

6.5.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryDiscountTreatyDetails">

<requestHeader>

116 / 293

    
    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

<dtClient>2023-08-21 18:49:29</dtClient>

<clientId>**********</clientId>

<userId>**********050</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023081614523070</trnId>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<custAccount>*********</custAccount>

<treatyNo>********155824816**********07060086578300054421254</

treatyNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryDiscountTreatyDetails">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

117 / 293

<dtServer>2023-08-21 19:13:06</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300202308210182730054354000002</svrId>

<total>1</total>

<List>

<Map>

<billNo>5********************000671825</billNo>

<billType>AC01</billType>

<billClass>ME02</billClass>

<issueDt>********</issueDt>

<dueDt>********</dueDt>

<billMoney>44.00</billMoney>

<rate>0.090000</rate>

<discInterest>0.14</discInterest>

<remitter>工商测试 001</remitter>

<acceptor>中国工商银行股份有限公司</acceptor>

<discMoney>43.86</discMoney>

<payMoney>43.86</payMoney>

<receiverName>中国民生银行股份有限公司</receiverName>

<receiverBankNo>************</receiverBankNo>

<billId>****************</billId>

<transId>****************</transId>

118 / 293

民生银行银企直联

<discDt>********</discDt>

<processResult>贴现成功</processResult>

<billRangeStart>580001</billRangeStart>

<billRangeEnd>584400</billRangeEnd>

</Map>

</List>

<trnId>CMBCTRN2023081614523070</trnId>

</xDataBody>

</CMBC>

7.质押

7.1.可质押票据列表查询(B2eNbsQueryImpawnApply)

本部分更新日期:2023-10-31

说明：

1.本接口用于查询可贴现的票据列表

7.1.1.请求(B2eNbsQueryImpawnApply)

标记

是否必

说明

输

<xDataBody>

长

度

<trnId>

Y

客户技术请求流水号，同一客户请勿重

64

复

119 / 293

  
 
<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

<minBillMoney>

Y

N

N

N

N

N

民生银行银企直联

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

7.1.2.响应(B2eNbsQueryImpawnApply)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

输

N

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客

30

4

15,2

15,2

10

10

10

10

长

度

32

64

120 / 293

 
 
 
 
 
 
 
 
 
 
 
  
  
  
<total>

<discSumAmt>

<List>

<Map>

<billNo>

<billType>

<billClass>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

户请勿重复

总条数

int

贴现实付总金额

票据(包)号码

票据类型 AC01：银承 AC02：

商承

票据介质 ME01：纸票 ME02：

电票

持有人名称

持有人账号

持有人开户行行号

<isAllowSplitBill> Y

是否可分包 0-否 1-是

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

Y

Y

Y

Y

Y

出票日期

汇票到期日

票据（包）金额

出票人全称

出票人账号

30

4

4

60

60

12

1

8

8

12

180

32

121 / 293

  
  
  
   
    
    
    
    
    
    
    
    
    
    
    
    
Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankName>

<acptName>

<billRangeStart>

<billRangeEnd>

<billRemark>

<transAmt>

</Map>

出票人账户名称

出票人开户行行号

出票人开户行行名

收款人全称

收款人账号

收款人账户名称

收款人开户行行名

收款人开户行行号

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行名称

承兑人全称

子票区间起始

子票区间截止

备注

交易金额

民生银行银企直联

180

12

180

180

32

180

180

12

32

180

32

180

180

12

12

256

12

122 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
   
民生银行银企直联

</List>

</xDataBody>

7.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryImpawnApply">

<requestHeader>

<dtClient>2023-06-19 11:19:10</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835284</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>2</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

123 / 293

  
<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryImpawnApply">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-19 16:15:59</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<billNo>5********************000916313</billNo>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<dueDt>********</dueDt>

<billMoney>466.00</billMoney>

<drwrName>工商测试 001</drwrName>

<drwrAcctNo>123456</drwrAcctNo>

124 / 293

民生银行银企直联

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

<acptBankNo>************</acptBankNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptName>中国工商银行股份有限公司</acptName>

<billRangeStart>20001</billRangeStart>

<billRangeEnd>66600</billRangeEnd>

<billStatus>CS03</billStatus>

<transAmt>466.00</transAmt>

</Map>

<Map>

<billNo>5********************000916321</billNo>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

125 / 293

民生银行银企直联

<isAllowSplitBill>1</isAllowSplitBill>

<dueDt>********</dueDt>

<billMoney>666.00</billMoney>

<drwrName>工商测试 001</drwrName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

<acptBankNo>************</acptBankNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptName>中国工商银行股份有限公司</acptName>

<billRangeStart>1</billRangeStart>

<billRangeEnd>66600</billRangeEnd>

<billStatus>CS03</billStatus>

<transAmt>666.00</transAmt>

</Map>

</List>

<trnId>CMBCTRN20230612143835304</trnId>

126 / 293

民生银行银企直联

</xDataBody>

</CMBC>

7.2.质押申请(B2eNbsDraftImpawn)

本部分更新日期:2023-10-31

说明：

1.本接口用于对可质押的票据进行质押申请

2.质押申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatu

s)》查询交易结果

7.2.1.请求(B2eNbsDraftImpawn)

标记

是否必

说明

<xDataBody>

<trnId>

<insId>

<custAccount>

<receiverName>

<receiverAcctNo>

<receiverBankNo>

<receiverBank>

输

Y

Y

Y

Y

Y

Y

Y

长

度

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

签约账号

质权人名称

质权人账号

质权人开户行行号

质权人开户行行名

32

180

32

12

180

127 / 293

  
 
 
 
 
 
 
 
<receiverBankCode>

N

质权人开户机构代码

32

民生银行银企直联

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transAmt>

Y

Y

子票区间截止

交易金额

</Map>

</List>

</xDataBody>

7.2.2.响应(B2eNbsDraftImpawn)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<insId>

输

Y

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

128 / 293

30

12

12

12

长

度

32

 
  
   
    
    
    
    
   
  
  
  
  
  
<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

民生银行银企直联

30

12

12

long

12

250

</Map>

</List>

</xDataBody>

7.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftImpawn">

<requestHeader>

<dtClient>2023-07-14 10:09:03</dtClient>

129 / 293

  
   
    
    
    
    
    
    
   
  
民生银行银企直联

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023071216503797</trnId>

<insId>CMBCINS2023071216503798</insId>

<custAccount>*********</custAccount>

<receiverName>1</receiverName>

<receiverAcctNo>1</receiverAcctNo>

<receiverBankNo>************</receiverBankNo>

<receiverBank>中国民生银行股份有限公司广州分行营业部</receiverBank>

<receiverBankCode>*********</receiverBankCode>

<List>

<Map>

<billNo>5************20220727000094911</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>198000</billRangeEnd>

<transAmt>1980.00</transAmt>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

130 / 293

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftImpawn">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 15:07:08</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********0308220106354000089</svrId>

<insId>CMBCINS20230717112044120</insId>

<List>

<Map>

<billNo>5************20220727000094911</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>198000</billRangeEnd>

<transId>****************</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

131 / 293

<trnId>CMBCTRN20230717112044119</trnId>

民生银行银企直联

</xDataBody>

</CMBC>

8.解质押

8.1.可解质押票据列表查询（B2eNbsDisImpawnApplly

Pre）

本部分更新日期:2023-10-31

说明：

本接口查询可解除质押的票据列表

8.1.1.请求(B2eNbsDisImpawnAppllyPre)

标记

是否必

说明

输

Y

Y

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

长

度

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

132 / 293

  
 
 
 
 
民生银行银企直联

<billNo>

<billType>

<minBillMoney>

N

N

N

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

8.1.2.响应(B2eNbsDisImpawnAppllyPre)

标记

说明

是否

必输

<xDataBody>

服务消息集

30

4

15,2

15,2

10

10

10

10

长

度

32

<svrId>

<trnId>

<insId>

<total>

N

Y

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

客户业务请求流水号，同一业

64

务请求请勿重复

总个数

int

133 / 293

 
 
 
 
 
 
 
 
  
  
  
  
  
<List>

<Map>

民生银行银企直联

<transFromName>

Y

出质人名称

180

<transFromAcctNo> Y

出质人账号

<transDt>

<billNo>

<billType>

Y

Y

Y

质押签收日期

票据(包)号码

票据类型

AC01：银承

AC02：商承

<billClass>

Y

票据介质

ME01：纸票

ME02：电票

持有人名称

持有人账号

持有人开户行行号

是否可分包 0-否 1-是

出票日期

汇票到期日

票据（包）金额

出票人全称

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<isAllowSplitBill>

<remitDt>

<dueDt>

<billMoney>

<drwrName>

Y

Y

Y

Y

Y

Y

Y

Y

32

8

30

4

4

60

60

12

1

8

8

12

180

134 / 293

  
   
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankName>

<acptName>

<billRangeStart>

<billRangeEnd>

<billRemark>

<transAmt>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行行名

收款人全称

收款人账号

收款人账户名称

收款人开户行行名

收款人开户行行号

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行名称

承兑人全称

子票区间起始

子票区间截止

备注

交易金额

民生银行银企直联

32

180

12

180

180

32

180

180

12

32

180

12

180

180

12

12

256

135 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

</Map>

</List>

</xDataBody>

8.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDisImpawnAppllyPre">

<requestHeader>

<dtClient>2023-06-19 17:33:45</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835338</trnId>

<insId>CMBCINS20230612143835339</insId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

136 / 293

   
  
</CMBC>

响应报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDisImpawnAppllyPre">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-19 17:34:44</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<total>0</total>

<List />

</xDataBody>

</CMBC>

8.2.解质押申请(B2eNbsDisImpawnApplly)

本部分更新日期:2023-10-31

说明：

1.本接口用于对可解除质押的票据进行解除质押申请

137 / 293

民生银行银企直联
2.解除质押申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatu

s)》查询交易结果

8.2.1.请求(B2eNbsDisImpawnApplly)

标记

是否必

说明

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请

64

求请勿重复

签约账号

输

Y

Y

Y

<xDataBody>

<trnId>

<insId>

<custAccount>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transAmt>

Y

Y

子票区间截止

交易金额

</Map>

</List>

</xDataBody>

长

度

32

30

12

12

12

138 / 293

  
 
 
 
 
  
   
   
   
   
  
 
民生银行银企直联

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

8.2.2.响应(B2eNbsDisImpawnApplly)

标记

是否必

说明

输

Y

Y

Y

<xDataBody>

<svrId>

<trnId>

<insId>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

返回信息

1：交易成功

30

12

12

long

12

250

0：具体失败错误信息

139 / 293

  
  
  
  
  
   
    
    
    
    
    
    
民生银行银企直联

</Map>

</List>

</xDataBody>

8.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDisImpawnApplly">

<requestHeader>

<dtClient>2023-06-25 14:01:05</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN********14553435</trnId>

<insId>CMBCINS********14553436</insId>

<custAccount>*********</custAccount>

<List>

<Map>

<billNo>5************20221117000384500</billNo>

<billRangeStart>1</billRangeStart>

140 / 293

   
  
<billRangeEnd>*********</billRangeEnd>

<transAmt>1000006.00</transAmt>

民生银行银企直联

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDisImpawnApplly">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:59:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T77492023062514010488203540007MC</svrId>

<insId>CMBCINS********14553436</insId>

<List>

<Map>

<billNo>5************20221117000384500</billNo>

141 / 293

民生银行银企直联

<billRangeStart>1</billRangeStart>

<billRangeEnd>*********</billRangeEnd>

<transId>2781143964020807</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN********14553435</trnId>

</xDataBody>

</CMBC>

9.保证申请

9.1.可保证申请票据列表查询(B2eNbsQueryGuarantee

ableDraft)

本部分更新日期:2023-10-31

说明：

本接口查询可进行保证申请的票据列表

9.1.1.请求(B2eNbsQueryGuaranteeableDraft)

标记

是否必

说明

输

<xDataBody>

长

度

<trnId>

Y

客户技术请求流水号，同一客户请勿重 64

142 / 293

  
 
民生银行银企直联

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

Y

N

N

N

N

100 条）

票号

票据类型

AC01：银承

AC02：商承

<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

<assuType>

N

N

</xDataBody>

票面到期日止 yyyy-MM-dd

保证类型

1：出票保证

2：承兑保证

3：背书保证

30

4

15,2

15,2

10

10

10

10

1

143 / 293

 
 
 
 
 
 
 
 
 
 
 
 
9.1.2.响应(B2eNbsQueryGuaranteeableDraft)

民生银行银企直联

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<List>

<Map>

<billNo>

<billRangeStart>

<billRangeEnd>

<billType>

<billClass>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

输

N

Y

Y

Y

Y

Y

Y

Y

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客

户请勿重复

票据(包)号码

子票据区间开始

子票据区间截止

票据类型 AC01：银承 AC02：

商承

票据介质 ME01：纸票 ME02：

电票

持有人名称

持有人账号

持有人开户行行号

<isAllowSplitBill> Y

是否可分包

长

度

32

64

30

12

12

4

4

60

60

12

1

144 / 293

  
  
  
  
   
    
    
    
    
    
    
    
    
    
0-否

1-是

出票日期

汇票到期日

票据（包）金额

出票人全称

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行行名

收款人全称

收款人账号

收款人账户名称

收款人开户行行名

收款人开户行行号

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行名称

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

8

8

12

180

32

180

12

180

180

32

180

180

12

32

180

12

180

145 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<acptName>

<billRemark>

Y

N

承兑人全称

备注

民生银行银企直联

180

256

</Map>

</List>

</xDataBody>

9.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryGuaranteeableDraft">

<requestHeader>

<dtClient>2023-06-25 11:01:30</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835578</trnId>

<insId>CMBCINS20230612143835579</insId>

<custAccount>*********</custAccount>

<assuType>1</assuType>

<pageNo>1</pageNo>

146 / 293

    
    
   
  
民生银行银企直联

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryGuaranteeableDraft">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 11:01:31</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<billNo>5********************000329435</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>8000000</billRangeEnd>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrId>****************</hldrId>

147 / 293

民生银行银企直联

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>80000.00</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<pyeeName>韦杰文企业公司</pyeeName>

<pyeeAcctName>韦杰文企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankNo>************</acptBankNo>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

148 / 293

民生银行银企直联

</Map>

</List>

<total>55</total>

<trnId>CMBCTRN20230612143835578</trnId>

</xDataBody>

</CMBC>

9.2.保证申请(B2eNbsGuaranteeRequest)

本部分更新日期:2023-10-31

说明：

1.本接口用于对可保证的票据进行保证申请

2.保证申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatu

s)》查询交易结果

9.2.1.请求(B2eNbsGuaranteeRequest)

标记

是否必

说明

长

度

<xDataBody>

<trnId>

<insId>

<custAccount>

输

Y

Y

Y

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请

64

求请勿重复

签约账号

32

149 / 293

  
 
 
 
民生银行银企直联

保证人全称

保证人账户名称

保证人账号

保证人开户行行号

保证人开户行行名

保证人开户行机构代码

保证类型

1：出票保证

2：承兑保证

3：背书保证

<guarntrName>

<guarntrAcctName>

<guarntrAcctNo>

<guarntrBankNo>

<guarntrBankName>

<receiverBankCode>

<assuType>

Y

Y

Y

Y

Y

N

Y

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transAmt>

Y

N

子票区间截止

交易金额 assuType=3,背书保证时必

输

</Map>

</List>

</xDataBody>

180

180

32

12

180

1

30

12

12

12

150 / 293

 
 
 
 
 
 
 
 
  
   
   
   
   
  
 
民生银行银企直联

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

9.2.2.响应(B2eNbsGuaranteeRequest)

标记

是否必

说明

输

Y

Y

Y

<xDataBody>

<svrId>

<trnId>

<insId>

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票据区间开始

<billRangeEnd>

<transId>

<retCode>

<retMsg>

Y

Y

Y

Y

子票据区间截止

交易流水号

错误码

1：成功

0：失败

返回信息

1：交易成功

0：具体失败错误信息

30

12

12

long

long

250

151 / 293

  
  
  
  
  
   
    
    
    
    
    
    
民生银行银企直联

</Map>

</List>

</xDataBody>

9.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsGuaranteeRequest">

<requestHeader>

<dtClient>2023-06-15 10:39:34</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<custAccount>*********</custAccount>

<trnId>CMBCTRN20230612143835107</trnId>

<insId>CMBCINS20230612143835108</insId>

<guarntrName>殷林聪企业公司</guarntrName>

<guarntrAcctName>中国民生银行股份有限公司北京分行

</guarntrAcctName>

<guarntrAcctNo>************</guarntrAcctNo>

152 / 293

   
  
民生银行银企直联

<guarntrBankNo>************</guarntrBankNo>

<guarntrBankName>中国民生银行股份有限公司北京分行

</guarntrBankName>

<receiverBankCode>************</receiverBankCode>

<assuType>1</assuType>

<List>

<Map>

<billNo>5********************000396603</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>88811</billRangeEnd>

<transAmt>8889</transAmt>

<billMoney>6668</billMoney>

<isAllowSplitBill>0</isAllowSplitBill>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsGuaranteeRequest">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

153 / 293

<dtServer>2023-06-15 10:39:42</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********041041001835400001N</svrId>

<insId>CMBCINS20230612143835108</insId>

<trnId>CMBCTRN20230612143835107</trnId>

<list>

<Map>

<billNo>5********************000396603</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>88811</billRangeEnd>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</list>

</xDataBody>

</CMBC>

10.追索

10.1.可追索通知票据列表查询（B2eNbsRecourseNotic

ePre）

本部分更新日期:2023-10-31

154 / 293

民生银行银企直联

说明：

可追索通知票据列表查询（B2eNbsRecourseNoticePre）

10.1.1.请求(B2eNbsRecourseNoticePre)

标记

是否必

说明

输

Y

Y

N

N

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

<billType>

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

票据类型

AC01：银承

AC02：商承

<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

长

度

30

4

15,2

15,2

10

10

155 / 293

  
 
 
 
 
 
 
 
 
 
 
<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

10.1.2.响应(B2eNbsRecourseNoticePre)

标记

是否必

说明

输

<xDataBody>

服务消息集

民生银行银企直联

10

10

长

度

32

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billId>

<billRangeStart>

<billRangeEnd>

<billType>

N

Y

Y

Y

Y

Y

Y

Y

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

记录总数

int

票据(包)号码

票据流水号

子票据区间开始

子票据区间截止

票据类型

AC01：银承

30

long

12

12

4

156 / 293

 
 
  
  
  
  
  
   
    
    
    
    
    
<billClass>

<hldrName>

<hldrAcctNo>

<hldrBankNo>

Y

Y

Y

Y

AC02：商承

票据介质

ME01：纸票

ME02：电票

持有人名称

持有人账号

持有人开户行行号

<isAllowSplitBill> Y

是否可分包

0-否

1-是

出票日期

汇票到期日

票据（包）金额

出票人全称

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行行名

收款人全称

收款人账号

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<pyeeName>

<pyeeAcctNo>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

4

60

60

12

1

8

8

12

180

32

180

12

180

180

32

157 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankName>

<acptName>

<billRemark>

<rcrsTp>

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

民生银行银企直联

180

180

12

32

180

12

180

180

256

4

收款人账户名称

收款人开户行行名

收款人开户行行号

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行名称

承兑人全称

备注

追索类型

RT00 拒付追索

RT01 非拒付追索

</Map>

</List>

</xDataBody>

10.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsRecourseNoticePre">

<requestHeader>

158 / 293

    
    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

<dtClient>2023-06-06 14:26:51</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083663</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsRecourseNoticePre">

159 / 293

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:53:09</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T774920230625135504881635400044A</svrId>

<total>900</total>

<List>

<Map>

<billNo>5********************000705943</billNo>

<billId>****************</billId>

<billRangeStart>1000001</billRangeStart>

<billRangeEnd>********</billRangeEnd>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<remitDt>********</remitDt>

160 / 293

民生银行银企直联

<dueDt>********</dueDt>

<billMoney>90000.00</billMoney>

<drwrName>工商测试 001</drwrName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

<acptBankNo>************</acptBankNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptName>中国工商银行股份有限公司</acptName>

<rcrsTp>RT01</rcrsTp>

</Map>

</List>

<trnId>CMBCTRN2023060210083663</trnId>

</xDataBody>

</CMBC>

161 / 293

民生银行银企直联
10.2.查询可被追索对象(B2eNbsCancleableRecourse

Query)

本部分更新日期:2023-10-31

说明：

本接口用于根据票据（包）号和签约账号查询多个可追索人信息。

10.2.1.请求(B2eNbsCancleableRecourseQuery)

标记

是否必

说明

输

Y

Y

N

N

Y

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

<billRangeStart> Y

子票区间起始

<billRangeEnd>

Y

子票区间截止

</xDataBody>

长

度

30

12

12

162 / 293

  
 
 
 
 
 
 
 
10.2.2.响应(B2eNbsCancleableRecourseQuery)

民生银行银企直联

标记

是否必

说明

输

<xDataBody>

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

<svrId>

<trnId>

<billInfo>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

N

Y

Y

Y

Y

Y

Y

请勿重复

票据正面信息

出票人全称

出票人账号

出票人账户名称

出票人开户行行号

<drwrBankName> Y

出票人开户行行名

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

Y

Y

Y

收款人全称

收款人账号

收款人账户名称

<pyeeBankName> Y

收款人开户行行名

<pyeeBankNo>

<acptAcctNo>

Y

Y

收款人开户行行号

承兑人账号

长

度

32

180

32

180

12

180

180

32

180

180

12

32

163 / 293

  
  
  
  
   
   
   
   
   
   
   
   
   
   
   
<acptAcctName>

<acptBankNo>

<acptBankName>

<acptName>

</billInfo>

<endoList>

<Map>

<custName>

<acctNo>

<acctName>

<bankNo>

<bankName>

<socCode>

<memeberId>

<distType>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

</Map>

</endoList>

</xDataBody>

承兑人账户名称

承兑人开户行行号

承兑人开户行名称

承兑人全称

票据正面信息

被追索人名称

被追索人账号

被追索人账户名称

被追索人开户行行号

被追索人开户行行名

民生银行银企直联

180

12

180

180

60

32

150

12

150

组织机构代码/统一社会信用代码 32

接入机构代码

识别类型

32

10

164 / 293

   
   
   
   
  
  
   
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

10.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsCancleableRecourseQuery">

<requestHeader>

<dtClient>2023-06-06 15:46:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083669</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo>5************20230613000907147</billNo>

<billType></billType>

<billRangeStart>2000001</billRangeStart>

<billRangeEnd>4000000</billRangeEnd>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

165 / 293

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsCancleableRecourseQuery">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:53:35</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083669</trnId>

<endoList>

<Map>

<custName>南京银行股份有限公司</custName>

<bankNo>************</bankNo>

<bankName>南京银行股份有限公司</bankName>

<acctNo>0</acctNo>

<memeberId>100016</memeberId>

</Map>

</endoList>

<billInfo>

<drwrAcctNo>123456</drwrAcctNo>

166 / 293

民生银行银企直联

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

<acptBankNo>************</acptBankNo>

<acptBankName>南京银行股份有限公司</acptBankName>

<acptName>南京银行股份有限公司</acptName>

</billInfo>

</xDataBody>

10.3.追索通知(B2eNbsRecourseRequest)

本部分更新日期:2023-10-31

说明：

1.本功能用于客户向可追索人发出清偿的请求。

2.被追索人信息可以通过查询可被追索对象(B2eNbsCancleableRecourseQ

uery)交易获取。

3.追索通知申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatu

s)》查询交易结果。

167 / 293

10.3.1.请求(B2eNbsRecourseRequest)

标记

说明

是否

必输

民生银行银企直联

长

度

<xDataBody>

<trnId>

<insId>

<custAccount>

<rcvgName>

<rmrkByPropsr>

<rcrsRsnCd>

<rcvgBankNo>

<rcvgOrgCode>

<rcvgAcctNo>

<rcvgDistType>

Y

Y

Y

Y

Y

N

Y

N

Y

N

客户技术请求流水号，同一客户请勿重

64

复

客户业务请求流水号，同一业务请求请

64

勿重复

签约账号

被追索人名称

追索备注

追索理由代码

被追索人开户行行号

被追索人统一社会信用代码：

1、追索对象是企业时被追人人统一社会

信用代码必输。

2、追索对象是银行时被追人人统一社会

信用代码非必输。

被追索人账号

被追索人识别类型

DT01：票据账户

32

180

255

4

12

32

32

4

168 / 293

  
 
 
 
 
 
 
 
 
 
 
DT02：银行账户

民生银行银企直联

<rcvgAcctName> Y

被追索人账户名称

<rcrsTp>

Y

被追索人标识

180

4

RT00：拒付追索

RT01：非拒付追索

<rcvgMemberId> N

业务办理渠道为供应链平台是必输，其

6

30

12

12

长

度

32

他不可输

票据(包)号码

子票区间起始

子票区间截止

<billNo>

<billRangeStart>

<billRangeEnd>

Y

Y

Y

</xDataBody>

10.3.2.响应(B2eNbsRecourseRequest)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<insId>

输

Y

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

169 / 293

 
 
 
 
 
 
  
  
  
  
<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

民生银行银企直联

30

12

12

long

12

250

</Map>

</List>

</xDataBody>

10.3.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsRecourseRequest">

<requestHeader>

170 / 293

  
   
    
    
    
    
    
    
   
  
民生银行银企直联

<dtClient>2023-07-13 15:51:55</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230705092727302</trnId>

<insId>CMBCINS20230705092727303</insId>

<custAccount>*********</custAccount>

<rcvgName>工商测试 001</rcvgName>

<rmrkByPropsr>99</rmrkByPropsr>

<rcrsRsnCd></rcrsRsnCd>

<rcvgBankNo>************</rcvgBankNo>

<rcvgBankName></rcvgBankName>

<rcvgOrgCode>1</rcvgOrgCode>

<rcvgAcctNo>123456</rcvgAcctNo>

<rcvgDistType></rcvgDistType>

<rcvgAcctName>工商测试 001</rcvgAcctName>

<rcrsTp>RT01</rcrsTp>

<billNo>5************20230613000907147</billNo>

<billRangeStart>2000001</billRangeStart>

<billRangeEnd>4000000</billRangeEnd>

<transAmt>121</transAmt>

</xDataBody>

171 / 293

</CMBC>

响应报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsRecourseRequest">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-13 15:52:03</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>3130020230713095785001135400001M</svrId>

<insId>CMBCINS20230705092727303</insId>

<List>

<Map>

<billNo>5************20230613000907147</billNo>

<billRangeStart>2000001</billRangeStart>

<billRangeEnd>4000000</billRangeEnd>

<transId>2811036364554755</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

172 / 293

</Map>

</List>

<trnId>CMBCTRN20230705092727302</trnId>

民生银行银企直联

</xDataBody>

</CMBC>

11.同意清偿

11.1.可同意清偿申请列表查询（B2eNbsAgreeDischar

geSignUpPre）

本部分更新日期:2023-12-06

说明：

本接口用于查询客户作为被追索人收到的待自己同意清偿的追索申请。

11.1.1.请求(B2eNbsAgreeDischargeSignUpPre)

标记

是否必

说明

长

度

输

Y

Y

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页 int

173 / 293

  
 
 
 
 
民生银行银企直联

30

4

15,2

15,2

10

10

10

10

长

度

<billNo>

<billType>

N

N

100 条）

票号

票据类型

AC01：银承

AC02：商承

<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

11.1.2.响应(B2eNbsAgreeDischargeSignUpPre)

说明

标记

<xDataBody>

<svrId>

<trnId>

是

否

必

输

N

Y

服务消息集

银行渠道交易流水号

32

客户技术请求流水号，同一客 64

174 / 293

 
 
 
 
 
 
 
 
  
  
  
<total>

<List>

<Map>

<billNo>

<billRangeStart>

民生银行银企直联

户请勿重复

Y

总条数

int

Y

Y

票据（包）号码

30

子票区间起始(等分化零票（金

12

融机构票据且是否分包流转标

志为 0）返回 0，否则返回具体

区间值)

<billRangeEnd>

Y

子票区间截止(等分化零票（金

12

<isAllowSplitBill>

<billType>

Y

Y

融机构票据且是否分包流转标

志为 0）返回 0，否则返回具体

区间值)

是否可分包 0：否 1：是

票据类型

AC01：银承

AC02：商承

<billClass>

Y

票据介质

ME01：纸票

ME02：电票

<remitDt>

Y

出票日

1

4

4

8

175 / 293

  
  
   
    
    
    
    
    
    
    
<dueDt>

<stdAmt>

<billMoney>

<transAmt>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

Y

N

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

票面到期日

8

标准金额 等分化零票（金融机

12

构票据是否分包流转标志为

0）为空，无标准金额

票据（包）金额

追索金额

出票人全称

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行行名

票面收款人全称

票面收款人账号

票面收款人账户名称

票面收款人开户行行名

票面收款人开户行行号

承兑人账号

承兑人账户名称

承兑人开户行行号

12

12

180

32

180

12

180

180

32

180

180

12

32

180

12

176 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<acptBankName>

<acptName>

<transFromName>

<transFromAcctNo>

<transFromSocCode>

<transFromBankNo>

Y

Y

Y

Y

Y

Y

承兑人开户行名称

承兑人称

申请人名称

申请人账号

申请人统一社会信用代码

申请人开户行号

<transFromBankName> Y

申请人开户行名称

<transToAcctNo>

<transToName>

<transToSocCode>

<transToBankNo>

<transToBankName>

<banEndrsmtMark>

N

N

N

N

N

Y

接收人账号

接收人名称

接收人统一社会信用代码

接收人开户行行号

接收人开户行名称

不得转让标记

EM00：可再转让

EM01：不得转让

<rcrsTp>

Y

追索类型

RT00：拒付追索

RT01：非拒付追索

</Map>

</List>

180

180

180

32

18

12

180

32

180

18

12

180

4

177 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
   
  
民生银行银企直联

</xDataBody>

11.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsAgreeDischargeSignUpPre">

<requestHeader>

<dtClient>2023-06-06 14:38:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083664</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<billNo></billNo>

<billType></billType>

<maxBillMoney></maxBillMoney>

<minBillMoney></minBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

178 / 293

民生银行银企直联

<beginEndDate></beginEndDate>

<endDate></endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsAgreeDischargeSignUpPre">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:54:33</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T77502023062513564072493540004JL</svrId>

<total>5</total>

<List>

<Map>

<billNo>5********************000916389</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>66600</billRangeEnd>

179 / 293

民生银行银企直联

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC01</billType>

<billClass>ME02</billClass>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>666.00</billMoney>

<transAmt>666.00</transAmt>

<drwrName>工商测试 001</drwrName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国工商银行股份有限公司</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptBankNo>************</acptBankNo>

<transFromName>金梦浩企业公司</transFromName>

<transFromAcctNo>*********</transFromAcctNo>

180 / 293

<transFromBankNo>************</transFromBankNo>

民生银行银企直联

<billStatus>CS03</billStatus>

<rcrsTp>RT01</rcrsTp>

</Map>

</List>

<trnId>CMBCTRN2023060210083664</trnId>

</xDataBody>

</CMBC>

11.2.同意清偿申请(B2eNbsAgreeDischargeSignUp)

本部分更新日期:2023-10-31

说明：

1.本接口用于客户作为被追索人查询到待自己同意的追索申请，并同意这些申

请。

2.同意清偿申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatu

s)》查询交易结果。

11.2.1.请求(B2eNbsAgreeDischargeSignUp)

标记

是否必输

说明

长

度

<xDataBody>

<trnId>

<insId>

Y

Y

客户技术请求流水号，同一客

64

户请勿重复

客户业务请求流水号，同一业

64

务请求请勿重复

181 / 293

  
 
 
<custAccount>

Y

签约账号

<List>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<rcrsAmt>

Y

Y

子票区间截止

民生银行银企直联

32

30

12

12

同意清偿金额（对于不可分包

12

的票据，该同意清偿金额须与

票据包金额一致）

<sttlmMk>

N

结算方式，不输默认

4

ST01：票款对付（DVP）（线

上清算）

ST02：纯票过户（FOP）（线

下清算）

</Map>

</List>

</xDataBody>

11.2.2.响应(B2eNbsAgreeDischargeSignUp)

标记

是否必

说明

输

长

度

182 / 293

 
  
   
    
    
    
    
    
   
  
  
<xDataBody>

服务消息集

民生银行银企直联

Y

Y

Y

银行渠道交易流水号

32

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

<svrId>

<trnId>

<insId>

<List>

<Map>

<billNo>

Y

票据（包）号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

</Map>

</List>

30

12

12

long

12

250

183 / 293

  
  
  
  
   
    
    
    
    
    
    
   
  
民生银行银企直联

</xDataBody>

11.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsAgreeDischargeSignUp">

<requestHeader>

<dtClient>2023-06-20 11:13:21</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835376</trnId>

<insId>CMBCINS20230612143835377</insId>

<custAccount>*********</custAccount>

<List>

<Map>

<billNo>5************20230425000613101</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>88811</billRangeEnd>

<rcrsAmt>27831</rcrsAmt>

<sttlmMk>ST01</sttlmMk>

184 / 293

民生银行银企直联

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsAgreeDischargeSignUp">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-20 11:13:26</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<insId>CMBCINS20230612143835377</insId>

<trnId>CMBCTRN20230612143835376</trnId>

</xDataBody>

</CMBC>

185 / 293

12.不可转让撤销

民生银行银企直联

12.1.可进行不可转让撤销申请票据列表查询(B2eNbsNotE

ndorseRepealPre)

本部分更新日期:2023-10-31

说明：

1.该功能是指：已标注了不得转让标记的票据，可由持票人向不得转让标记登

记人发送不得转让撤销申请，登记人签收后，撤销不得转让标记。

2.本接口用于查询可进行不可转让撤销申请的票据列表

12.1.1.请求(B2eNbsNotEndorseRepealPre)

标记

是否必

说明

长

度

输

Y

Y

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

客户技术请求流水号，同一客户请勿重

64

复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

186 / 293

  
 
 
 
 
<billNo>

<billType>

<minBillMoney>

N

N

N

票号

票据类型 AC01：银承 AC02：商承

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

<transToName>

N

N

</xDataBody>

票面到期日止 yyyy-MM-dd

不可转让标记人

12.1.2.响应(B2eNbsNotEndorseRepealPre)

标记

是否必

说明

民生银行银企直联

30

4

15,2

15,2

10

10

10

10

150

长

度

32

输

N

Y

Y

<xDataBody>

<svrId>

<trnId>

<total>

<List>

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

总条数

int

187 / 293

 
 
 
 
 
 
 
 
 
  
  
  
  
  
<Map>

<billNo>

<billClass>

Y

Y

票据（包）号码

票据介质

ME01：纸票

ME02：电票

<billType>

Y

票据类型

AC01：银承

AC02：商承

持有人名称

持有人账号

持有人开户行行号

是否可分包

0：否

1：是

承兑人账号

承兑人账户名称

承兑人开户行行号

承兑人开户行名称

承兑人全称

出票人全称

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<isAllowSplitBill>

<acptAcctNo>

<acptAcctName>

<acptBankNo>

<acptBankName>

<acptName>

<drwrName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

30

4

4

60

60

12

1

32

180

12

180

180

180

188 / 293

   
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<drwrAcctNo>

<drwrAcctName>

<drwrBankNo>

<drwrBankName>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<banEndrsmtMark>

<remitDt>

<dueDt>

<billRangeStart>

<billRangeEnd>

<billRemark>

<transAmt>

<transFromName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

Y

出票人账号

出票人账户名称

出票人开户行行号

出票人开户行行名

票面收款人全称

票面收款人账号

票面收款人账户名称

票面收款人开户行行名

票面收款人开户行行号

不得转让标记

EM00：可再转让

EM01：不得转让

出票日

票面到期日

子票区间起始

子票区间截止

备注

交易金额

不得转让标记人名称

32

180

12

180

180

32

180

180

12

4

8

8

12

12

256

12

180

189 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<transFromAcctNo>

Y

不得转让标记人账号

<transFromBankNo> Y

不得转让标记人行号

民生银行银企直联

32

12

</Map>

</List>

</xDataBody>

12.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsNotEndorseRepealPre">

<requestHeader>

<dtClient>2023-06-19 16:47:37</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835326</trnId>

<insId>CMBCINS20230612143835327</insId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

190 / 293

    
    
   
  
民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsNotEndorseRepealPre">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-21 15:56:40</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<billNo>6********************000910163</billNo>

<billClass>ME02</billClass>

<billType>AC02</billType>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

191 / 293

民生银行银企直联

<billMoney>200.00</billMoney>

<acptAcctNo>123456</acptAcctNo>

<acptAcctName>工商测试 001</acptAcctName>

<acptBankName>中国工商银行总行清算中心</acptBankName>

<acptBankNo>************</acptBankNo>

<acptName>工商测试 001</acptName>

<drwrName>工商测试 001</drwrName>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billRangeStart>1</billRangeStart>

<billRangeEnd>20000</billRangeEnd>

<billStatus>CS03</billStatus>

<transAmt>200.00</transAmt>

<transFromName>工商测试 001</transFromName>

192 / 293

<transFromAcctNo>123456</transFromAcctNo>

<transFromBankNo>************</transFromBankNo>

民生银行银企直联

</Map>

</List>

<total>1</total>

<trnId>CMBCTRN20230612143835532</trnId>

</xDataBody>

</CMBC>

12.2.不可转让撤销申请（B2eNbsNotEndorseRepea

l）

本部分更新日期:2023-10-31

说明：

1.本接口用于持票人向不得转让标记登记人发送不得转让撤销申请，登记人签

收后，撤销不得转让标记

2.不得转让撤销申请后使用《票据交易状态查询(B2eNbsQryDraftTransSta

tus)》查询交易结果

12.2.1.请求(B2eNbsNotEndorseRepeal)

标记

是否必

说明

长

度

<xDataBody>

<trnId>

<insId>

输

Y

Y

客户技术请求流水号，同一客户请

64

勿重复

客户业务请求流水号，同一业务请 64

193 / 293

  
 
 
民生银行银企直联

求请勿重复

32

30

12

12

长

度

32

<custAccount>

Y

签约账号

<List>

<Map>

<Map>

<billNo>

Y

票据(包)号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

Y

子票区间截止

</Map>

</List>

</xDataBody>

12.2.2.响应(B2eNbsNotEndorseRepeal)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<insId>

输

Y

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务 64

194 / 293

 
 
  
   
   
   
   
  
 
  
  
  
  
民生银行银企直联

请求请勿重复

<List>

<Map>

<billNo>

Y

票据（包）号码

<billRangeStart> Y

子票区间起始

<billRangeEnd>

<transId>

<retCode>

Y

Y

Y

子票区间截止

交易流水号

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

30

12

12

long

12

12

</Map>

</List>

</xDataBody>

12.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsNotEndorseRepeal">

195 / 293

  
   
    
    
    
    
    
    
   
  
民生银行银企直联

<requestHeader>

<dtClient>2023-07-18 16:31:10</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230717112044185</trnId>

<insId>CMBCINS20230717112044186</insId>

<custAccount>*********</custAccount>

<List>

<Map>

<billNo>5************20220825000241128</billNo>

<billRangeStart>22234</billRangeStart>

<billRangeEnd>222233</billRangeEnd>

<transAmt>2000.00</transAmt>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsNotEndorseRepeal">

196 / 293

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-07-18 16:31:13</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********03082201523540000LM</svrId>

<insId>CMBCINS20230717112044186</insId>

<List>

<Map>

<billNo>5************20220825000241128</billNo>

<billRangeStart>22234</billRangeStart>

<billRangeEnd>222233</billRangeEnd>

<transId>2816115158558701</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN20230717112044185</trnId>

</xDataBody>

</CMBC>

197 / 293

13.供票结算

民生银行银企直联

13.1.可供票结算签收的供应链票据列表查询（B2eNbsDe

ductConfirmPre）

本部分更新日期:2023-10-31

说明：

1.本接口用于查询可进行供票结算确认签收的票据列表。

2.使用该接口需要验证客户在企业网银具有责任签收菜单权限，您可联系企网

管理员为该银企直联（YQZL）操作员配置相应菜单权限。

13.1.1.请求(B2eNbsDeductConfirmPre)

标记

是否必

说明

长度

输

Y

Y

N

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billNo>

客户技术请求流水号，同一客户请勿

64

重复

签约账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票号

4

198 / 293

  
 
 
 
 
 
<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

</xDataBody>

13.1.2.响应(B2eNbsDeductConfirmPre)

标记

说明

是否

必输

<xDataBody>

服务消息集

民生银行银企直联

15，

2

15，

2

长

度

32

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billRangeStart>

<billRangeEnd>

<remitDt>

N

Y

Y

Y

Y

Y

N

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

总记录数

票据（包）号码

子票区间起始

子票区间截止

出票日

int

30

12

12

8

199 / 293

 
 
  
  
  
  
  
   
    
    
    
    
民生银行银企直联

<dueDt>

<isAllowSplitBill>

<billMoney>

<drwrName>

<pyeeName>

<acptName>

<transFromMsgId>

<settleAmt>

N

N

N

N

Y

Y

Y

Y

票面到期日

是否可分包

0：否

1：是

票据（包）金额

出票人名称

票面收款人名称

承兑人名称

报文流水号

结算金额

<drwrMemberName> Y

出票平台名称

<busiType>

Y

业务类型

BC13：提示付款

BC16：追索同意清偿申请

<transApplDt>

Y

结算日期

</Map>

</List>

</xDataBody>

13.1.3.例子

请求报文

8

12

12

180

180

180

long

12

500

6

200 / 293

    
    
    
    
    
    
    
    
    
    
    
   
  
<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDeductConfirmPre">

民生银行银企直联

<requestHeader>

<dtClient>2023-06-07 16:29:32</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083685</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<billNo></billNo>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDeductConfirmPre">

<responseHeader>

<status>

201 / 293

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:55:23</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T77502023062513574072503540004VJ</svrId>

<total>0</total>

<List />

<trnId>CMBCTRN2023060210083685</trnId>

</xDataBody>

</CMBC>

13.2.供票结算确认签收(B2eNbsDeductCfmSignUp)

本部分更新日期:2023-10-31

说明：

1.本接口用于对可签收的票据进行同意或拒绝签收操作。

2.使用该接口需要验证客户在企业网银具有责任签收菜单权限，您可联系

企网管理员为该银企直联（YQZL）操作员配置相应菜单权限。

13.2.1.请求(B2eNbsDeductCfmSignUp)

标记

是否必

说明

长

202 / 293

  
输

Y

Y

Y

<xDataBody>

<trnId>

<insId>

<custAccount>

<List>

<Map>

民生银行银企直联

度

客户技术请求流水号，同一客户

64

请勿重复

客户业务请求流水号，同一业务

64

请求请勿重复

签约账号

32

4

33

<signFlag>

Y

签收标志 SU00：同意签

收 SU01：拒绝签收

<transFromMsgId> Y

报文流水号

<remark>

N

回复人备注（最多 50 个汉字）

256

提示付款

驳回时，

拒付理由

为其他

（CP06

）时，不

能为空

<dshnrCd>

N

拒绝原因:

256

提示付款

CP01:背书签章未依次前后衔接

203 / 293

 
 
 
 
  
   
   
   
   
民生银行银企直联

驳回时必

CP02:背书记载不清晰

输

CP03:背书人签章缺少单位印章、

法定代表人或其授权的代理人签

章

CP04:背书粘单未加盖骑缝章、骑

缝章不连续或骑缝章不清

CP05:背书不规范、文字有歧义

CP06:其他

CP07:自动拒付

CP08:与自己有直接债权债务关系

的持票人未履行约定义务

CP09:持票人以欺诈、偷盗或者胁

迫等手段取得票据

CP10:持票人明知有欺诈、偷盗或

者胁迫等情形，出于恶意取得票

据

CP11:持票人明知债务人与出票人

或者持票人的前手之间存在抗辩

事由而取得票据

CP12:持票人因重大过失取得不符

合《票据法》规定的票据

CP13:被法院冻结或收到法院止付

通知书

CP14:票据未到期

CP15:商业承兑汇票承兑人账户余

额不足

204 / 293

民生银行银企直联

CP16:承兑人应答同意，扣款确认

行未应答

CP17 承:兑人应答同意，扣款确

认行拒绝

</Map>

</List>

</xDataBody>

13.2.2.响应(B2eNbsDeductCfmSignUp)

标记

是否必

说明

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户请勿

64

重复

客户业务请求流水号，同一业务请求

64

请勿重复

输

Y

Y

Y

<xDataBody>

<svrId>

<trnId>

<insId

<List>

<Map>

<transId>

Y

交易流水号

<retCode> Y

错误码

long

12

205 / 293

  
 
  
  
  
  
  
   
    
    
民生银行银企直联

1：成功

0：失败

<retMsg>

Y

返回信息

250

1：交易成功

0：具体失败错误信息

</Map>

</List>

</xDataBody>

13.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDeductCfmSignUp">

<requestHeader>

<dtClient>2023-06-25 15:36:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835654</trnId>

206 / 293

    
   
  
<insId>CMBCINS20230612143835655</insId>

<custAccount>*********</custAccount>

民生银行银企直联

<List>

<Map>

<transFromMsgId>*********</transFromMsgId>

<transId>**********</transId>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDeductCfmSignUp">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-16 17:59:00</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300********0522900055354000083</svrId>

207 / 293

<insId>CMBCINS20230612143835207</insId>

<trnId>CMBCTRN20230612143835206</trnId>

民生银行银企直联

</xDataBody>

</CMBC>

14.通用签收

14.1.可通用签收票据列表查询(B2eNbsQryStaySignUp

Drafts)

本部分更新日期:2023-10-31

说明：

1.本接口用于查询可进行签收的票据列表，具体类型见下方查询类型"queryT

ype"。

2.使用该接口需要验证客户在企业网银具有责任签收，权利签收，不得转让撤

销菜单权限，您可联系企网管理员为该银企直联（YQZL）操作员配置相

应菜单权限。

14.1.1.请求(B2eNbsQryStaySignUpDrafts)

标记

是否必

说明

长度

输

<xDataBody>

<trnId>

Y

客户技术请求流水号，同一客户请勿重

64

复

<custAccount>

Y

签约账号

32

208 / 293

  
 
 
<queryType>

N

查询类型(不传查全部)

2

民生银行银企直联

01:提示承兑签收（承兑申请签收）

02:提示收票签收（出票交付签收）

03:背书转让签收

04:质押签收（质押申请签收）

05:质押解除签收

06:提示付款签收（委托收款申请签

收）

07:保证签收（保证申请签收）

08:同意清偿申请签收

09:不可转让撤销签收（不得转让撤销

申请签收）

当前页码(从 1 开始)，不传默认为 1

int

每页数据条数（默认 10 条，最大每页

int

<pageNo>

<pageSize>

<billNo>

<billType>

N

N

N

N

100 条）

票号

票据类型

AC01：银承

AC02：商承

<minBillMoney>

N

票据金额范围起

<maxBillMoney> N

票据金额范围止

4

4

15，

2

15，

2

209 / 293

 
 
 
 
 
 
 
<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

14.1.2.响应(B2eNbsQryStaySignUpDrafts)

标记

是否必输

说明

<xDataBody>

服务消息集

民生银行银企直联

10

10

10

10

长

度

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billRangeStart>

N

Y

Y

Y

Y

银行渠道交易流水号

32

客户技术请求流水号，

64

同一客户请勿重复

总条数

int

票据（包）号码

30

子票区间起始(等分化零

12

票（金融机构票据且是

否分包流转标志为 0）返

回 0，否则返回具体区间

210 / 293

 
 
 
 
  
  
  
  
  
   
    
    
民生银行银企直联

值)

<billRangeEnd>

Y

子票区间截止(等分化零

12

票（金融机构票据且是

否分包流转标志为 0）返

回 0，否则返回具体区间

值)

<isAllowSplitBill>

Y

是否可分包

12

<billType>

<billClass>

<remitDt>

<dueDt>

<stdAmt>

Y

Y

Y

Y

N

0：否

1：是

票据类型

AC01：银承

AC02：商承

票据介质

ME01：纸票

ME02：电票

出票日

票面到期日

4

4

8

8

标准金额 等分化零票

12

（金融机构票据是否分

包流转标志为 0）为空，

无标准金额

<billMoney>

Y

票据（包）金额

12

211 / 293

    
    
    
    
    
    
    
    
<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankName>

<drwrBankNo>

<pyeeName>

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptName>

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<transFromName>

<transFromAcctNo>

<transFromSocCode>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

出票人名称

出票人账号

出票人账号名称

出票人开户行

出票人开户行行号

票面收款人名称

票面收款人账号

180

180

180

180

12

180

32

票面收款人账号名称

180

票面收款人开户行名称

180

票面收款人开户行行号

12

承兑人名称

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

交易前手名称

交易前手账号

180

32

180

180

12

180

32

交易前手统一社会信用

18

代码

212 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<transFromBankNo>

Y

交易前手开户行号

12

民生银行银企直联

<transFromBankName> Y

交易前手开户行名称

180

<transFromRmark>

<transToAcctNo>

<transToName>

<transToSocCode>

<transToBankNo>

<transToBankName>

<banEndrsmtMark>

<sttlmMk>

N

Y

Y

Y

Y

Y

Y

Y

<transCode>

Y

签收类型:

申请方备注

签收人账号

签收人名称

250

32

180

签收人统一社会信用代

18

码

签收人开户行行号

签收人开户行名称

12

180

不得转让标记

EM01：不得转让

EM00：可再转让

结算方式

ST01：票款对付

（DVP）（线上清算）

ST02：纯票过户（FOP）

（线下清算）

4

4

2

01：提示承兑签收

02：提示收票签收

03：背书转让签收

213 / 293

    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

04：质押签收

05：质押解除签收

06：提示付款签收

07：保证签收

08：同意清偿申请签收

09：不可转让撤销签收

<transId>

<msgId>

Y

Y

交易流水号

申请报文 id

long

33

</Map>

</List>

</xDataBody>

14.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQryStaySignUpDrafts">

<requestHeader>

<dtClient>2023-06-13 11:43:13</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

214 / 293

    
    
   
  
民生银行银企直联

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060910360619</trnId>

<custAccount>*********</custAccount>

<beginAcptDt>2023-06-11</beginAcptDt>

<endAcptDt>2023-08-14</endAcptDt>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

<minBillMoney>1</minBillMoney>

<maxBillMoney>10000</maxBillMoney>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQryStaySignUpDrafts">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 18:54:49</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

215 / 293

民生银行银企直联

<xDataBody>

<total>29</total>

<List>

<Map>

<billNo>6********************001096691</billNo>

<billRangeStart>2001</billRangeStart>

<billRangeEnd>99900</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC02</billType>

<billClass>ME02</billClass>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<stdAmt>0.01</stdAmt>

<billMoney>979.00</billMoney>

<drwrName>奚峰圣企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>奚峰圣企业公司</drwrAcctName>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>金梦浩企业公司</pyeeName>

<pyeeAcctName>金梦浩企业公司</pyeeAcctName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

216 / 293

民生银行银企直联

<acptName>奚峰圣企业公司</acptName>

<acptAcctNo>*********</acptAcctNo>

<acptAcctName>奚峰圣企业公司</acptAcctName>

<acptBankName>中国民生银行股份有限公司北京中关村支行

</acptBankName>

<acptBankNo>************</acptBankNo>

<transFromName>金梦浩企业公司</transFromName>

<transFromAcctNo>*********</transFromAcctNo>

<transFromSocCode>*********DSXVF92X0</transFromSocCode>

<transFromBankNo>************</transFromBankNo>

<transFromBankName>中国民生银行股份有限公司北京中关村支行

</transFromBankName>

<transToAcctNo>*********</transToAcctNo>

<transToName>殷林聪企业公司</transToName>

<transToSocCode>00000000EQ588PQ510</transToSocCode>

<transToBankNo>************</transToBankNo>

<transToBankName>中国民生银行股份有限公司北京中关村支行

</transToBankName>

<banEndrsmtMark>EM00</banEndrsmtMark>

<transCode>03</transCode>

<transId>****************</transId>

<msgId>100009*****************2000677104</msgId>

</Map>

</List>

<trnId>CMBCTRN2023060910360619</trnId>

</xDataBody>

217 / 293

民生银行银企直联

</CMBC>

14.2.通用签收(B2eNbsDraftSignUp)

本部分更新日期:2024-12-05

说明：

1.本接口用于对可签收的票据进行同意或拒绝签收操作。

2.使用该接口需要验证客户在企业网银具有责任签收，权利签收，不得转让撤

销菜单权限，您可联系企网管理员为该银企直联（YQZL）操作员配置相

应菜单权限。

3.签收申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询交易结果

4.通用签收（提示承兑申请签收）校验商票承兑限额，若校验失败，则返回提

示客户，提示文案：您提交的票据金额超过您的本年可用商票出票限额，

详询客户经理。

14.2.1.请求(B2eNbsDraftSignUp)

标记

是否必输

说明

长度

<xDataBody>

<trnId>

<insId>

Y

Y

客户技术请求流

64

水号，同一客户

请勿重复

客户业务请求流

64

水号，同一业务

请求请勿重复

<custAccount>

Y

签约账号

32

218 / 293

  
 
 
 
<List>

<Map>

<transId>

<transCode>

Y

Y

民生银行银企直联

long

12

交易流水号

交易类型:

01:提示承兑签收

（承兑申请签

收）

02:提示收票签收

（出票交付签

收）

03:背书转让签收

04:质押签收（质

押申请签收）

05:质押解除签收

06:提示付款签收

（委托收款申请

签收）

07:保证签收（保

证申请签收）

08:同意清偿申请

签收

09:不可转让撤销

签收（不得转让

撤销申请签收）

219 / 293

 
  
   
   
<signFlag>

Y

签收标识

4

民生银行银企直联

SU00：同意签收

SU01：拒绝签收

<remark>

N （以下场景必输：交

回复人备注

256

易类型为提示付款，签

（汉字

收标识为拒绝，拒付理

由为其他（CP06））

<dshnrCd>

N （以下场景必输：交

拒绝原因:

易类型为提示付款，签

CP01:背书签章未

收标识为拒绝）

依次前后衔接

CP02:背书记载不

清晰

CP03:背书人签章

缺少单位印章、

法定代表人或其

授权的代理人签

章

CP04:背书粘单未

加盖骑缝章、骑

缝章不连续或骑

缝章不清

CP05:背书不规

范、文字有歧义

CP06:其他

最多

50

个）

4

220 / 293

   
   
   
民生银行银企直联

CP07:自动拒付

CP08:与自己有直

接债权债务关系

的持票人未履行

约定义务

CP09:持票人以欺

诈、偷盗或者胁

迫等手段取得票

据

CP10:持票人明知

有欺诈、偷盗或

者胁迫等情形，

出于恶意取得票

据

CP11:持票人明知

债务人与出票人

或者持票人的前

手之间存在抗辩

事由而取得票据

CP12:持票人因重

大过失取得不符

合《票据法》规

定的票据

CP13:被法院冻结

或收到法院止付

通知书

221 / 293

民生银行银企直联

CP14:票据未到期

CP15:商业承兑汇

票承兑人账户余

额不足

CP16:承兑人应答

同意，扣款确认

行未应答

CP17:承兑人应答

同意，扣款确认

行拒绝

<guaranteeAdr> N （以下场景必输，交

保证人地址

300

易类型为保证申请）

</Map>

</List>

</xDataBody>

14.2.2.响应(B2eNbsDraftSignUp)

标记

是否必

说明

输

Y

Y

<xDataBody>

<svrId>

<trnId>

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户请勿

64

重复

222 / 293

   
  
 
  
  
  
<insId>

Y

客户业务请求流水号，同一业务请求

64

民生银行银企直联

请勿重复

<List>

<Map>

<transId>

Y

交易流水号

<retCode> Y

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

long

12

250

</Map>

</List>

</xDataBody>

14.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftSignUp">

<requestHeader>

<dtClient>2023-09-14 19:18:01</dtClient>

<clientId>**********</clientId>

223 / 293

  
  
   
    
    
    
   
  
民生银行银企直联

<userId>**********001</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023091115083741</trnId>

<insId>CMBCINS2023091115083742</insId>

<custAccount>*********</custAccount>

<List>

<Map>

<transId>****************</transId>

<transCode>03</transCode>

<signFlag>SU00</signFlag>

<dshnrCd></dshnrCd>

<remark></remark>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftSignUp">

<responseHeader>

<status>

224 / 293

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 19:18:14</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300202309141918102303354001UIU</svrId>

<insId>CMBCINS2023091115083742</insId>

<List>

<Map>

<transId>****************</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN2023091115083741</trnId>

</xDataBody>

</CMBC>

225 / 293

15.通用撤销

民生银行银企直联

15.1.可通用撤销票据列表查询(B2eNbsRevocableDraft

sQry)

本部分更新日期:2024-01-22

说明：

1.该接口是指对已成功发起但对手方未签收的电子票据业务交易做撤销处理。

2.本 接 口 用 于 查 询 可 进 行 撤 销 的 票 据 列 表 ， 具 体 类 型 见 下 方 查 询 类 型

“queryType”。

3.使用该接口需要验证客户在企业网银具对应交易类型的菜单权限，您可联系

企网管理员为该银企直联（YQZL）操作员配置相应菜单权限。

4.对于提示承兑且为我行承兑的票据，查询前需联系我行客户经理先进行批次

撤销，再进行查询。

15.1.1.请求(B2eNbsRevocableDraftsQry)

标记

是否必

说明

长度

输

<xDataBody>

<trnId>

Y

客户技术请求流水号，同一客户请勿

64

重复

<custAccount>

Y

签约账号

32

226 / 293

  
 
 
<queryType>

N

查询类型：(不传查全部)

2

民生银行银企直联

01：提示承兑撤

02：出票交付撤销

03：背书转让撤销

04：质押撤销

05：质押解除撤销

06：提示付款撤销

07：贴现撤销

08：保证撤销

09：不可转让撤销

10：追索撤销

11：同意清偿申请撤销

当前页码(从 1 开始)，不传默认为 1

int

每页数据条数（默认 10 条，最大每页

int

<pageNo>

<pageSize>

<billType>

<billNo>

<minBillMoney>

N

N

N

N

N

100 条）

票据类型

AC01：银承

AC02：商承

票号

票据金额范围起

<maxBillMoney> N

票据金额范围止

4

4

15，

2

15，

2

227 / 293

 
 
 
 
 
 
 
<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

<endDate>

N

票面到期日止 yyyy-MM-dd

</xDataBody>

15.1.2.响应(B2eNbsRevocableDraftsQry)

标记

说明

是否

必输

<xDataBody>

服务消息集

民生银行银企直联

10

10

10

10

长

度

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billRangeStart>

<billRangeEnd>

N

Y

Y

Y

Y

Y

银行渠道交易流水号

32

客户技术请求流水号，同一

64

客户请勿重复

返回的总条数（全部数据

int

量）

票据（包）号码

子票区间起始

子票区间截止

30

12

12

228 / 293

 
 
 
 
  
  
  
  
  
   
    
    
    
<isAllowSplitBill>

Y

是否可分包

12

民生银行银企直联

0：否

1：是

<billType>

Y

票据类型

AC01：银票

AC02：商票

<billClass>

Y

票据介质

ME01：纸票

ME02：电票

持有人名称

持有人账号

持有人开户行行号

出票日

票面到期日

票据（包）金额

出票人名称

出票人账号

出票人账号名称

出票人开户行名称

出票人开户行行号

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankName>

<drwrBankNo>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

4

4

60

60

12

8

8

12

180

32

180

180

180

229 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
<pyeeName >

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptName >

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<banEndrsmtMark>

<transFromName >

<transFromAcctNo>

<transFromSocCode>

<transFromBankNo>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

票面收款人名称

票面收款人账号

票面收款人账号名称

票面收款人开户行名称

12

32

180

180

票面收款人开户行行号

12

承兑人名称

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

不得转让标记

EM00：可再转

EM01：不得转让

申请人名称

申请人账号

申请人统一社会信用代码

申请人开户行号

180

32

180

180

12

4

180

32

18

12

180

250

230 / 293

<transFromBankName> Y

申请人开户行名称

<transFromRmark >

Y

申请方备注

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<transToAcctNo>

<transToName>

<transToSocCode>

<transToBankNo>

<transToBankName>

<sttlmMk>

<discDt>

<toBankName>

<transAmt>

<rcrsTp>

<transApplDt>

<transCode>

Y

Y

Y

Y

Y

Y

Y

Y

Y

N

Y

Y

民生银行银企直联

接收人账号

接收人名称

接收人统一社会信用代码

接收人开户行行号

接收人开户行名称

结算方式

贴现日期

贴入行名称

交易金额

追索类型

RT00：拒付追索

RT01：非拒付追索

交易申请日期

撤销类型

01：提示承兑撤销

02：出票交付撤销

03：背书转让撤销

04：质押撤销

05：质押解除撤销

06：提示付款撤销

07：贴现撤销

32

180

18

12

180

4

8

180

12

4

8

2

231 / 293

    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

08：保证撤销

09：不可转让撤销

10：追索撤销

11：同意清偿申请撤销

<transId>

<msgId>

Y

Y

交易流水号

报文流水号

19

50

</Map>

</List>

</xDataBody>

15.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsRevocableDraftsQry">

<requestHeader>

<dtClient>2023-06-20 15:03:35</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

232 / 293

    
    
   
  
民生银行银企直联

<custAccount>*********</custAccount>

<trnId>CMBCTRN********15292321</trnId>

<insId>CMBCINS********15292322</insId>

<queryType>02</queryType>

<beginAcptDt>2022-07-01</beginAcptDt>

<endAcptDt>2023-06-20</endAcptDt>

<beginEndDate>2022-08-01</beginEndDate>

<endDate>2023-07-20</endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsRevocableDraftsQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 18:53:37</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>313002023091418531021UF354001P27</svrId>

233 / 293

民生银行银企直联

<total>2</total>

<List>

<Map>

<billNo>6********************000476827</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>222300</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC02</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>2223.00</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>王三明企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>王三明企业公司</pyeeAcctName>

234 / 293

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

民生银行银企直联

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>陈洋楠企业公司</acptName>

<acptAcctNo>*********</acptAcctNo>

<acptAcctName>陈洋楠企业公司</acptAcctName>

<acptBankName>中国民生银行股份有限公司北京中关村支行

</acptBankName>

<acptBankNo>************</acptBankNo>

<banEndrsmtMark>EM00</banEndrsmtMark>

<transFromName>殷林聪企业公司</transFromName>

<transFromAcctNo>*********</transFromAcctNo>

<transFromSocCode>00000000EQ588PQ510</transFromSocCode>

<transFromBankNo>************</transFromBankNo>

<transFromBankName>中国民生银行股份有限公司北京中关村支行

</transFromBankName>

<transToAcctNo>*********</transToAcctNo>

<transToName>王三明企业公司</transToName>

<transToBankNo>************</transToBankNo>

<transToBankName>中国民生银行股份有限公司北京中关村支行

</transToBankName>

<discDt>********</discDt>

<toBankName>中国民生银行股份有限公司北京中关村支行

</toBankName>

<transAmt>2223.00</transAmt>

<transApplDt>********</transApplDt>

235 / 293

<transCode>02</transCode>

<transId>****************</transId>

<msgId>100009*****************2000383202</msgId>

民生银行银企直联

</Map>

<Map>

<billNo>5********************000650772</billNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>************</billRangeEnd>

<isAllowSplitBill>1</isAllowSplitBill>

<billType>AC01</billType>

<billClass>ME02</billClass>

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>**********.00</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>杜仁台企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>杜仁台企业公司</pyeeAcctName>

236 / 293

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

民生银行银企直联

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<acptBankNo>************</acptBankNo>

<banEndrsmtMark>EM00</banEndrsmtMark>

<transFromName>殷林聪企业公司</transFromName>

<transFromAcctNo>*********</transFromAcctNo>

<transFromSocCode>00000000EQ588PQ510</transFromSocCode>

<transFromBankNo>************</transFromBankNo>

<transFromBankName>中国民生银行股份有限公司北京中关村支行

</transFromBankName>

<transToAcctNo>*********</transToAcctNo>

<transToName>杜仁台企业公司</transToName>

<transToBankNo>************</transToBankNo>

<transToBankName>中国民生银行股份有限公司北京中关村支行

</transToBankName>

<discDt>********</discDt>

<toBankName>中国民生银行股份有限公司北京中关村支行

</toBankName>

<transAmt>**********.00</transAmt>

<transApplDt>********</transApplDt>

237 / 293

<transCode>02</transCode>

<transId>****************</transId>

<msgId>100009*****************2000452003</msgId>

民生银行银企直联

</Map>

</List>

<trnId>CMBCTRN********15292321</trnId>

</xDataBody>

</CMBC>

15.2.通用撤销(B2eNbsDraftRevocation)

本部分更新日期:2023-10-31

说明：

1.本接口用于对已发起但未签收的电子票据业务交易做撤销处理。

2.使用该接口需要验证客户在企业网银具有相应交易类型的菜单权限，您可联

系企网管理员为该银企直联（YQZL）操作员配置相应菜单权限。

3.撤销申请后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查

询交易结果。

15.2.1.请求(B2eNbsDraftRevocation)

标记

是否必输

说明

长

度

<xDataBody>

<insId>

<trnId>

Y

Y

客户业务请求流水号，同一业务请求

64

请勿重复

客户技术请求流水号，同一客户请勿 64

238 / 293

  
 
 
重复

民生银行银企直联

<custAccount>

Y

签约账号

<List>

<Map>

<transId>

Y

待撤销流水号

<transCode> Y

交易类型:

32

12

32

01：提示承兑撤销

02：出票交付撤销

03：背书转让撤销

04：质押撤销

05：质押解除撤销

06：提示付款撤销

07：贴现撤销

08：保证撤销

09：不可转让撤销

10：追索撤销

11：同意清偿申请撤销

</Map>

</List>

</xDataBody>

239 / 293

 
 
  
   
   
  
 
15.2.2.响应(B2eNbsDraftRevocation)

标记

是否必

说明

民生银行银企直联

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客户请勿

64

重复

客户业务请求流水号，同一业务请求

64

请勿重复

输

Y

Y

Y

<xDataBody>

<svrId>

<trnId>

<insId>

<List>

<Map>

<transId>

Y

待撤销流水号

<retCode> Y

错误码

1：成功

0：失败

<retMsg>

Y

返回信息

1：交易成功

0：具体失败错误信息

</Map>

</List>

</xDataBody>

12

12

250

240 / 293

  
  
  
  
  
   
    
    
    
   
  
15.2.3.例子

请求报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftRevocation">

<requestHeader>

<dtClient>2023-09-14 17:53:31</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<custAccount>*********</custAccount>

<trnId>CMBCTRN2023091115083737</trnId>

<insId>CMBCINS2023091115083738</insId>

<List>

<Map>

<transCode>02</transCode>

<transId>****************</transId>

<msgId>100009*****************2000383202</msgId>

</Map>

</List>

</xDataBody>

</CMBC>

241 / 293

响应报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftRevocation">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 17:53:45</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>31300202309141753101UBO35400172B</svrId>

<insId>CMBCINS2023091115083738</insId>

<List>

<Map>

<transId>****************</transId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</Map>

</List>

<trnId>CMBCTRN2023091115083737</trnId>

</xDataBody>

242 / 293

</CMBC>

16.通用查询

民生银行银企直联

16.1.持有票据查询(B2eNbsDraftHoldingBillsQry)

本部分更新日期:2024-11-13

说明：

1.本接口用于查询当前持有票据信息。

16.1.1.请求(B2eNbsDraftHoldingBillsQry)

标记

是否必

说明

长度

输

Y

Y

N

N

N

<xDataBody>

<trnId>

<custAccount>

<pageNo>

<pageSize>

<billType>

客户技术请求流水号，同一客户请勿

64

重复

签约账号/授权账号

当前页码(从 1 开始)，不传默认为 1

32

int

每页数据条数（默认 10 条，最大每页

int

100 条）

票据类型

AC01：银承

AC02：商承

4

243 / 293

  
 
 
 
 
 
<billNo>

<minBillMoney>

N

N

票号

票据金额范围起

<maxBillMoney> N

票据金额范围止

<beginAcptDt>

<endAcptDt>

N

N

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

<beginEndDate> N

票面到期日起 yyyy-MM-dd

N

N

N

票面到期日止 yyyy-MM-dd

承兑人支持模糊查询

交易前手支持模糊查询

<endDate>

<acptName>

<oppName>

</xDataBody>

16.1.2.响应(B2eNbsDraftHoldingBillsQry)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

输

N

Y

民生银行银企直联

4

15，

2

15，

2

10

10

10

10

150

150

长

度

32

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一客

64

户请勿重复

244 / 293

 
 
 
 
 
 
 
 
 
  
  
  
Y

返回的总条数（全部数据量） int

民生银行银企直联

<total>

<List>

<Map>

<billNo>

<billType>

Y

Y

票据（包）号码

票据类型

AC01：银票

AC02：商票

<billClass>

Y

票据介质

ME01：纸票

ME02：电票

持有人名称

持有人账号

持有人开户行行号

是否可分包

0：否

1：是

出票日

票面到期日

票据（包）金额

出票人名称

<hldrName>

<hldrAcctNo>

<hldrBankNo>

<isAllowSplitBill>

<remitDt>

<dueDt>

<billMoney>

<drwrName>

Y

Y

Y

Y

Y

Y

Y

Y

30

4

4

60

60

12

12

8

8

12

180

245 / 293

  
  
   
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

<drwrAcctNo>

<drwrAcctName>

<drwrBankName>

<drwrBankNo>

<pyeeName >

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptName >

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<billRangeStart>

<billRangeEnd>

<transAmt>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

出票人账号

出票人账号名称

出票人开户行名称

出票人开户行行号

票面收款人名称

票面收款人账号

票面收款人账号名称

票面收款人开户行名称

票面收款人开户行行号

承兑人名称

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

子票区间起始

子票区间截止

交易金额

<banEndrsmtMark> Y

是否可转让:

EM00：可再转

32

180

180

180

12

32

180

180

12

180

32

180

180

12

12

12

12

4

246 / 293

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
民生银行银企直联

EM01：不得转让

<billStatus>

Y

新一代票据状态

4

已出票:CS01

已承兑:CS02

已收票:CS03

已到期:CS04

已终止:CS05

已结清:CS06

<cirStatus>

N

票据流通状态

30

TF0301-可流通

TF0302-已锁定

TF0303-不可转让

TF0304-已质押

TF0305-待赎回

TF0401-托收在

TF0402-追索中

TF0501-已结束

<grayDisplay >

Y

是否可操作:

1

0：允许

1：不允许

</Map>

</List>

</xDataBody>

247 / 293

    
    
    
   
  
民生银行银企直联

16.1.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDraftHoldingBillsQry">

<requestHeader>

<dtClient>2023-06-07 16:36:17</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023060210083688</trnId>

<custAccount>*********</custAccount>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<billNo>5********************000338762</billNo>

<billType></billType>

<minBillMoney></minBillMoney>

<maxBillMoney></maxBillMoney>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

248 / 293

民生银行银企直联

<acptName></acptName>

<oppName></oppName>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftHoldingBillsQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 13:58:30</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>T7750202306251400407251354000707</svrId>

<total>1</total>

<List>

<Map>

<billNo>5********************000338762</billNo>

<billType>AC01</billType>

<billClass>ME02</billClass>

249 / 293

民生银行银企直联

<hldrName>殷林聪企业公司</hldrName>

<hldrAcctNo>*********</hldrAcctNo>

<hldrBankNo>************</hldrBankNo>

<isAllowSplitBill>1</isAllowSplitBill>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>900.00</billMoney>

<drwrName>工商测试 001</drwrName>

<drwrAcctNo>123456</drwrAcctNo>

<drwrAcctName>工商测试 001</drwrAcctName>

<drwrBankName>中国工商银行股份有限公司</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国工商银行股份有限公司</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国工商银行股份有限公司</acptBankName>

<acptBankNo>************</acptBankNo>

<billRangeStart>1</billRangeStart>

<billRangeEnd>90000</billRangeEnd>

<transAmt>900.00</transAmt>

<banEndrsmtMark>EM00</banEndrsmtMark>

250 / 293

民生银行银企直联

<billStatus>CS03</billStatus>

<grayDisplay>1</grayDisplay>

<cirStatus></cirStatus>

</Map>

</List>

<trnId>CMBCTRN2023060210083688</trnId>

</xDataBody>

</CMBC>

16.2.票据详细信息查询(B2eNbsDraftDetail)

本部分更新日期:2024-11-13

说明：

1.本接口用于查询新一代票据正背面信息。

16.2.1.请求(B2eNbsDraftDetail)

标记

是否必

说明

<xDataBody>

<trnId>

<custAccount>

<billNo>

<billRangeStart>

<billRangeEnd>

输

Y

Y

Y

Y

Y

长

度

客户技术请求流水号，同一客户请勿

64

重复

签约账号/授权账号

票号

子票区间起始

子票区间截止

32

4

12

12

251 / 293

  
 
 
 
 
 
<showBillRemark> N

展示票面备注

1

民生银行银企直联

0：展示

1：不展示 不输不展示

</xDataBody>

16.2.2.响应(B2eNbsDraftDetail)

标记

是否必

说明

<xDataBody>

<svrId>

<trnId>

<billInfo>

<billNo>

<billType>

<billClass>

输

N

Y

Y

Y

Y

Y

服务消息集

银行渠道交易流水号

客户技术请求流水号，同一

客户请勿重复

正面信息

票据（包）号码

票据类型

AC01：银票

AC02：商票

票据介质

ME01：纸票

ME02：电票

<isAllowSplitBill>

Y

是否可分包

0：否

长

度

32

64

30

4

4

1

252 / 293

 
  
  
  
  
   
   
   
   
<remitDt>

<dueDt>

<acptDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankName>

<drwrBankNo>

<pyeeName >

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptName >

<acptAcctNo>

<acptAcctName>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

1：是

出票日

票面到期日

承兑日期

票据（包）金额

出票人名称

出票人账号

出票人账号名称

出票人开户行名称

出票人开户行行号

收款人名称

收款人账号

收款人账号名称

收款人开户行名称

收款人开户行行号

承兑人全称

承兑人账号

承兑人账号名称

民生银行银企直联

8

8

8

12

180

32

180

180

180

12

32

180

180

12

180

32

180

253 / 293

   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
民生银行银企直联

<acptBankName>

<acptBankNo>

<acptBankType>

<billRangeStart>

<billRangeEnd>

<transAmt>

Y

Y

Y

Y

Y

Y

承兑人开户行名称

承兑人开户行行号

承兑人开户行类型:

201：政策型银行

202：国有商业银行

203：股份制商业银行

204：外资银行

205：城市商业银行

206：农商行和农合行

207：村镇银行

208：农村信用社

301：财务公司

子票区间起始

子票区间截止

交易金额

<hldrBanEndrsmtMark> Y

不得转让标记:

EM00：可再转

EM01：不得转让

交易合同号

发票号

出票人信用等级

出票人评级机构

<transCtrctNo>

<invoiceNo>

<drwrCreditRatgs>

<drwrCdtRatgAgcy>

Y

Y

Y

Y

180

12

3

12

12

12

4

50

50

150

150

254 / 293

   
   
   
   
   
   
   
   
   
   
   
<drwrRatgDuedt>

<acptCdtRatgAgcy>

<acptCdtRatgs>

<acptCdtRatgDueDt>

<billStatus>

Y

Y

Y

Y

Y

民生银行银企直联

150

150

150

150

4

出票人评级到期日

承兑人评级主体

承兑人信用等级

承兑人评级到期日

新一代票据状态

已出票：CS01

已承兑：CS02

已收票：CS03

已到期：CS04

已终止：CS05

已结清：CS06

<cirStatus>

N

票据流通状态

30

TF0101：待收票

TF0301：可流通

TF0302：已锁定

TF0303：不可转让

TF0304：已质押

TF0305：待赎回

TF0401：托收在途

TF0402：追索中

TF0501：已结束

</billInfo >

<endoList>

N

背面信息

255 / 293

   
   
   
   
   
   
  
  
<Map>

<endrsmtType>

Y

Y

民生银行银企直联

10

背书类型

1：保证

2：质押

3：转让背书

4：买断式贴现背书

5：转贴现背书

6：买断式再贴现背书

7：提示付款

8：追索清偿

9：再追索清偿

10：买断式回购

11：回购式贴现背书

12：回购式贴现赎回背书

13：买断式回购赎回

14：质押式回购

15：质押式回购赎回

16：质押式回购再贴现

17：质押式回购再贴现赎回

<transFromName>

<transToName>

<banEndrsmtMark>

<applDt>

Y

Y

Y

N

背书人名称

被背书人名称

禁止背书标记

申请日期

1.背书类型为提示付款时表示

100

10

10

8

256 / 293

   
    
    
    
    
    
民生银行银企直联

提示付款日期

2.背书类型为追索清偿、再追

索清偿时表示追索日期

<signDt>

N

签收日期

8

1.背书类型为转让背书、买断

式贴现背书、回购式贴现背

书、转贴现背书、质押式回

购、质押式回购赎回、买断

式回购赎回、买断式回购、

买断式再贴现背书、质押式

回购再贴现、质押式回购再

贴现赎回表示背书日期

2.背书类型为保证时表示保证

日期

3.背书类型为质押时表示出质

日期

4.背书类型为提示付款时表示

付款或拒付日期

5.背书类型为追索清偿、再追

索清偿时表示同意清偿签收

<redeemOpenDt>

<redeemDueDt>

<guarnteeAdr>

Y

Y

Y

日期

赎回开放日

赎回截止日

保证人地址

8

8

300

257 / 293

    
    
    
    
<payRefuseFlag>

Y

付款或拒付标志

10

民生银行银企直联

SU00：付款

SU01：拒付

拒付理由代码

拒付备注信息

追索类型

BC14：拒付追索

BC15：非拒付追索

背面信息

保证信息

保证人名称

保证人地址

保证日期

保证类型

1: 出票保证

2: 承兑保证

3: 背书保证

Y

Y

Y

N

Y

Y

Y

Y

<returnCode>

<returnMsg>

<recourseType>

</Map>

</endoList>

<guarnteeList >

<Map>

<guartrName>

<guarntrAddr>

<warteeDt>

<assuType>

</Map>

500

200

10

100

100

1

258 / 293

    
    
    
    
   
  
  
   
    
    
    
    
   
</guarnteeList >

保证信息

民生银行银企直联

</xDataBody>

16.2.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsDraftDetail">

<requestHeader>

<dtClient>2023-09-14 18:36:04</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023090819143639</trnId>

<insId>CMBCINS2023090819143640</insId>

<custAccount>*********</custAccount>

<billNo>5********************001115853</billNo>

<billRangeStart>0</billRangeStart>

<billRangeEnd>0</billRangeEnd>

</xDataBody>

</CMBC>

259 / 293

  
响应报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsDraftDetail">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 18:59:25</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<svrId>3130020230914185910225M354001QTU</svrId>

<trnId>CMBCTRN2023090819143639</trnId>

<billInfo>

<billNo>5********************001115853</billNo>

<billType>AC01</billType>

<billClass>ME02</billClass>

<isAllowSplitBill>0</isAllowSplitBill>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<acptDt>********</acptDt>

<billMoney>118.00</billMoney>

260 / 293

民生银行银企直联

<drwrName>金梦浩企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>金梦浩企业公司</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<pyeeName>殷林聪企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeAcctName>殷林聪企业公司</pyeeAcctName>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankNo>************</acptBankNo>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<acptBankType>203</acptBankType>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<billRangeStart>0</billRangeStart>

<billRangeEnd>0</billRangeEnd>

<transAmt>118.00</transAmt>

<hldrBanEndrsmtMark>EM00</hldrBanEndrsmtMark>

<transCtrctNo>222</transCtrctNo>

<invoiceNo>333</invoiceNo>

<drwrCreditRatgs>2323</drwrCreditRatgs>

261 / 293

民生银行银企直联

<drwrCdtRatgAgcy>232323</drwrCdtRatgAgcy>

<drwrRatgDuedt>********</drwrRatgDuedt>

<billStatus>CS03</billStatus>

<cirStatus>TF0301</cirStatus>

</billInfo>

<endoList />

<guarnteeList />

</xDataBody>

</CMBC>

16.3.票据交易状态查询(B2eNbsQryDraftTransStatus)

本部分更新日期:2024-11-13

说明：

1.本接口用于根据签约账户、操作类型等参数查询各个交易的交易状态。

2.若想查询撤销交易的状态或者结果，操作类型请选择“申请”，客户业务请求

流水号（insid）也请使用对应申请的客户业务请求流水号。例如：背书转

让申请后，在背书转让待签收前，进行背书转让撤销，撤销后，若需查询

撤销结果，需使用背书申请的 insid，操作类型选择“申请”，业务类型选

择“02 背书转出”。

16.3.1.请求(B2eNbsQryDraftTransStatus)

标记

是否必输 说明

长

度

<xDataBody>

<trnId>

Y

客户技术请求流水号，同一客户 64

262 / 293

  
 
<custAccount>

<operType>

<insId>

<busiStage>

Y

Y

N

N

民生银行银企直联

请勿重复

签约账号/授权账号

操作类型:

1：申请

2：签收

原交易客户业务请求流水号

业务类型

【申请方】

01: 出票交付（提示收票）

02: 背书转出

03: 申请贴现

04: 申请质押

05: 出票登记（提交出票）

06: 提示承兑

07: 发出托收（提示付款）

08: 保证申请

09: 解质押申请

10: 未用退回（撤票）

11: 发出追索

12: 发出清偿

13: 不得转让撤销申请

【签收方】：

01: 收票签收

32

1

64

2

263 / 293

 
 
 
 
民生银行银企直联

02: 转让签收（收到背书）

03: 收到质押

04: 收到承兑

05: 收到托收（提示付款）

06: 收到提示保证（保证申请）

07: 收到解除质押

08: 收到追索

09: 收到清偿申请

10: 收到不得转让撤销

票据类型，不传为全部

4

AC01：银承

AC02：商承

出票日起 yyyy-MM-dd

出票日止 yyyy-MM-dd

票据到期日起 yyyy-MM-dd

票据到期日止 yyyy-MM-dd

操作日期起 yyyy-MM-dd

操作日期止 yyyy-MM-dd

票据（包）号码

10

10

10

10

10

10

30

当前页码(从 1 开始，不传默认为

int

1）

每页数据条数（默认 10 条，最大 int

264 / 293

<billType>

<beginAcptDt>

<endAcptDt>

<beginEndDate>

<endDate>

<beginApplyDt>

<endApplyDt>

<billNo>

<pageNo>

<pageSize>

N

N

N

N

N

N

N

N

N

N

 
 
 
 
 
 
 
 
 
 
民生银行银企直联

每页 100 条）

</xDataBody>

16.3.2.响应(B2eNbsQryDraftTransStatus)

标记

是否

说明

必输

长

度

<xDataBody>

服务消息集

银行渠道交易流水号

32

客户技术请求流水号，同一

64

<svrId>

<trnId>

<total>

<List>

<Map>

<billNo>

<billType>

N

Y

Y

Y

Y

客户请勿重复

总条数

票据（包）号码

票据类型

AC01：银票

AC02：商票

<isAllowSplitBill>

Y

是否可分包

0：否

1：是

<remitDt>

Y

出票日

int

30

4

1

8

265 / 293

  
  
  
  
  
  
   
   
   
   
<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

<drwrBankName>

<drwrBankNo>

<pyeeName >

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptName >

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<billRangeStart>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

票面到期日

票据（包）金额

出票人全称

出票人账号

出票人账号名称

出票人开户行名称

出票人开户行行号

收款人名称

收款人账号

收款人账号名称

收款人开户行名称

收款人开户行行号

承兑人全称

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

民生银行银企直联

8

12

180

32

180

180

180

12

32

180

180

12

180

32

180

180

12

子票区间起始：等分化零票

12

（金融机构票据且是否分包

266 / 293

   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
   
民生银行银企直联

流转标志为 0）返回 0，否则

返回具体区间值

<billRangeEnd>

Y

子票区间截止：等分化零票

12

（金融机构票据且是否分包

流转标志为 0）返回 0，否则

返回具体区间值

<hldrBanEndrsmtMark>

Y

禁止背书标记:

EM00：可再转

EM01：不得转让

<billStatus>

Y

票据状态

已出票：CS01

已承兑：CS02

已收票：CS03

已到期：CS04

已终止：CS05

已结清：CS06

<statusCode>

Y

处理结果

0：待处理

1：处理中

2：处理成功

3：处理失败

4

4

1

<transBusiStatus>

Y

业务交易状态

10

TBS11：待申请

TBS12：申请待确认

267 / 293

   
   
   
   
   
民生银行银企直联

TBS13：待应答

TBS14：待结算

TBS15：应答待确认

TBS16：撤回待确认

TBS17：清分待确认

TBS18：清算排队

TBS19：待应答（清分认）

TBS21：已签收

TBS22：结算成功

TBS23：已成功

TBS24：已权属

TBS31：已失败

TBS32：已拒绝

TBS33：已撤回

TBS34：已清退

TBS35：已清分

TBS36：结算失败

TBS37：已作废

<transBusiStatusCode>

Y

业务交易状态码

50

TBS11

TBS12

TBS13

TBS14

TBS15

TBS16

268 / 293

   
民生银行银企直联

TBS17

TBS18

TBS19

TBS21

TBS22

TBS23

TBS24

TBS31

TBS32

TBS33

TBS34

TBS35

TBS36

TBS37

<transFromName>

<transFromAcctNo>

<transFromBankNo>

<transApplDt>

<transFromRemark>

<transtoName>

<transtoAcctNo>

<transtoBankNo>

Y

Y

Y

Y

N

Y

Y

Y

申请人名称

申请人账号

申请人行号

申请日期

申请人备注

接收人名称

接收人账号

接收人行号

180

32

12

8

255

180

32

12

269 / 293

   
   
   
   
   
   
   
   
<transToRemark>

<onlineMark>

N

Y

民生银行银企直联

接收人备注

255

线上清算标识

线上：ST01

线下：ST02

<busiStage>

Y

业务类型

【申请方】

01: 出票交付（提示收票）

02: 背书转出

03: 申请贴现

04: 申请质押

05: 出票登记（提交出票）

06: 提示承兑

07: 发出托收（提示付款）

08: 保证申请

09: 解质押申请

10: 未用退回（撤票）

11: 发出追索

12: 发出清偿

13: 不得转让撤销申请

【签收方】

01: 收票签收

02: 转让签收（收到背书）

03: 收到质押

04: 收到承兑

4

2

270 / 293

   
   
   
民生银行银企直联

05: 收到托收（提示付款）

06: 收到提示保证（保证申

请）

07: 收到解除质押

08: 收到追索

09: 收到清偿申请

10: 收到不得转让撤销

<errorMsg>

处理结果说明

<transId>

Y

交易流水号

long

</Map>

</List>

</xDataBody>

16.3.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQryDraftTransStatus">

<requestHeader>

<dtClient>2023-06-13 21:27:54</dtClient>

<clientId>**********</clientId>

<userId>**********999</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

271 / 293

   
   
  
  
民生银行银企直联

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN********134621184</trnId>

<insId>CMBCINS20230914134621187</insId>

<custAccount>*********</custAccount>

<operType>1</operType>

<busiStage>01</busiStage>

<billType></billType>

<beginAcptDt></beginAcptDt>

<endAcptDt></endAcptDt>

<beginEndDate></beginEndDate>

<endDate></endDate>

<beginApplyDt>2023-08-14</beginApplyDt>

<endApplyDt>2023-09-14</endApplyDt>

<billNo>5********************001110814</billNo>

<pageNo>1</pageNo>

<pageSize>1</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQryDraftTransStatus">

<responseHeader>

<status>

<code>0</code>

272 / 293

民生银行银企直联

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 18:03:58</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<total>1</total>

<List>

<Map>

<billNo>5********************001110814</billNo>

<billType>AC01</billType>

<isAllowSplitBill>0</isAllowSplitBill>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>710.00</billMoney>

<drwrName>殷林聪企业公司</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrAcctName>殷林聪企业公司</drwrAcctName>

<drwrBankNo>************</drwrBankNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<pyeeName>金梦浩企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

273 / 293

<pyeeAcctName>金梦浩企业公司</pyeeAcctName>

民生银行银企直联

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<acptBankNo>************</acptBankNo>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<billRangeStart>0</billRangeStart>

<billRangeEnd>0</billRangeEnd>

<hldrBanEndrsmtMark>EM01</hldrBanEndrsmtMark>

<billStatus>CS03</billStatus>

<statusCode>2</statusCode>

<transBusiStatusCode>TBS21</transBusiStatusCode>

<transBusiStatus>已签收</transBusiStatus>

<transFromName>殷林聪企业公司</transFromName>

<transFromAcctNo>*********</transFromAcctNo>

<transFromBankNo>************</transFromBankNo>

<transApplDt>********</transApplDt>

<transtoName>金梦浩企业公司</transtoName>

<transtoAcctNo>*********</transtoAcctNo>

<transtoBankNo>************</transtoBankNo>

<busiStage>01</busiStage>

<errorMsg>处理成功</errorMsg>

274 / 293

<transId>****************</transId>

民生银行银企直联

</Map>

</List>

<trnId>CMBCTRN********134621184</trnId>

</xDataBody>

</CMBC>

16.4.票据交易状态精确查询(B2eNbsComprehensiveQ

ueryByTransId)

本部分更新日期:2023-10-31

说明：

1.本接口用于根据票据交易流水号（transId）精准查询单笔交易状态。

16.4.1.请求(B2eNbsComprehensiveQueryByTransId)

标记

是否必

说明

输

<xDataBody>

长

度

<trnId>

Y

客户技术请求流水号，同一客户请勿重

64

复

<custAccount> Y

签约账号

<transId>

Y

交易流水号

</xDataBody>

32

long

275 / 293

  
 
 
 
16.4.2.响应(B2eNbsComprehensiveQueryByTransId)

民生银行银企直联

标记

是否必

说明

长度

<xDataBody>

<svrId>

<trnId>

<billNo>

<billType>

输

N

Y

Y

Y

服务消息集

银行渠道交易流水号

32

客户技术请求流水号，同一客

64

户请勿重复

票据（包）号码

票据类型

AC01：银票

AC02：商票

<isAllowSplitBill>

Y

是否可分包

0：否

1：是

出票日

票面到期日

票据（包）金额

出票人全称

出票人账号

出票人账号名称

<remitDt>

<dueDt>

<billMoney>

<drwrName>

<drwrAcctNo>

<drwrAcctName>

Y

Y

Y

Y

Y

Y

30

4

1

8

8

12

180

32

180

276 / 293

  
  
  
  
  
  
  
  
  
  
  
  
<drwrBankName>

<drwrBankNo>

<pyeeName >

<pyeeAcctNo>

<pyeeAcctName>

<pyeeBankName>

<pyeeBankNo>

<acptName >

<acptAcctNo>

<acptAcctName>

<acptBankName>

<acptBankNo>

<billRangeStart>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

民生银行银企直联

180

180

12

32

180

180

12

180

32

180

180

12

12

出票人开户行名称

出票人开户行行号

收款人名称

收款人账号

收款人账号名称

收款人开户行名称

收款人开户行行号

承兑人全称

承兑人账号

承兑人账号名称

承兑人开户行名称

承兑人开户行行号

子票区间起始：等分化零票

（金融机构票据且是否分包流

转标志为 0）返回 0，否则返回

具体区间值

<billRangeEnd>

Y

子票区间截止：等分化零票

12

（金融机构票据且是否分包流

转标志为 0）返回 0，否则返回

具体区间值

277 / 293

  
  
  
  
  
  
  
  
  
  
  
  
  
  
民生银行银企直联

4

4

1

<hldrBanEndrsmtM

Y

禁止背书标记:

ark>

EM00：可再转

EM01：不得转让

<billStatus>

Y

票据状态 :

已出票：CS01

已承兑：CS02

已收票：CS03

已到期：CS04

已终止：CS05

已结清：CS06

<statusCode>

Y

处理结果

0：待处理

1：处理中

2：处理成功

3：处理失败

<transBusiStatus>

Y

业务交易状态

10

TBS11：待申请

TBS12：申请待确认

TBS13：待应答

TBS14：待结算

TBS15：应答待确认

TBS16：撤回待确认

TBS17：清分待确认

TBS18：清算排队

TBS19：待应答（清分确认）

278 / 293

  
  
  
  
民生银行银企直联

TBS21：已签收

TBS22：结算成功

TBS23：已成功

TBS24：已权属

TBS31：已失败

TBS32：已拒绝

TBS33：已撤回

TBS34：已清退

TBS35：已清分

TBS36：结算失败

TBS37：已作废

<transBusiStatus

Y

业务交易状态码

50

Code>

TBS11

TBS12

TBS13

TBS14

TBS15

TBS16

TBS17

TBS18

TBS19

TBS21

TBS22

TBS23

TBS24

279 / 293

   
民生银行银企直联

TBS31

TBS32

TBS33

TBS34

TBS35

TBS36

TBS37

<transFromName>

<transFromAcctNo>

Y

Y

申请人名称

申请人账号

<transFromBankNo> Y

申请人行号

<transApplDt>

Y

申请日期

<transFromRemark> N

申请人备注

<transtoName>

<transtoAcctNo>

<transtoBankNo>

<transToRemark>

<onlineMark>

Y

Y

Y

N

Y

接收人名称

接收人账号

接收人行号

接收人备注

结算方式

ST01：票款对付（DVP）（线

上清算）

ST02：纯票过户（FOP）（线

下清算）

180

32

12

8

255

180

32

12

255

4

280 / 293

  
  
  
  
  
  
  
  
  
  
<busiStage>

Y

业务类型

2

民生银行银企直联

CBS.000：票据维护

NES.001：出票

NES.002：承兑

NES.003：收票

NES.004：保证

NES.006：转让背书

NES.007：贴现

NES.008：贴现赎回

NES.009：质押

NES.010：质押解除

NES.011：提示付款

NES.012：追偿

NES.013：同意清偿

NES.014：撤票

NES.015：不得转让撤销

<errorMsg>

处理结果说明

</xDataBody>

16.4.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsComprehensiveQueryByTransId">

<requestHeader>

<dtClient>2023-06-14 10:42:04</dtClient>

281 / 293

   
  
民生银行银企直联

<clientId>**********</clientId>

<userId>**********028</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612093933174</trnId>

<custAccount>*********</custAccount>

<transId>****************</transId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsComprehensiveQueryByTransId">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-09-14 19:22:26</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

282 / 293

</responseHeader>

<xDataBody>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

民生银行银企直联

<drwrAcctName>金梦浩企业公司</drwrAcctName>

<billRangeStart>1</billRangeStart>

<transFromName>金梦浩企业公司</transFromName>

<pyeeAcctNo>*********</pyeeAcctNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<billRangeEnd>777700</billRangeEnd>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<billMoney>7777.00</billMoney>

<dueDt>********</dueDt>

<transBusiStatusCode>TBS13</transBusiStatusCode>

<acptAcctNo>0</acptAcctNo>

<drwrAcctNo>*********</drwrAcctNo>

<acptAcctName>中国民生银行股份有限公司北京分行</acptAcctName>

<transtoBankNo>************</transtoBankNo>

<onlineMark>ST01</onlineMark>

<billStatus>CS03</billStatus>

<transApplDt>********</transApplDt>

<billNo>5********************000416345</billNo>

<drwrBankNo>************</drwrBankNo>

<transtoAcctNo>0</transtoAcctNo>

<svrId>3130020230914192210237I354001VDQ</svrId>

283 / 293

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

民生银行银企直联

</pyeeBankName>

<remitDt>********</remitDt>

<transFromBankNo>************</transFromBankNo>

<billType>AC01</billType>

<transBusiStatus>待应答</transBusiStatus>

<acptBankNo>************</acptBankNo>

<busiStage>NES.013</busiStage>

<transtoName>慈溪民生村镇银行股份有限公司</transtoName>

<drwrName>金梦浩企业公司</drwrName>

<transFromAcctNo>*********</transFromAcctNo>

<pyeeAcctName>苗聪静企业公司</pyeeAcctName>

<trnId>CMBCTRN20230612093933174</trnId>

<pyeeBankNo>************</pyeeBankNo>

<pyeeName>苗聪静企业公司</pyeeName>

<isAllowSplitBill>1</isAllowSplitBill>

<statusCode>1</statusCode>

<errorMsg>处理成功</errorMsg>

</xDataBody>

</CMBC>

16.5.开户行信息查询 (B2eNbsQueryBankInfo)

本部分更新日期:2024-02-20

说明：

1.本接口用于查询开户行信息。

284 / 293

民生银行银企直联
2.注意：如果要查询“中国民生银行北京亚运村支行”，可输入以下条件进行查

询：1.民生银行~运村；2.北京亚运村支行；3.民生~北京~运村。行别不

能为简称，如：“工行~和平里“，应为“工商~和平里”。

16.5.1.请求(B2eNbsQueryBankInfo)

标记

是否必

说明

长

度

<xDataBody>

<trnId>

<memberId>

<brchName>

<bankNo>

<pageNo>

<pageSize>

输

Y

N

Y

N

N

N

客户技术请求流水号，同一客户请勿重

64

复

业务办理渠道代码，码值见字典《业务

32

办理渠道代码》

机构名称 支持模糊查询，~表示任意字

255

符

行号

当前页码(从 1 开始，不传默认为 1）

12

10

每页数据条数（默认 10 条，最大每页

6

100 条）

<showBankName> N

是否返回大额支付系统机构名称 0:展

2

示 1:不展示

</xDataBody>

285 / 293

  
 
 
 
 
 
 
 
民生银行银企直联

长

度

32

服务消息集

银行渠道交易流水号

请勿重复

总条数

客户技术请求流水号，同一客户

64

16.5.2.响应(B2eNbsQueryBankInfo)

标记

是否必

说明

输

N

Y

Y

<xDataBody>

<svrId>

<trnId>

<total>

<List>

<Map>

<bankFullName> Y

机构全称

<memberId>

<bankCode>

<socCode>

<bankNo>

<bankName>

<isAvailable>

Y

Y

Y

Y

N

N

</Map>

业务办理渠道代码

机构参与者代码

统一社会信用代码

行号

大额支付系统机构名称

是否可用:

0:不可用

1:可用

int

255

6

9

18

1

255

2

286 / 293

  
  
  
  
  
   
    
    
    
    
    
    
    
   
民生银行银企直联

</List>

</xDataBody>

16.5.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryBankInfo">

<requestHeader>

<dtClient>2023-06-25 11:08:19</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835586</trnId>

<insId>CMBCINS20230612143835587</insId>

<custAccount>*********</custAccount>

<brchName>北京</brchName>

<bankNo>************</bankNo>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

287 / 293

  
</CMBC>

响应报文

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="B2eNbsQueryBankInfo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 11:08:20</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<total>1</total>

<List>

<Map>

<bankFullName>中国民生银行股份有限公司北京分行</bankFullName>

<memberId>100009</memberId>

<bankCode>*********</bankCode>

<socCode>911101028019779621</socCode>

<bankNo>************</bankNo>

</Map>

</List>

288 / 293

<trnId>CMBCTRN20230612143835586</trnId>

民生银行银企直联

</xDataBody>

</CMBC>

17.业务字典

17.1.业务办理渠道代码

码值

说明

100009

中国民生银行

100001

中国工商银行

100002

中国农业银行

100003

中国银行

100004

中国建设银行

100006

中国邮政储蓄银行

100005

交通银行

100007

中信银行

100008

中国光大银行

101369

华夏银行

100223

广发银行

100012

平安银行

289 / 293

民生银行银企直联

100010

招商银行

100011

兴业银行

100013

上海浦东发展银行

100019

浙商银行

100019

浙商银行

100029

恒丰银行

17.2.省份代码

码值

说明

11

12

13

14

15

21

22

23

31

32

北京

天津

河北

山西

内蒙古

辽宁

吉林

黑龙江

上海

江苏

290 / 293

33

34

35

36

37

41

42

43

44

45

46

50

51

52

53

54

61

62

浙江

安徽

福建

江西

山东

河南

湖北

湖南

广东

广西

海南

重庆

四川

贵州

云南

西藏

陕西

甘肃

民生银行银企直联

291 / 293

民生银行银企直联

63

64

65

71

81

82

青海

宁夏

新疆

台湾

香港

澳门

292 / 293

