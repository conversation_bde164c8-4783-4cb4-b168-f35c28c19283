银企直联接口文档

（现金管理-集团账户服务）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-18

定义接口文档

V2.0.0 2024-11-21

合并原接口文档<36.银企直联-集团

授权业务>集团客户总公司查询子公

司信息

(QryBlocAuthRelShpAllShow)接

口

1 / 19

目录

民生银行银企直联

目录 .............................................................................................2

1. 集团公司主动收款交易(ENXFER) ........................................................ 3

1.1. 请求(ENXFER) ..........................................................................3

1.2. 响应(ENXFER) ..........................................................................4

1.3. 例子 ...................................................................................... 4

2. 授权关系总览(QRYAUTHRELATION) ................................................. 6

2.1. 请求(QRYAUTHRELATION) ........................................................... 6

2.2. 响应(QRYAUTHRELATION) ........................................................... 6

2.3. 例子 ...................................................................................... 7

3. 集团对账授权关系总览(QRYBLOCAUTHRELATION) ............................... 9

3.1. 请求(QRYBLOCAUTHRELATION) .................................................... 9

3.2. 响应(QRYBLOCAUTHRELATION) .................................................... 9

3.3. 例子 .................................................................................... 10

4. 客户授权业务信息查询(QRYAUTHCIFRELATIONINFO) ......................... 11

4.1. 请求(QRYAUTHCIFRELATIONINFO) ............................................... 12

4.2. 响应(QRYAUTHCIFRELATIONINFO) ............................................... 12

4.3. 例子 .................................................................................... 12

5. 集团客户总公司查询子公司信息(QRYBLOCAUTHRELSHPALLSHOW) ....... 14

5.1. 请求(QRYBLOCAUTHRELSHPALLSHOW) ........................................ 14

5.2. 响应(QRYBLOCAUTHRELSHPALLSHOW) ........................................ 14

5.3. 报文示例 ............................................................................... 16

2 / 19

民生银行银企直联

1. 集团公司主动收款交易(EnXfer)

本部分更新日期:2021-04-02

集团公司主动收下属子公司款的交易。要求

1：付款方向是从子公司账号到总公司账号；

2：付款账号是该集团公司的下属子公司且签约网银；

3：收款账号是该集团公司的签约账号；

4：付款账号所属子公司授权开通此服务；

5：付款账号所属子公司授权总公司有转账权限；

6：insId 不能重复，如果重复，则返回流水号重复，如果该流水号的交易失败返回失

败的原因；

7：转账单笔限额和当日限额根据不同的设定分别收到总公司，子公司和特殊账户设定

的限制；

8：目前不支持预约转账，填什么日期都是即时转账；

9：手续费收取设置等同于企业网银该交易的收取设置。

1.1. 请求(EnXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

 <cltcookie> 可选，客户端 cookie，响应时原值返回（★）

 <insId>

指令 ID，一条转账指令在客户端的唯一标识（★）

 <acntNo>

付款账号（子公司账号）（★）

 <acntToNo> 收款账号（总公司账号）（★）

 <amount>

转账金额（★）

长度

64

64

32

32

12

3 / 19

 <payType> 转账方向，预留字段，目前只支持填 0

 <explain>

摘要/用途（★）

 <certNo>

企业自制凭证号(8 位以内的数字)

民生银行银企直联

1

22

8

 <actDate>

要求的转账日期 YYYY-MM-DD（★）预留字段，目前不

10

支持预约转账，填什么日期都是即时转账

</xDataBody>

1.2. 响应(EnXfer)

  标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <svrId>

服务器该笔交易的标识（★）

 <insId>

指令 ID，请求时给出的 ID（★）

 <balance>

余额

</xDataBody>

1.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="EnXfer">

<requestHeader>

<dtClient>2008-03-20 10:05:33</dtClient>

<clientId>2001660306</clientId>

<userId>200166030601</userId>

长度

64

32

64

15

4 / 19

民生银行银企直联

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>80531224</trnId>

<cltcookie></cltcookie>

<insId>80535099</insId>

<acntNo>0101014130000653</acntNo>

<acntToNo>0101014830000648</acntToNo>

<amount>1</amount>

<payType>0</payType>

<explain>平台付款 fafsd</explain>

<actDate>2008-03-28</actDate>

<certNo>12345678</certNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none" trnCode="EnXfer"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-17 09:43:47</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>80531224</trnId>

<svrId></svrId>

<insId>80535099</insId>

<balance>4904495.00</balance>

5 / 19

民生银行银企直联

</xDataBody>

</CMBC>

2. 授权关系总览(QryAuthRelation)

本部分更新日期:2021-10-27

2.1. 请求(QryAuthRelation)

标记

说明

<xDataBody>

 <pageNo>

页码最小为 1，且不能大于实际查询结果总页数

 <pageSize> 查询笔数，可选 10、20、50

</xDataBody>

2.2. 响应(QryAuthRelation)

长度

8

8

标记

说明

长度

<xDataBody>

 <allNum>

总记录数（★）

 <pageNumber>

总页数（★）

 <pageSize>

页面大小（★）

 <List>

  <Map>

   <CifNo>

授权公司客户号

8

8

8

4

6 / 19

民生银行银企直联

   <AcNo>

授权公司帐号

   <CifName>

授权公司名称

   <AcType>

对公活期

   <AcPermit>

授权权限 1-查询 2-查询并转账

   <TransferLimit>

授权额度日累计限额

   <AuthDebitSearchFlag> 法透账户查询 1 有权限 0-无权限

   <AuthFlowSearchFlag> 对公流动利查询 1 有权限 0-无权限

39

240

2

2

2

2

2

备用字段 1

备用字段 2

备用字段 3

   <Remark1>

   <Remark2>

   <Remark3>

  </Map>

 </List>

</xDataBody>

2.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryAuthRelation">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>2200328186</clientId>

<userId>2200328186680</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

7 / 19

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<pageSize>10</pageSize>

<pageNo>1</pageNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none"

lang="chs" trnCode="QryAuthRelation">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-09-01 19:33:16</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<pageSize>10</pageSize>

<pageNumber>1</pageNumber>

<list>

<Map>

<CifNo>2200037959</CifNo>

<AcNo>*********</AcNo>

<CifName>回款通标准客户 2</CifName>

<AcType>01</AcType>

<AcPermit>2</AcPermit>

<TransferLimit>2010.00</TransferLimit>

<AuthDebitSearchFlag>1</AuthDebitSearchFlag>

<AuthFlowSearchFlag>1</AuthFlowSearchFlag>

<Remark1></Remark1>

民生银行银企直联

8 / 19

民生银行银企直联

<Remark2></Remark2>

<Remark3></Remark3>

</Map>

</list>

<allNum>1</allNum>

</xDataBody>

</CMBC>

3. 集团对账授权关系总览(QryBlocAuthRelation)

本部分更新日期:2021-10-27

3.1. 请求(QryBlocAuthRelation)

标记

说明

<xDataBody>

 <pageNo>

页码最小为 1，且不能大于实际查询结果总页数

 <pageSize> 查询笔数，可选 10、20、50

</xDataBody>

3.2. 响应(QryBlocAuthRelation)

长度

8

8

标记

说明

长度

<xDataBody>

 <allNum>

总记录数（★）

 <pageNumber>

总页数（★）

 <pageSize>

页面大小（★）

 <List>

8

8

8

9 / 19

民生银行银企直联

  <Map>

   <groupCustomNo>

(被)授权公司客户号

   <groupCustomName> (被)授权公司名称

   <agreementNo>

(被)集团总部协议编号

   <Remark1>

   <Remark2>

   <Remark3>

  </Map>

 </List>

</xDataBody>

3.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryBlocAuthRelation">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>2200328186</clientId>

<userId>2200328186680</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

10 / 19

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none"

lang="chs" trnCode="QryBlocAuthRelation">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-09-01 18:13:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<pageSize>10</pageSize>

<pageNumber>0</pageNumber>

<list>

<Map>

<groupCustomNo>2200037959</groupCustomNo>

<agreementNo>AP*********</agreementNo>

<groupCustomName>客户 2</groupCustomName>

<Remark1></Remark1>

<Remark2></Remark2>

<Remark3></Remark3>

</Map>

</list>

<allNum>0</allNum>

</xDataBody>

</CMBC>

4. 客户授权业务信息查询(QryAuthCifRelationInfo)

本部分更新日期:2021-10-27

11 / 19

4.1. 请求(QryAuthCifRelationInfo)

民生银行银企直联

标记

说明

<xDataBody>

 <AuthedCifNo> 客户号（★）

</xDataBody>

4.2. 响应(QryAuthCifRelationInfo)

长度

32

标记

说明

长度

<xDataBody>

 <DebitFlag>

定期账户：1 有权限，0-无权限

 <DepositFlag> 大额存单账户：1 有权限，0-无权限

 <GuarFlag>

保证金账户：1 有权限，0-无权限

 <LoanFlag>

贷款账户：1 有权限，0-无权限

2

2

2

2

 <Remark1>

备用字段 1

 <Remark2>

备用字段 2

 <Remark3>

备用字段 3

</xDataBody>

4.3. 例子

请求报文

12 / 19

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="QryAuthCifRelationInfo">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>2200328186</clientId>

<userId>2200328186680</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<AuthedCifNo>2200840164</AuthedCifNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none"

lang="chs" trnCode="AuthRelationDetail">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-09-01 19:33:16</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<pageSize>10</pageSize>

<pageNumber>1</pageNumber>

<DebitFlag>1</DebitFlag>

<DepositFlag>1</DepositFlag>

<GuarFlag>1</GuarFlag>

<LoanFlag>1</LoanFlag>

13 / 19

民生银行银企直联

<allNum>1</allNum>

<Remark1></Remark1>

<Remark2></Remark2>

<Remark3></Remark3>

</xDataBody>

</CMBC>

5. 集团客户总公司查询子公司信息(QryBlocAuthRelS

hpAllShow)

本部分更新日期:2024-09-06

5.1. 请求(QryBlocAuthRelShpAllShow)

  标记

说明

<xDataBody>

标记为★的为必填元素

 <insId>

客户端产生的交易唯一标志

 <pageNo>

页码：

最小为 1，且不能大于实际查询页数

 <pageSize>

查询笔数：

可选 10、20、50

</xDataBody>

5.2. 响应(QryBlocAuthRelShpAllShow)

  标记

说明

<xDataBody>

长度

32

8

8

长度

14 / 19

  标记

 <allNum>

说明

总记录数

 <customNo>

客户号

 <customName>

客户名称

<agreementNo>

协议编号

签约日期

签约机构

<signDate>

<signBranch>

<list>

<Map>

<subCustomNo>

成员客户号

<subCustomName>

成员客户名称

<subSignDate>

成员客户签约日期

<subCancleDate>

成员客户解约日期

<subStatus>

成员客户签约状态：

0-签约

1-解约

<subSignBranch>

成员客户签约机构

</Map>

</list>

</xDataBody>

民生银行银企直联

长度

8

15 / 19

5.3. 报文示例

请求报文：

民生银行银企直联

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs" trnCode="

QryBlocAuthRelShpAllShow">

<requestHeader>

<dtClient>2024-08-30 18:31:32</dtClient>

<clientId>2200046391</clientId>

<userId>2200046391001</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>CMBCINS202408301831321</insId>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs" trnCode="

QryBlocAuthRelShpAllShow">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

16 / 19

民生银行银企直联

<message>交易成功</message>

</status>

<dtServer>2024-09-03 14:18:31</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<customNo>2200046391</customNo>

<agreementNo>xybh0001</agreementNo>

<signBranch>3300</signBranch>

<customName>转账客户一</customName>

<signDate>20200629</signDate>

<list>

<Map>

<subCustomNo>2200260418</subCustomNo>

<subCustomName>法测三</subCustomName>

<subSignDate>20200630</subSignDate>

<subStatus>0</subStatus>

<subSignBranch>9001</subSignBranch>

</Map>

<Map>

<subCustomNo>2200362042</subCustomNo>

<subCustomName>集团对账银企子</subCustomName>

<subSignDate>20200720</subSignDate>

<subStatus>0</subStatus>

<subSignBranch>9001</subSignBranch>

</Map>

<Map>

<subCustomNo>2200006348</subCustomNo>

<subCustomName>UAT 结算通 1</subCustomName>

<subSignDate>20210816</subSignDate>

<subStatus>0</subStatus>

<subSignBranch>9001</subSignBranch>

</Map>

<Map>

<subCustomNo>2200038230</subCustomNo>

17 / 19

<subCustomName>高测试 011</subCustomName>

<subSignDate>20210817</subSignDate>

<subStatus>0</subStatus>

<subSignBranch>9001</subSignBranch>

民生银行银企直联

</Map>

</list>

<allNum>4</allNum>

</xDataBody>

</CMBC>

18 / 19

