银企直联接口文档

（账户管理-保证金）

邮箱：<EMAIL>

版本

日期

说明

编写者 审核者

文档修改记录

民生银行银企直联

V1.0.0 2021-03-18

定义接口文档

V1.1.0 2024-11-20

保证金账户信息查询

(QryGuarActList)返回字段修改，

<openDate>开户日

V1.1.1 2024-12-5

入参中增加 qryOpBankName（是

否查询交易对手开户行行名）字

段，若上送 0，则出参中返回

opBankName（交易对手开户行行

名）字段。

1 / 14

目录

民生银行银企直联

目录 .............................................................................................2

１ 集团保证金查询交易(QRYENSUACCT) ................................................. 3

查询本公司名下或本行授权账户的全部保证金账户。 .....................................3

1.1. 请求(QRYENSUACCT) ................................................................. 3

1.2. 响应(QRYENSUACCT) ................................................................. 3

1.3. 报文示例 ................................................................................. 4

保证金账户信息查询(QRYGUARACTLIST) ................................................6

2.1. 请求(QRYGUARACTLIST) .............................................................6

2.2. 响应(ELECTNOTELISTQRY) ........................................................... 7

2.3. 例子 ...................................................................................... 8

３ 保证金账户交易明细查询(QRYGUARACTTRANSLIST) ............................. 9

3.1. 请求(QRYGUARACTTRANSLIST) .................................................. 10

3.2. 响应(QRYGUARACTTRANSLIST) .................................................. 11

3.3. 例子 .................................................................................... 12

2 / 14

民生银行银企直联

１ 集团保证金查询交易(qryEnsuAcct)

本部分更新日期:2021-04-12

查询本公司名下或本行授权账户的全部保证金账户。

1.1. 请求(qryEnsuAcct)

  标记

说明

长度

<xDataBody>

</xDataBody>

1.2. 响应(qryEnsuAcct)

  标记

说明

长度

<xDataBody>

 <acctList>

账户列表开始

  <acctInfo>

0..n

  <acctName>

账户名称

  <acctPro>

账户性质

  <acctNo>

  <currNo>

账号

币种

  <acctBal>

账户余额

<rate>

利率

64

32

32

16

15，2

9,7

3 / 14

民生银行银企直联

长度

80

  标记

说明

  <openBranch> 开户机构

  <acctInfo>

 <acctList>

</xDataBody>

1.3. 报文示例

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="qryEnsuAcct">

<requestHeader>

<dtClient>20020615 10:20:45</dtClient>

<clientId>2001660306</clientId>

<userId>200166030601</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none" trnCode="qryEnsuAcct"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

4 / 14

<message>ok</message>

</status>

<dtServer>2008-09-17 09:53:43</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<acctList>

<acctInfo>

<acctName>a</acctName>

<acctPro>临时存款账户</acctPro>

<acctNo>0101014850005617</acctNo>

<currNo>01</currNo>

<acctBal>100000.00</acctBal>

<rate>0.7700000</rate>

<openBranch>中国民生银行总行营业部</openBranch>

</acctInfo>

<acctInfo>

<acctName>a</acctName>

<acctPro>基本账户</acctPro>

<acctNo>0101014850004518</acctNo>

<currNo>01</currNo>

<acctBal>2428494.34</acctBal>

<rate>0.7700000</rate>

<openBranch>中国民生银行总行营业部</openBranch>

</acctInfo>

<acctInfo>

<acctName>a</acctName>

<acctPro>基本账户</acctPro>

<acctNo>0101014850005480</acctNo>

<currNo>01</currNo>

<acctBal>30000.00</acctBal>

<rate>0.7700000</rate>

<openBranch>中国民生银行总行营业部</openBranch>

</acctInfo>

<acctInfo>

5 / 14

民生银行银企直联

<acctName>a</acctName>

<acctPro>基本账户</acctPro>

<acctNo>0101014850005471</acctNo>

<currNo>01</currNo>

<acctBal>50000.00</acctBal>

<rate>0.7700000</rate>

<openBranch>中国民生银行总行营业部</openBranch>

</acctInfo>

</acctList>

</xDataBody>

</CMBC>

２ 保证金账户信息查询(QryGuarActList)

本部分更新日期:2022-11-16

通过客户号来查询对应的保证金账户信息。

2.1. 请求(QryGuarActList)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

 <custNo>

客户号（不输入默认查当前客户）

 <extFields1> 备用字段（未启用）

 <extFields2> 备用字段（未启用）

 <extFields3> 备用字段（未启用）

</xDataBody>

长度

64

50

6 / 14

2.2. 响应(QryGuarActList)

民生银行银企直联

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作

64

用）(★)

 <allNum>

总笔数(★)

 <List>

  <Map>

   <acNo>

银行账号

   <acName>

账户名称

   <currency>

币种

   <balance>

账户余额

   <availBal>

可用余额

   <timType>

保证金类型

   <rate>

利率 %

   <deptName>

开户机构

   <openDate>

开户日

   <expireDate>

到期日 yyyy-MM-dd

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

  </Map>

8

32

120

50

15,2

15,2

20

6,4

200

10

10

7 / 14

说明

民生银行银企直联

长度

  标记

 </List>

</xDataBody>

2.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryGuarActList">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********412</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>wang20200602001</trnId>

<custNo></custNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryGuarActList" header="100"

lang="chs"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

8 / 14

民生银行银企直联

<message>ok</message>

</status>

<dtServer>2020-06-02 17:46:46</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>wang20200602001</trnId>

<allNum>1</allNum>

<List>

<Map>

<acNo>*********</acNo>

<acName>票据管家测试 003</acName>

<currency>人民币</currency>

<balance>709743.31</balance>

<availBal>709743.31</availBal>

<timType>活期</timType>

<rate>0.6900000000</rate>

<deptName>中国民生银行股份有限公司北京木樨地支行</deptName>

<openDate>2019-04-29</openDate>

<expireDate></expireDate>

</Map>

</List>

</xDataBody>

</CMBC>

３ 保证金账户交易明细查询(QryGuarActTransList)

本部分更新日期:2024-12-05

1. 查询一个账号在一段时间内的保证金账户交易明细。同一个账号不能在设定的时间内

连续进行起始记录条数是 1 的查询，其他的起始条数被认为是翻页查询，不受时间

间隔限制，目前时间限制设置是 15 分钟；

9 / 14

民生银行银企直联
2. 如果是从头开始查，开始条数请输入 1，银行会返回结束条数—开始条数条记录，比

如你输入开始条数 1、结束条数 21，银行会返回 20 条记录；

3. 请不要一次请求过多的条数，建议开始条数—结束条数=20 条；

3.1. 请求(QryGuarActTransList)

标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <acNo>

银行账号(★)

 <startNo>

开始条数，必须是有意义的数字(★)

 <endNo>

结束条数，必须是有意义的数字(★)

 <qryStartDate>

查询开始日期 (★)yyyy-MM-dd

 <qryEndDate>

查询截止日期(★)yyyy-MM-dd

<qryOpBankName> 是否查询交易对手开户行行名:（非必输）

 0：出参返回交易对手行名<opBankName>

 其他值或不传：不返回<opBankName>字段

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

 <extFields3>

备用字段（未启用）

</xDataBody>

50

8

8

10

10

10 / 14

  
3.2. 响应(QryGuarActTransList)

民生银行银企直联

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

(★)

 <allNum>

总笔数(★)

 <acNo>

银行账号

 <acName>

账户名称

 <List>

  <Map>

   <trsDate>

交易日期 yyyy-MM-dd

   <dcFlag>

借贷标志 1：借方 2：贷方

   <trsAmt>

交易金额

   <opAcNo>

对方账号

   <opAcName>

对方户名

   <balance>

余额

   <chequeNum> 凭证号

   <explain>

摘要

   <svrId>

流水号

   <remark>

银行附言

<opBankName> 交易对手开户行行名

长度

64

8

32

120

10

1

15,2

50

200

15,2

13

200

23

200

11 / 14

民生银行银企直联

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

   <extFields3>

备用字段（未启用）

  <Map>

 <List>

</xDataBody>

3.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryGuarActTransList">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********412</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>wang20200602001</trnId>

<acNo>*********</acNo>

<startNo>1</startNo>

<endNo>100</endNo>

<qryStartDate>2020-01-01</qryStartDate>

<qryEndDate>2020-04-01</qryEndDate>

</xDataBody>

</CMBC>

响应报文

12 / 14

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="QryGuarActTransList" header="100"

民生银行银企直联

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-06-03 17:35:39</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>wang20200602001</trnId>

<allNum>1</allNum>

<acNo>*********</acNo>

<acName>票据管家测试 003</acName>

<List>

<Map>

<svrId>00000000000000000000000000000000</svrId>

<trsDate>2020-03-20</trsDate>

<dcFlag>2</dcFlag>

<trsAmt>1235.76</trsAmt>

<opAcNo></opAcNo>

<opAcName></opAcName>

<balance>709743.31</balance>

<chequeNum></chequeNum>

<explain>结息</explain>

<remark></remark>

</Map>

</List>

</xDataBody>

</CMBC>

13 / 14

