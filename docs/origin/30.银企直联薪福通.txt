银企直联接口文档

（薪福通）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 95

目录

民生银行银企直联

目录 .............................................................................................2

1. 签约账号列表查询(B2EQRYCUSTOMERSIGNINFOLIST) ........................ 6

1.1. 请求(B2EQRYCUSTOMERSIGNINFOLIST) ..........................................6

1.2. 响应(B2EQRYCUSTOMERSIGNINFOLIST) ..........................................6

1.3. 例子 ...................................................................................... 7

2. 项目子账簿与特殊子账簿列表查询(B2EQRYALLVIRTUALACNOBYSIGNNO)10

2.1. 请求(B2EQRYALLVIRTUALACNOBYSIGNNO) .................................. 10

2.2. 响应(B2EQRYALLVIRTUALACNOBYSIGNNO) .................................. 10

2.3. 例子 .................................................................................... 11

3. 项目子账簿列表查询(B2EGETVIRTUALACNOINFOBYSIGNNO) ............. 13

3.1. 请求(B2EGETVIRTUALACNOINFOBYSIGNNO) ................................. 13

3.2. 响应(B2EGETVIRTUALACNOINFOBYSIGNNO) ................................. 14

3.3. 例子 .................................................................................... 15

4. 子账簿余额查询(B2EQRYSALARYVACCTBALANCE) ........................... 20

4.1. 请求(B2EQRYSALARYVACCTBALANCE) ..........................................20

4.2. 响应(B2EQRYSALARYVACCTBALANCE) ..........................................20

4.3. 例子 .................................................................................... 21

5. 子账簿新增(B2EVIRTUALACCOUNTADD) ........................................ 22

5.1. 请求(B2EVIRTUALACCOUNTADD) ................................................ 22

5.2. 响应(B2EVIRTUALACCOUNTADD) ................................................ 23

5.3. 例子 .................................................................................... 23

6. 子账簿销户(B2EVIRTUALACCOUNTCANCEL) ................................... 24

6.1. 请求（B2EVIRTUALACCOUNTCANCEL） ......................................... 25

6.2. 响应（B2EVIRTUALACCOUNTCANCEL） ......................................... 25

2 / 95

民生银行银企直联
6.3. 例子 .................................................................................... 25

7. 子账簿修改(B2EVIRTUALACCOUNTMODIFY) ................................... 26

7.1. 请求(B2EVIRTUALACCOUNTMODIFY) ............................................ 27

7.2. 响应(B2EVIRTUALACCOUNTMODIFY) ............................................ 28

7.3. 例子 .................................................................................... 28

8. 子账簿批量出金(B2ESALARYMGNBATCHTRANSFER) ........................ 29

8.1. 请求(B2ESALARYMGNBATCHTRANSFER) ....................................... 29

8.2. 响应(B2ESALARYMGNBATCHTRANSFER) ....................................... 31

8.3. 例子 .................................................................................... 31

9. 子账簿单笔出金(B2ESALARYMGNTRANSFER) .................................. 33

9.1. 请求(B2ESALARYMGNTRANSFER) ................................................ 33

9.2. 响应(B2ESALARYMGNTRANSFER) ................................................ 35

9.3. 例子 .................................................................................... 35

10. 子账簿间转账(B2EVIRTUALACCOUNTEACHTRANSFER) .................. 37

10.1. 请求（B2EVIRTUALACCOUNTEACHTRANSFER） ..............................37

10.2. 响应（B2EVIRTUALACCOUNTEACHTRANSFER） ..............................37

10.3. 例子 ...................................................................................38

11. 子账簿交易明细查询(B2EVIRTUALACCOUNTTRANSFERDETAIL) ........ 39

11.1. 请求(B2EVIRTUALACCOUNTTRANSFERDETAIL) .............................. 40

11.2. 响应(B2EVIRTUALACCOUNTTRANSFERDETAIL) .............................. 40

11.3. 例子 ...................................................................................41

12. 子账簿批量出金结果查询(B2EQRYSALARYMGNBATCHLIST) ...............45

12.1. 请求(B2EQRYSALARYMGNBATCHLIST) ........................................45

12.2. 响应(B2EQRYSALARYMGNBATCHLIST) ........................................45

12.3. 例子 ...................................................................................46

3 / 95

民生银行银企直联
13. 子账簿批量转账明细(B2EQRYSALARYMGNBATCHTRANSFERDETAIL) 50

13.1. 请求（B2EQRYSALARYMGNBATCHTRANSFERDETAIL） .................... 50

13.2. 响应（B2EQRYSALARYMGNBATCHTRANSFERDETAIL） .................... 51

13.3. 例子 ...................................................................................53

14. 待清分账簿入账明细(B2EQRYCLARIFYVACCTNOTRANSFERDETAIL) .. 55

14.1. 请求(B2EQRYCLARIFYVACCTNOTRANSFERDETAIL) ......................... 55

14.2. 响应(B2EQRYCLARIFYVACCTNOTRANSFERDETAIL) ......................... 56

14.3. 例子 ...................................................................................57

15. 子账簿调账(B2EVIRTUALACCOUNTRECONCILIATION) ....................60

15.1. 请求（B2EVIRTUALACCOUNTRECONCILIATION） .............................61

15.2. 响应（B2EVIRTUALACCOUNTRECONCILIATION） .............................62

15.3. 例子 ...................................................................................62

16. 实账户模式单笔出金(B2ESALARYMGNREALTRANSFER) ................... 64

16.1. 请求(B2ESALARYMGNREALTRANSFER) ....................................... 64

16.2. 响应(B2ESALARYMGNREALTRANSFER) ....................................... 65

16.3. 例子 ...................................................................................66

17. 实账户模式批量出金(B2ESALARYMGNREALBATCHTRANSFER) ......... 68

17.1. 请求(B2ESALARYMGNREALBATCHTRANSFER) ............................... 68

17.2. 响应(B2ESALARYMGNREALBATCHTRANSFER) ............................... 69

17.3. 例子 ...................................................................................70

18. 实账户批量出金结果查询(B2EQRYSALARYMGNREALBATCHLIST) ....... 71

18.1. 请求(B2EQRYSALARYMGNREALBATCHLIST) ................................. 71

18.2. 响应(B2EQRYSALARYMGNREALBATCHLIST) ................................. 72

18.3. 例子 ...................................................................................73

19. 实账户批量转账明细(B2EQRYSALARYMGNBATCHREALDETAIL) ........ 75

4 / 95

民生银行银企直联
19.1. 请求(B2EQRYSALARYMGNBATCHREALDETAIL) ..............................75

19.2. 响应(B2EQRYSALARYMGNBATCHREALDETAIL) ..............................76

19.3. 例子 ...................................................................................79

20. 实账户批量退汇查询 (B2EQRYSALARYMGNBATCHREALREEXLIST) ....83

20.1. 请求(B2EQRYSALARYMGNBATCHREALREEXLIST) .......................... 83

20.2. 响应(B2EQRYSALARYMGNBATCHREALREEXLIST) .......................... 84

20.3. 例子 ...................................................................................85

21. 批量代发批次结果详情 PDF 文件下载

(B2EQRYCOMMONSALARYMGNBATCHBYFILE) ................................ 86

21.1. 请求(B2EQRYCOMMONSALARYMGNBATCHBYFILE) ........................ 87

21.2. 响应(B2EQRYCOMMONSALARYMGNBATCHBYFILE) ........................ 87

21.3. 例子 ...................................................................................88

22. 工资卡信息校验(B2ESALARYCHECKBANKCARD) ............................ 90

22.1. 请求(B2ESALARYCHECKBANKCARD) ...........................................90

22.2. 响应(B2ESALARYCHECKBANKCARD) ...........................................90

22.3. 报文示例 ............................................................................. 91

23. 工资卡信息校验(B2ESALARYCHECKBANKCARD) ............................ 92

23.1. 请求(B2ESALARYCHECKBANKCARD) ...........................................92

23.2. 响应(B2ESALARYCHECKBANKCARD) ...........................................92

23.3. 报文示例 ............................................................................. 93

5 / 95

1.签约账号列表查询(B2eQryCustomerSignInfoLis

民生银行银企直联

t)

本部分更新日期:2022-02-15

用于查询总公司及授权公司已签约薪福通账户信息，该接口是全部服务的基础接口

1.1.请求(B2eQryCustomerSignInfoList)

标记

说明

<xDataBody>

   <acPermit> 账户权限（★）

1：查询权限

2：查询并转帐

</xDataBody>

长度

1

1.2.响应(B2eQryCustomerSignInfoList)

标记

说明

长度

<xDataBody>

   <signAcNoList>

签约账号列表

    <Map>

    <customerSignId>

客户签约编码

    <acctNo>

实账户账号

    <acctName>

实账户名称

32

32

240

6 / 95

标记

说明

    <deptName>

开户行名称

    <currency>

币种

    <interestVacctNo>

利息子账簿账号

    <clarifyVacctNo>

待清分子账簿账号

    <ownVacctNo>

自有子账簿账号

    <isUEBAllotVacctPerm> 是否签约子账簿权限

0：否；

1：是；

民生银行银企直联

长度

150

5

32

32

32

1

    </Map>

   </signAcNoLis>

</xDataBody>

1.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryCustomerSignInfoList">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<acPermit>2</acPermit>

7 / 95

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryCustomerSignInfoList">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 11:25:12</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<signAcNoList>

<Map>

<deptName>中国民生银行股份有限公司长春民丰大街支行</deptName>

<acctNo>*********</acctNo>

<currency>CNY</currency>

<isUEBAllotVacctPerm>1</isUEBAllotVacctPerm>

<clarifyVacctNo>****************</clarifyVacctNo>

<acctName>开放银行测试 7</acctName>

<ownVacctNo>****************</ownVacctNo>

<customerSignId>01202105130947000001</customerSignId>

<interestVacctNo>9902000305200628</interestVacctNo>

</Map>

<Map>

<deptName>中国民生银行股份有限公司长春民丰大街支行</deptName>

<acctNo>*********</acctNo>

<currency>CNY</currency>

<isUEBAllotVacctPerm>0</isUEBAllotVacctPerm>

<clarifyVacctNo>9902000305221988</clarifyVacctNo>

8 / 95

<acctName>开放银行测试 7</acctName>

<ownVacctNo>9902000305222002</ownVacctNo>

<customerSignId>01202105171052490045</customerSignId>

<interestVacctNo>9902000305221996</interestVacctNo>

民生银行银企直联

</Map>

<Map>

<deptName>中国民生银行股份有限公司长春民丰大街支行</deptName>

<acctNo>624277225</acctNo>

<currency>CNY</currency>

<isUEBAllotVacctPerm>0</isUEBAllotVacctPerm>

<clarifyVacctNo>9902000305228488</clarifyVacctNo>

<acctName>开放银行测试 7</acctName>

<ownVacctNo>9902000305228504</ownVacctNo>

<customerSignId>01********1448060009</customerSignId>

<interestVacctNo>9902000305228496</interestVacctNo>

</Map>

<Map>

<deptName>中国民生银行股份有限公司长春民丰大街支行</deptName>

<acctNo>*********</acctNo>

<currency>CNY</currency>

<isUEBAllotVacctPerm>0</isUEBAllotVacctPerm>

<clarifyVacctNo>9902000305383887</clarifyVacctNo>

<acctName>开放银行测试 7</acctName>

<ownVacctNo>9902000305383903</ownVacctNo>

<customerSignId>01202110251508000005</customerSignId>

<interestVacctNo>9902000305383895</interestVacctNo>

</Map>

</signAcNoList>

</xDataBody>

</CMBC>

9 / 95

民生银行银企直联
2.项目子账簿与特殊子账簿列表查询(B2eQryAllVirtua

lAcNoBySignNo)

本部分更新日期:2022-02-15

查询实账户项下待清分子账簿、利息子账簿、项目子账簿信息，该接口用于薪福通单笔转

账、批量代发、薪福通转账服务中查询子账簿信息列表

2.1.请求(B2eQryAllVirtualAcNoBySignNo)

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <accountNo>

实账户账号（★）

</xDataBody>

2.2.响应(B2eQryAllVirtualAcNoBySignNo)

长度

32

32

标记

说明

长度

<xDataBody>

   <vacctNoList>

项目子账簿列表

    <Map>

    <vacctNo>

子账簿账号

    <vacctBusiName>

子账簿简称

    <isApprove>

用工单位审批标志位

32

240

2

10 / 95

标记

说明

0：否；

1：是

    </Map>

   </vacctNoList>

   <interestVacctNoList> 利息子账簿列表

    <Map>

    <vacctNo>

子账簿账号

    <vacctBusiName>

子账簿简称

民生银行银企直联

长度

32

240

    <isApprove>

用工单位审批标志位 0：否；1：是

2

    </Map>

   </interestVacctNoList>

   <ownVacctNoList>

自有资金子账簿列表

    <Map>

    <vacctNo>

子账簿账号

    <vacctBusiName>

子账簿简称

32

240

    <isApprove>

用工单位审批标志位 0：否；1：是

2

    </Map>

   </ownVacctNoList>

</xDataBody>

2.3.例子

请求报文

11 / 95

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="B2eQryAllVirtualAcNoBySignNo">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<accountNo>*********</accountNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryAllVirtualAcNoBySignNo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 11:32:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<ownVacctNoList>

<Map>

<vacctNo>****************</vacctNo>

<vacctBusiName>自有资金子账簿</vacctBusiName>

12 / 95

民生银行银企直联

<isApprove></isApprove>

</Map>

</ownVacctNoList>

<interestVacctNoList>

<Map>

<vacctNo>****************</vacctNo>

<vacctBusiName>开放银行测试 1</vacctBusiName>

<isApprove>0</isApprove>

</Map>

<Map>

<vacctNo>****************</vacctNo>

<vacctBusiName>李明</vacctBusiName>

<isApprove>1</isApprove>

</Map>

<Map>

<vacctNo>****************</vacctNo>

<vacctBusiName>��浜�</vacctBusiName>

<isApprove>0</isApprove>

</Map>

</vacctNoList>

</xDataBody>

</CMBC>

3.项目子账簿列表查询(B2eGetVirtualAcNoInfoBy

SignNo)

本部分更新日期:2022-02-15

查询总公司及子公司签约账号下的子账簿列表，该接口用于薪福通管理中对子账簿进行修

改与删除时获取子账簿列表。

3.1.请求(B2eGetVirtualAcNoInfoBySignNo)

标记

说明

长度

13 / 95

标记

说明

长度

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <accountNo>

实账户账号（★）

32

32

   <pageNo>

当前页（首页传 1）

5

   <pageSize>

查询行数（最大 50） 5

</xDataBody>

3.2.响应(B2eGetVirtualAcNoInfoBySignNo)

标记

<xDataBody>

   <total>

   <list>

    <Map>

说明

总笔数

结果集

    <vacctBusiId>

子账簿业务编号

    <vacctBusiName>

子账簿简称

    <contactsName>

联系人名称

    <contactsMobileNo>

联系人手机号码

    <bindAcctName>

绑定账户名称

    <remark>

备注

    <vacctNo>

子账簿账号

民生银行银企直联

长度

5

32

240

150

11

240

300

32

14 / 95

标记

说明

    <vacctBalance>

子账簿余额

    <isApprove>

子账簿交易审批

    <customerRegisterNo>

现金盈客户注册号

    <customerRegisterName> 联系人名称

    <contactsName>

现金盈客户名称

民生银行银企直联

长度

16,2

1

32

150

240

    </Map>

   </list>

</xDataBody>

3.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eGetVirtualAcNoInfoBySignNo">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<accountNo>*********</accountNo>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<permitFlag>N</permitFlag>

15 / 95

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eGetVirtualAcNoInfoBySignNo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 14:58:43</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>59</total>

<list>

<Map>

<vacctBusiId>91101202105201458003410911023423</vacctBusiId>

<vacctBusiName>测试</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo>***********</contactsMobileNo>

<bindAcctName>测试数据</bindAcctName>

<remark></remark>

<vacctNo>****************</vacctNo>

<vacctBalance>*************.00</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101202105241437550373911000375</vacctBusiId>

<vacctBusiName>需要审批审批</vacctBusiName>

<contactsName></contactsName>

16 / 95

民生银行银企直联

<contactsMobileNo></contactsMobileNo>

<bindAcctName>需要审批审批</bindAcctName>

<remark></remark>

<vacctNo>****************</vacctNo>

<vacctBalance>977760.00</vacctBalance>

<isApprove>1</isApprove>

<customerRegisterNo>9595000032136005</customerRegisterNo>

<customerRegisterName>页面一</customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101********1512350207911000208</vacctBusiId>

<vacctBusiName>李明</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo></contactsMobileNo>

<bindAcctName>李明</bindAcctName>

<remark></remark>

<vacctNo>9902000305249153</vacctNo>

<vacctBalance>500.00</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101********1513060213911000214</vacctBusiId>

<vacctBusiName>开放银行测试 1</vacctBusiName>

<contactsName>罗冰峰</contactsName>

<contactsMobileNo>13788988842</contactsMobileNo>

<bindAcctName>殷悦勇</bindAcctName>

<remark></remark>

<vacctNo>****************</vacctNo>

<vacctBalance>9.98</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101********1730101362911001399</vacctBusiId>

17 / 95

民生银行银企直联

<vacctBusiName>李明</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo></contactsMobileNo>

<bindAcctName>李明</bindAcctName>

<remark></remark>

<vacctNo>****************</vacctNo>

<vacctBalance>11436.02</vacctBalance>

<isApprove>1</isApprove>

<customerRegisterNo>null78018</customerRegisterNo>

<customerRegisterName>7</customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101********1803391862911001900</vacctBusiId>

<vacctBusiName>李明</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo></contactsMobileNo>

<bindAcctName>李明</bindAcctName>

<remark></remark>

<vacctNo>****************</vacctNo>

<vacctBalance>5005.00</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101202106241657138079911008104</vacctBusiId>

<vacctBusiName>澶�AA</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo>25236545120</contactsMobileNo>

<bindAcctName></bindAcctName>

<remark></remark>

<vacctNo>9902000305330524</vacctNo>

<vacctBalance></vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

18 / 95

民生银行银企直联

<Map>

<vacctBusiId>91101202107051002223585911033594</vacctBusiId>

<vacctBusiName>大 AA</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo>***********</contactsMobileNo>

<bindAcctName>大 AA</bindAcctName>

<remark></remark>

<vacctNo>****************</vacctNo>

<vacctBalance>0.00</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101202107051005113613911033622</vacctBusiId>

<vacctBusiName>��浜�</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo>***********</contactsMobileNo>

<bindAcctName>��浜�</bindAcctName>

<remark></remark>

<vacctNo>9902000305332108</vacctNo>

<vacctBalance>0.00</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</Map>

<Map>

<vacctBusiId>91101202107071649052238911012237</vacctBusiId>

<vacctBusiName>璧靛��</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo>***********</contactsMobileNo>

<bindAcctName>璧靛��</bindAcctName>

<remark></remark>

<vacctNo>9902000305332892</vacctNo>

<vacctBalance>0.00</vacctBalance>

<isApprove>0</isApprove>

<customerRegisterNo></customerRegisterNo>

19 / 95

<customerRegisterName></customerRegisterName>

民生银行银企直联

</Map>

</list>

</xDataBody>

</CMBC>

4.子账簿余额查询(B2eQrySalaryVacctBalance)

本部分更新日期:2022-02-15

查询子账簿余额，用于薪福通单笔转账、批量代发、子账簿间转账的账户金额查询。

4.1.请求(B2eQrySalaryVacctBalance)

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <vacctNo>

子账户（★）

</xDataBody>

4.2.响应(B2eQrySalaryVacctBalance)

标记

说明

<xDataBody>

   <vacctBalance> 子账户余额

</xDataBody>

长度

32

32

长度

15

20 / 95

民生银行银企直联

4.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryVacctBalance">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<vacctNo>****************</vacctNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryVacctBalance">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-15 17:24:27</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<vacctBalance>*************.59</vacctBalance>

21 / 95

</xDataBody>

</CMBC>

民生银行银企直联

5.子账簿新增(B2eVirtualAccountAdd)

本部分更新日期:2022-02-15

新增总公司及授权公司薪福通子账簿，用于新增实账户名下的子账簿信息

5.1.请求(B2eVirtualAccountAdd)

标记

说明

<xDataBody>

   <customerSignId>

客户签约编码（★）

   <acctNo>

签约实账户账号（★）

   <draweeAcctName>

用工单位付款名称（★）

   <vacctBusiName>

用工简称（★）

   <contactsName>

联系人名称

   <contactsMobileNo>

用工单位联系人手机号 该字段只在

isTransApprove=0 时有效，

isTransApprove=1 时无法添加

长度

32

32

32

240

150

11

   <isTransApprove>

是否用工单位审批（★）

1

0：否；

1：是；

   <customerRegisterNo>

现金盈客户注册号(用工单位) 当

32

isTransApprove=1 时必输

22 / 95

标记

说明

   <customerRegisterName> 客户名称（用工单位） 当

isTransApprove=1 时必输

民生银行银企直联

长度

240

</xDataBody>

5.2.响应(B2eVirtualAccountAdd)

标记

说明

<xDataBody>

   <vacctNo>

子账簿帐号

   <vacctName> 子账簿名称

</xDataBody>

5.3.例子

请求报文

长度

32

240

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountAdd">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<acctNo>*********</acctNo>

23 / 95

民生银行银企直联

<draweeAcctName>中国宜家</draweeAcctName>

<vacctBusiName>中国宜</vacctBusiName>

<contactsName></contactsName>

<contactsMobileNo>***********</contactsMobileNo>

<isTransApprove>0</isTransApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName></customerRegisterName>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountAdd">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 14:36:16</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<vacctName>开放银行测试 7</vacctName>

<vacctNo>****************</vacctNo>

</xDataBody>

</CMBC>

6.子账簿销户(B2eVirtualAccountCancel)

本部分更新日期:2022-02-15

24 / 95

民生银行银企直联
删除总公司及授权公司薪福通子账簿，需要先查询 B2eGetVirtualAcNoInfoBySignNo

服务获取子账簿信息。

6.1.请求（B2eVirtualAccountCancel）

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <vacctNo>

子账簿账号（★）

   <summary>

备注

</xDataBody>

6.2.响应（B2eVirtualAccountCancel）

标记

说明

长度

长度

32

32

300

<xDataBody>

</xDataBody>

6.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountCancel">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

25 / 95

民生银行银企直联

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<vacctNo>****************</vacctNo>

<summary></summary>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountCancel">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>
<message>交易成功</message>

</status>

<dtServer>2021-10-26 14:33:27</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<language>chs</language>

</responseHeader>

<xDataBody />

</CMBC>

7.子账簿修改(B2eVirtualAccountModify)

本部分更新日期:2022-02-15

修改总公司及授权公司薪福通子账簿信息。需要先查询

B2eGetVirtualAcNoInfoBySignNo 服务获取子账簿信息。

26 / 95

7.1.请求(B2eVirtualAccountModify)

民生银行银企直联

标记

说明

<xDataBody>

   <customerSignId>

客户签约编码（★）

   <vacctNo>

子账簿账号（★）

   <vacctBusiId>

子账簿业务 Id（★）

   <vacctBusiName>

子账簿业务名称（★）

   <draweeAcctName>

用工单位付款账户名称（★）

   <contactsName>

联系人名称

   <contactsMobileNo>

联系人手机号

   <isTransApprove>

子账簿交易审批（★）

0：否；

1：是；

   <customerRegisterNo>

现金盈注册号 当

isTransApprove=1 时必输

长度

32

32

32

240

32

15

11

1

32

   <customerRegisterName> 客户名称 当 isTransApprove=1 时

240

必输

</xDataBody>

说明：vacctBusiId 对应 B2eGetVirtualAcNoInfoBySignNo 服务中 vacctBusiId 字

段 vacctBusiName 对应 B2eGetVirtualAcNoInfoBySignNo 服务中 vacctBusiName

字段 contactsMobileNo 该字段只在 isTransApprove=0 时有效，isTransApprove=1

时无法修改

27 / 95

7.2.响应(B2eVirtualAccountModify)

民生银行银企直联

标记

说明

长度

<xDataBody>

</xDataBody>

7.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountModify">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<vacctNo>****************</vacctNo>

<vacctBusiId>91101202107051002223585911033594</vacctBusiId>

<vacctBusiName>大 AA</vacctBusiName>

<draweeAcctName>大 AA</draweeAcctName>

<contactsName>

</contactsName>

<contactsMobileNo>***********</contactsMobileNo>

<isTransApprove>0</isTransApprove>

<customerRegisterNo></customerRegisterNo>

<customerRegisterName>

</customerRegisterName>

<remark>

28 / 95

民生银行银企直联

</remark>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountModify">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 14:33:27</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<language>chs</language>

</responseHeader>

<xDataBody />

</CMBC>

8.子账簿批量出金(B2eSalaryMgnBatchTransfer)

本部分更新日期:2022-04-11

操作总公司及子公司子账簿进行批量代发。

8.1.请求(B2eSalaryMgnBatchTransfer)

标记

说明

长度

<xDataBody>

29 / 95

标记

说明

   <DraweeVacctNo>

付款子帐号（★）

   <DraweeVacctName> 付款子账户名称（★）

   <payerAcct>

付款实体账户（★）

   <payerName>

付款实体帐号名称（★）

   <CustomerSignId>

客户签约编码（★）

   <CertNo>

企业自制凭证号

   <amount>

转出金额（★）

   <currency>

币种（★）RMB

   <Usage>

用途（★）

民生银行银企直联

长度

32

240

32

240

32

8

15

3

20

   <Remark>

备注 用途和备注总和不得超过 24 个汉字 70

   <TotalCount>

总笔数（★）

4

   <FileNameContent>

文件内容（★）最大 500 笔数据

</xDataBody>

说明：DraweeVacctNo 包含自有资金子账簿、普通子账簿

DraweeVacctName 自有资金子账簿对应名称-自有资金子账簿，普通子账簿对应名

称-子账簿对应用工单位简称 Usage：字典：工资、绩效工资、奖金、补贴、报销、

劳务费、佣金、服务费

Remark：字典：一月, 二月, 三月, 四月, 五月, 六月, 七月, 八月, 九月, 十月, 十一月,

十二月, 一季度, 二季度, 三季度, 四季度, 半年, 年终+ 自定义值（客户自己输入）

FileNameContent: 明细序号|收款人姓名|收款人账号|交易金额|收款人开户行行号|收

款人开 户行名称|收款人证件号码|收款人手机号码|

30 / 95

每行数据以^拼接；

其中明细序号、收款人姓名、收款人账号、交易金额为必输项

民生银行银企直联

收款人开户行行号、收款人开户行名称、收款人证件号码、收款人手机号码为非必输项

明细序号从 1 开始累加

交易金额薪资发放金额，保留 2 位小数

8.2.响应(B2eSalaryMgnBatchTransfer)

长度

32

32

50

32

标记

说明

<xDataBody>

   <_JnlNo>

流水号

   <CertNo>

凭证号

   <UserName> 制单员

   <applyId>

批次请求流水号

</xDataBody>

8.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnBatchTransfer">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

31 / 95

民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<DraweeVacctNo>****************</DraweeVacctNo>

<DraweeVacctName>测试</DraweeVacctName>

<payerAcct>*********</payerAcct>

<payerName>开放银行测试 7</payerName>

<CustomerSignId>01202105130947000001</CustomerSignId>

<CertNo></CertNo>

<amount>3.00</amount>

<currency>RMB</currency>

<Usage>工资</Usage>

<Remark></Remark>

<TotalCount>3</TotalCount>

<FileNameContent>1| 胡思梦|****************|1|||||^2| 刘平金

|6228480018536951272|1|************| 中国农业银行

|110104195010102440|***********|^3| 陶燕

|************|1||||***********|</FileNameContent>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnBatchTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

31 / 52

</status>

<dtServer>2021-07-02 10:47:48</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

32 / 95

民生银行银企直联

<xDataBody>

<returnCode>

<code>AAAAAAA</code>

<type>S</type>

</returnCode>

<_JnlNo>20210702100297528200</_JnlNo>

<UserName>开放银行测 896</UserName>

<CertNo>9800000000117</CertNo>

<applyId>91120220411110940579388911579389</applyId>

</xDataBody>

</CMBC>

9.子账簿单笔出金(B2eSalaryMgnTransfer)

本部分更新日期:2022-02-15

操作总公司及子公司子账簿进行单笔代发。

9.1.请求(B2eSalaryMgnTransfer)

标记

说明

<xDataBody>

   <DraweeVacctNo> 付款子帐号（★）

   <payerAcct>

付款实体账户（★）

   <payerName>

付款实体帐号名称（★）

   <CustomerSignId> 客户签约编码（★）

   <payeeAcct>

收款账号（★）

   <payeeName>

收款帐号名称（★）

长度

32

32

240

32

32

240

33 / 95

标记

说明

   <PayeeDeptName> 收款帐户开户机构名称

   <PayeeBankNo>

收款帐户开户行号

   <amount>

转出金额（★）

   <currency>

币种（★）CNY

   <Usage>

用途（★）

民生银行银企直联

长度

240

32

15

3

128

   <Remark>

备注 当 Usage 为其他时，Remark 必输 用途

25

和备注总和不得超过 69 个汉字

   <SysFlag>

行内外标志（★）

0：本行

1：他行

   <EntPerFlag>

收款帐户类型

0：对公

1：对私

   <vacctBusiName>

用工单位简称

   <deptName>

付款账户开户行

   <CertNo>

企业自制凭证号

</xDataBody>

1

1

1

240

32

说明：DraweeVacctNo 包含自有资金子账簿、利息子账簿、普通子账簿 Usage 字

典 ：当为对私转账时：工资、绩效工资、奖金、补贴、报销、劳务费、佣金、服务费 当

为对公转账时：网下申购, 贷款, 往来结算款, 工资奖金, 差旅费, 租赁费, 办公费, 合同款,

水电费, 运费, 工程款,劳务费, 通讯费, 交通费, 报刊费, 餐费, 增资款, 投资款, 其

他 vacctBusiName 自有资金子账簿对应名称-自有资金子账簿,利息子账簿对应名称-利

息子账簿,普通子账簿对应名称-子账簿对应用工单位简称

34 / 95

9.2.响应(B2eSalaryMgnTransfer)

民生银行银企直联

标记

说明

<xDataBody>

   <JnlNo>

流水号

   <CertNo>

凭证号

   <UserName> 制单员

</xDataBody>

9.3.例子

请求报文

长度

32

32

50

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnTransfer">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<DraweeVacctNo>****************</DraweeVacctNo>

<payerAcct>*********</payerAcct>

<payerName>开放银行测试 7</payerName>

<CustomerSignId>01202105130947000001</CustomerSignId>

<CertNo></CertNo>

<payeeAcct>*********</payeeAcct>

<payeeName>开放银行测试 7</payeeName>

35 / 95

民生银行银企直联

<PayeeDeptName></PayeeDeptName>

<PayeeBankNo></PayeeBankNo>

<amount>100.00</amount>

<currency>CNY</currency>

<Usage>贷款</Usage>

<Remark>666</Remark>

<SysFlag>0</SysFlag>

<vacctBusiName></vacctBusiName>

<deptName>中国民生银行股份有限公司长春民丰大街支行</deptName>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-07-21 16:33:38</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<UserName>开放银行测 896</UserName>

<_JnlNo>20210721160436241800</_JnlNo>

<CertNo>*************</CertNo>

</xDataBody>

</CMBC>

36 / 95

10.子账簿间转账(B2eVirtualAccountEachTransf

民生银行银企直联

er)

本部分更新日期:2022-02-15

总公司及子公司进行子账簿间的资金划转。

10.1.请求（B2eVirtualAccountEachTransfer）

长度

32

32

240

32

32

240

15

3

50

标记

说明

<xDataBody>

   <DraweeVacctNo> 付款子帐号（★）

   <payerAcct>

付款实体账户（★）

   <payerName>

付款实体帐号名称（★）

   <CustomerSignId> 客户签约编码（★）

   <payeeAcct>

收款账号（★）

   <payeeName>

收款帐号名称（★）

   <amount>

转出金额（★）

   <currency>

币种（★）RMB

   <Remark>

备注 最多支持 50 个汉字

</xDataBody>

10.2.响应（B2eVirtualAccountEachTransfer）

标记

说明

长度

37 / 95

民生银行银企直联

标记

说明

长度

<xDataBody>

   <JnlNo>

流水号 32

   <CertNo>

凭证号 32

   <UserName> 制单员 50

</xDataBody>

10.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountEachTransfer">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<DraweeVacctNo>****************</DraweeVacctNo>

<payerAcct>*********</payerAcct>

<payerName>开放银行测试 7</payerName>

<CustomerSignId>01202105130947000001</CustomerSignId>

<payeeAcct>****************</payeeAcct>

<payeeName>李明</payeeName>

<amount>200.00</amount>

<currency>RMB</currency>

<Remark></Remark>

38 / 95

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountEachTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-07-02 16:43:47</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<returnCode>

<code>AAAAAAA</code>

<type>S</type>

</returnCode>

<_JnlNo>20210702160301956800</_JnlNo>

<UserName>开放银行测 896</UserName>

<CertNo>*************</CertNo>

</xDataBody>

</CMBC>

11.子账簿交易明细查询(B2eVirtualAccountTransf

erDetail)

本部分更新日期:2022-02-15

总公司及子公司查询子账簿交易明细。

39 / 95

本交易用于查询薪福通单笔转账、子账簿间转账、子账簿间调账的交易后查询子账簿的交

民生银行银企直联

易明细信息。

11.1.请求(B2eVirtualAccountTransferDetail)

标记

说明

长度

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <vacctNo>

子账簿账号（★）

   <dateFrom>

开始日期(yyyyMMdd)（★）

   <dateTo>

结束日期(yyyyMMdd)（★）

   <pageNo>

当前页 首页传 1

   <pageSize>

查询行数 最多支持 50

</xDataBody>

11.2.响应(B2eVirtualAccountTransferDetail)

标记

说明

<xDataBody>

   <total>

总笔数

   <creditTotalAmount> 借方汇总金额

   <creditTotalCount>

借方汇总笔数

   <debitTotalAmount> 贷方汇总金额

32

32

8

8

5

5

长度

5

16,2

16,2

16,2

40 / 95

民生银行银企直联

长度

16,2

8

16,2

16,2

32

240

300

300

300

标记

说明

   <debitTotalCount>

贷方汇总笔数

   <list>

结果集

    <Map>

    <transDate>

交易日期

    <creditAmount>

借方交易金额

    <debitAmount>

贷方交易金额

    <otherAcctNo>

对方账号

    <otherAcctName> 对方账号名称

    <otherPartyName> 对方账户开户行名称

    <postscript>

    <summary>

摘要

备注

    </Map>

   </lis>

</xDataBody>

11.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountTransferDetail">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

41 / 95

民生银行银企直联

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<vacctNo>****************</vacctNo>

<dateFrom>********</dateFrom>

<dateTo>********</dateTo>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountTransferDetail">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 15:20:22</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<creditTotalCount>5</creditTotalCount>

<debitTotalCount>2</debitTotalCount>

<total>7</total>

<debitTotalAmount>3000000.00</debitTotalAmount>

<list>

<Map>

<transDate>********</transDate>

<creditAmount>2010000.00</creditAmount>

42 / 95

民生银行银企直联

<otherAcctNo></otherAcctNo>

<otherAcctName></otherAcctName>

<otherPartyName></otherPartyName>

<postscript>服务费年终</postscript>

<summary>服务费年终</summary>

</Map>

<Map>

<transDate>********</transDate>

<debitAmount>2000000.00</debitAmount>

<otherAcctNo>****************</otherAcctNo>

<otherAcctName>开放银行测试 7</otherAcctName>

<otherPartyName>中国民生银行股份有限公司长春民丰大街支行

</otherPartyName>

<postscript></postscript>

<summary></summary>

</Map>

<Map>

<transDate>20210526</transDate>

<creditAmount>30.00</creditAmount>

<otherAcctNo></otherAcctNo>

<otherAcctName></otherAcctName>

<otherPartyName></otherPartyName>

<postscript>工资一月</postscript>

<summary>工资一月</summary>

</Map>

<Map>

<transDate>20210526</transDate>

<creditAmount>8000.00</creditAmount>

<otherAcctNo>*********111111</otherAcctNo>

<otherAcctName>开放银行测试 7xxx</otherAcctName>

<otherPartyName>中国工商银行股份有限公司北京东铁匠营支行

</otherPartyName>

<postscript>01：工资 01：一月</postscript>

<summary>01：工资 01：一月</summary>

</Map>

<Map>

43 / 95

民生银行银企直联

<transDate>20210524</transDate>

<creditAmount>30.00</creditAmount>

<otherAcctNo>*********</otherAcctNo>

<otherAcctName>开放银行测试 7</otherAcctName>

<otherPartyName>中国民生银行长春民丰大街支行</otherPartyName>

<postscript>xin'zi 薪资 fu'li</postscript>

<summary>xin'zi 薪资 fu'li</summary>

</Map>

<Map>

<transDate>20210524</transDate>

<creditAmount>4180.00</creditAmount>

<otherAcctNo>*********</otherAcctNo>

<otherAcctName>开放银行测试 7</otherAcctName>

<otherPartyName>中国民生银行长春民丰大街支行</otherPartyName>

<postscript>往来结算款 xxxx</postscript>

<summary>往来结算款 xxxx</summary>

</Map>

<Map>

<transDate>20210524</transDate>

<debitAmount>1000000.00</debitAmount>

<otherAcctNo>****************</otherAcctNo>

<otherAcctName>开放银行测试 7</otherAcctName>

<otherPartyName>中国民生银行股份有限公司长春民丰大街支行

</otherPartyName>

<postscript></postscript>

<summary></summary>

</Map>

</list>

<creditTotalAmount>2022240.00</creditTotalAmount>

</xDataBody>

</CMBC>

44 / 95

民生银行银企直联
12.子账簿批量出金结果查询(B2eQrySalaryMgnBat

chList)

本部分更新日期:2022-02-15

查询子账簿批量出金 B2eSalaryMgnBatchTransfer 的交易列表信息

12.1.请求(B2eQrySalaryMgnBatchList)

标记

说明

长度

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <draweeVacctNo> 付款子账户（★）

   <dateFrom>

开始日期(yyyyMMdd)（★）

   <dateTo>

结束日期(yyyyMMdd)（★）

   <pageNo>

当前页 首页传 1

   <pageSize>

查询行数 最多支持 50

</xDataBody>

12.2.响应(B2eQrySalaryMgnBatchList)

标记

说明

<xDataBody>

   <total>

   <list>

总笔数

结果集

32

32

8

8

5

5

长度

5

45 / 95

标记

说明

    <Map>

    <applyId>

请求流水号

    <transStatus>

交易状态

    <createTime>

转账申请时间

    <totNum>

汇总笔数

    <totAmt>

汇总金额（元）

    <sucNum>

成功笔数

    <sucAmt>

成功金额（元）

    <failNum>

失败笔数

    <failAmt>

失败金额（元）

    <sceneType>

场景类型

102-批量出金

106-批量间转账

民生银行银企直联

长度

32

2

19

4

16,2

4

16,2

4

16,2

3

    <sceneTypeDesc> 场景类型描述

300

    </Map>

   </lis>

</xDataBody>

12.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchList">

46 / 95

民生银行银企直联

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<draweeVacctNo>****************</draweeVacctNo>

<dateFrom>********</dateFrom>

<dateTo>********</dateTo>

<pageNo>1</pageNo>

<pageSize>20</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchList">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 15:02:42</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>6</total>

<list>

<Map>

<applyId>91101********1642370023911010026</applyId>

47 / 95

<transStatus>02</transStatus>

<createTime>2021-05-27 16:42:38</createTime>

民生银行银企直联

<totNum>1</totNum>

<totAmt>4000.00</totAmt>

<sucNum>1</sucNum>

<sucAmt>4000.00</sucAmt>

<failNum>0</failNum>

<failAmt>0.00</failAmt>

<sceneType>102</sceneType>

<sceneTypeDesc>批量代发</sceneTypeDesc>

</Map>

<Map>

<applyId>91101********1534579426911009428</applyId>

<transStatus>02</transStatus>

<createTime>2021-05-27 15:34:57</createTime>

<totNum>1</totNum>

<totAmt>3000.00</totAmt>

<sucNum>1</sucNum>

<sucAmt>3000.00</sucAmt>

<failNum>0</failNum>

<failAmt>0.00</failAmt>

<sceneType>102</sceneType>

<sceneTypeDesc>批量代发</sceneTypeDesc>

</Map>

<Map>

<applyId>91101********1013556288911006286</applyId>

<transStatus>02</transStatus>

<createTime>2021-05-27 10:13:56</createTime>

<totNum>6</totNum>

<totAmt>51501.00</totAmt>

<sucNum>4</sucNum>

<sucAmt>7000.00</sucAmt>

<failNum>2</failNum>

<failAmt>44501.00</failAmt>

<sceneType>102</sceneType>

<sceneTypeDesc>批量代发</sceneTypeDesc>

</Map>

<Map>

48 / 95

<applyId>91101********0943326020911006019</applyId>

<transStatus>02</transStatus>

<createTime>2021-05-27 09:43:33</createTime>

民生银行银企直联

<totNum>1</totNum>

<totAmt>200.00</totAmt>

<sucNum>1</sucNum>

<sucAmt>200.00</sucAmt>

<failNum>0</failNum>

<failAmt>0.00</failAmt>

<sceneType>102</sceneType>

<sceneTypeDesc>批量代发</sceneTypeDesc>

</Map>

<Map>

<applyId>91101202105261804163290911013312</applyId>

<transStatus>02</transStatus>

<createTime>2021-05-26 18:04:16</createTime>

<totNum>2</totNum>

<totAmt>300.00</totAmt>

<sucNum>2</sucNum>

<sucAmt>300.00</sucAmt>

<failNum>0</failNum>

<failAmt>0.00</failAmt>

<sceneType>102</sceneType>

<sceneTypeDesc>批量代发</sceneTypeDesc>

</Map>

<Map>

<applyId>91101202105261733172859911012878</applyId>

<transStatus>02</transStatus>

<createTime>2021-05-26 17:33:17</createTime>

<totNum>1</totNum>

<totAmt>1100.00</totAmt>

<sucNum>0</sucNum>

<sucAmt>0.00</sucAmt>

<failNum>1</failNum>

<failAmt>1100.00</failAmt>

<sceneType>102</sceneType>

<sceneTypeDesc>批量代发</sceneTypeDesc>

</Map>

49 / 95

</list>

</xDataBody>

</CMBC>

民生银行银企直联

13.子账簿批量转账明细(B2eQrySalaryMgnBatchT

ransferDetail)

本部分更新日期:2022-02-15

查询总公司及子公司批量转账后的交易明细。

调用该交易前需要先查询 B2eQrySalaryMgnBatchList 服务，获取交易参数信息

13.1.请求（B2eQrySalaryMgnBatchTransferDetail）

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <applyId>

原请求流水号（★）

   <transStatus>

交易状态（★）

11：待审批；

12：审批完成；

13：审批拒绝；

14：审批任务失效

01：交易中；

02：交易成功

03：交易失败；

04：交易未知；

00:处理中

长度

32

32

2

50 / 95

民生银行银企直联

标记

说明

   <pageNo>

当前页

   <pageSize>

查询条数 最大支持 50

</xDataBody>

13.2.响应（B2eQrySalaryMgnBatchTransferDetail）

标记

<xDataBody>

   <total>

   <list>

    <Map>

说明

总条数

    <draweeAcctNo>

付款方账号

    <draweeAcctName>

付款方名称

    <payeeAcctNo>

收款方账号

    <payeeAcctName>

收款方户名

    <payeeBankNo>

收款行行号

    <payeeBankName>

收款行行名

    <transAmt>

交易金额

    <retType>

返回类型

1：成功；

2：失败；

    <retTypeDesc>

返回类型描述

长度

5

5

长度

64

32

240

32

240

12

300

16,2

1

12

51 / 95

标记

说明

    <draweeSummary>

收款方备注

    <retMsg>

    <serNo>

返回信息

明细序号

    <othSerialNo>

其他交易流水号

    <retCode>

返回类型码值

    <payeeSummary>

付款方备注

    <draweeVoucherNo>

付款方凭证编码

    <payeePostscript>

付款方摘要

    <cashOrRemitFlag>

钞汇标志

1-汇

2-钞，默认为 2-钞

    <currency>

币种 默认 RMB

    <draweePostscript>

收款方摘要

    <cashOrRemitFlagDesc> 钞汇标志描述

    <refundTime>

退汇时间 YYYY-MM-DD

    <paymentPath>

HH:mm:SS

汇路

0：本转；

1：他转；

    <refundReason>

退汇原因

    <refundFlagDesc>

退汇标识描述

民生银行银企直联

长度

300

300

10

40

20

300

20

300

1

120

300

3

8

1

300

30

52 / 95

标记

说明

    <draweeVoucherType>

付款方凭证类型

    <payeeAcctType>

收款方账户类型

    <payeeAcctTypeDesc>

收款方账户类型描述

    <paymentPathDesc>

汇路描述

    <refundFlag>

退汇标识

0-未退汇

1-已退汇

2-退汇入账

民生银行银企直联

长度

16

1

120

120

2

    </Map>

   </lis>

</xDataBody>

13.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchTransferDetail">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

53 / 95

<applyId>91101********1642370023911010026</applyId>

民生银行银企直联

<transStatus>02</transStatus>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchTransferDetail">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 15:06:05</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>1</total>

<list>

<Map>

<draweeAcctName>开放银行测试 7</draweeAcctName>

<serNo>1</serNo>

<payeeAcctName>保险贷款 x'x'x</payeeAcctName>

<draweeSummary>奖金一月</draweeSummary>

<othSerialNo></othSerialNo>

<retCode></retCode>

<retType>1</retType>

<payeeSummary>奖金一月</payeeSummary>

<draweeVoucherNo></draweeVoucherNo>

<payeePostscript>奖金一月</payeePostscript>

<payeeBankNo>************</payeeBankNo>

54 / 95

民生银行银企直联

<cashOrRemitFlag>2</cashOrRemitFlag>

<transAmt>4000.00</transAmt>

<currency>RMB</currency>

<draweePostscript>奖金一月</draweePostscript>

<draweeAcctNo>****************</draweeAcctNo>

<cashOrRemitFlagDesc>钞</cashOrRemitFlagDesc>

<refundTime></refundTime>

<paymentPath>1</paymentPath>

<refundReason></refundReason>

<refundFlagDesc>未退汇</refundFlagDesc>

<retMsg>交易成功</retMsg>

<draweeVoucherType></draweeVoucherType>

<payeeAcctNo>6228480018536951272</payeeAcctNo>

<payeeAcctType>1</payeeAcctType>

<payeeAcctTypeDesc>对私</payeeAcctTypeDesc>

<payeeBankName>中国工商银行股份有限公司北营支行 1</payeeBankName>

<paymentPathDesc>他行</paymentPathDesc>

<refundFlag>0</refundFlag>

<retTypeDesc>成功</retTypeDesc>

</Map>

</list>

</xDataBody>

</CMBC>

14.待清分账簿入账明细(B2eQryClarifyVacctNoTra

nsferDetail)

本部分更新日期:2022-02-15

查询总公司及子公司待清分子账簿的明细，该交易用于子账簿间调账的服务

55 / 95

14.1.请求(B2eQryClarifyVacctNoTransferDetail)

民生银行银企直联

标记

说明

长度

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <vacctNo>

待清分账户（★）

   <dateFrom>

查询起始日期（★）格式为 YYYYMMDD

   <dateTo>

查询截止日期（★）格式为 YYYYMMDD

   <pageNo>

当前页 首页传 1

   <pageSize>

查询行数 最大支持 50

</xDataBody>

14.2.响应(B2eQryClarifyVacctNoTransferDetail)

标记

说明

<xDataBody>

   <total>

   <list>

    <Map>

总笔数

结果集

    <transSeqNo>

交易流水号

    <transDate>

交易日期

    <transAmt>

入账金额

    <otherAcctNo>

付款方账号

32

32

8

8

5

5

长度

5

32

8

16,2

32

56 / 95

标记

说明

    <otherAcctName>

收款行行号

    <payeeBankName> 付款方账号名称

    <postscript>

摘要

民生银行银企直联

长度

12

240

300

    </Map>

   </lis>

</xDataBody>

14.3.例子

说明：当前查询时间范围区间为 3 个月内

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryClarifyVacctNoTransferDetail">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<vacctNo>****************</vacctNo>

<dateFrom>********</dateFrom>

<dateTo>********</dateTo>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

57 / 95

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryClarifyVacctNoTransferDetail">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-10-26 15:08:28</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>14</total>

<list>

<Map>

<transSeqNo>210603092019000000000015443F1025</transSeqNo>

<transDate>20210603</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>210603092012000000000005443F101F</transSeqNo>

<transDate>20210603</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

58 / 95

<transSeqNo>210603091958000000000005443F0F8B</transSeqNo>

民生银行银企直联

<transDate>20210603</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>210602170720000000000015443E923C</transSeqNo>

<transDate>20210602</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>210602110820000000000015443E70A4</transSeqNo>

<transDate>20210602</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>210602093304000000000015444061C2</transSeqNo>

<transDate>20210602</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>2106020918280000000000054440607B</transSeqNo>

<transDate>20210602</transDate>

<transAmt>5.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

59 / 95

民生银行银企直联

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>2106011800130000000000054440192E</transSeqNo>

<transDate>********</transDate>

<transAmt>500.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>210601180007000000000005443E15C7</transSeqNo>

<transDate>********</transDate>

<transAmt>500.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

<Map>

<transSeqNo>2106011800020000000000154440191E</transSeqNo>

<transDate>********</transDate>

<transAmt>500.00</transAmt>

<otherAcctNo>51000000000008107870</otherAcctNo>

<otherAcctName></otherAcctName>

<postscript>资金转入</postscript>

</Map>

</list>

</xDataBody>

</CMBC>

15.子账簿调账(B2eVirtualAccountReconciliatio

n)

本部分更新日期:2022-02-15

总公司及子公司进行子账簿间的资金调账。

60 / 95

民生银行银企直联
该交易需要先调用服务 QryClarifyVacctNoTransferDetail 获取调账接口的各种参数信
息。

15.1.请求（B2eVirtualAccountReconciliation）

标记

说明

<xDataBody>

   <customerSignId>

客户签约编码（★）

   <OriChannelSeqno> 原交易流水（★）

   <payerAcct>

付款实体账号（★）

   <payerName>

付款实体账号名称（★）

   <DraweeVacctNo>

待清分子账户（★）

   <payeeAcct>

收款子账号（★）

   <payeeName>

收款子帐号名称（★）

   <DealType>

操作类型（★）

1-转入项目子账簿

2-转入利息子账户

3-转入自有资金子账户

   <amount>

转出金额（★）

   <currency>

币种（★）RMB

   <Usage>

   <Remark>

</xDataBody>

用途

备注

长度

32

32

32

300

32

32

300

1

15

3

128

300

说明：OriChannelSeqno ：该字段从 QryClarifyVacctNoTransferDetail 的交易中获

取的对应的流水 transSeqNo payeeAcct：此账号为子账簿账号，只能输入同一实账户

61 / 95

民生银行银企直联
账号项下的子账簿 DraweeVacctNo：该字段与 payeeAcct 字段账号为同一实账户下子

账簿账号 Amount：该字段从 QryClarifyVacctNoTransferDetail 中查询到对应的金额

要保持一致

15.2.响应（B2eVirtualAccountReconciliation）

标记

说明

<xDataBody>

   < _JnlNo>

流水号

   <CertNo>

凭证号

   <UserName> 制单员

</xDataBody>

15.3.例子

请求报文

长度

32

32

50

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountReconciliation">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<CustomerSignId>01202105130947000001</CustomerSignId>

<OriChannelSeqno>210601155153869808069855443F7EB4</OriChannelSe

qno>

62 / 95

民生银行银企直联

<payerAcct>*********</payerAcct>

<payerName>开放银行测试 7</payerName>

<DraweeVacctNo>****************</DraweeVacctNo>

<payeeAcct>****************</payeeAcct>

<payeeName>李明</payeeName>

<OriDraweeAcctNo>51000000000008107870</OriDraweeAcctNo>

<OriDraweeAcctName></OriDraweeAcctName>

<DealType>1</DealType>

<amount>5000.00</amount>

<currency>RMB</currency>

<Usage>资金转入</Usage>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eVirtualAccountReconciliation">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-07-02 16:49:48</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<returnCode>

<code>AAAAAAA</code>

<type>S</type>

</returnCode>

<_JnlNo>20210702160302006700</_JnlNo>

<UserName>开放银行测 896</UserName>

<CertNo>*************</CertNo>

63 / 95

</xDataBody>

</CMBC>

民生银行银企直联

16.实账户模式单笔出金(B2eSalaryMgnRealTransf

er)

本部分更新日期:2022-06-29

支持客户通过自有平台实现通过实账户进行对外单笔转账。

16.1.请求(B2eSalaryMgnRealTransfer)

标记

说明

<xDataBody>

   <payerAcct>

付款实体账户（★）

   <payerName>

付款实体账号名称（★）

   <CustomerSignId> 客户签约编码（★）

   <payeeAcct>

收款账号（★）

   <payeeName>

收款账号名称（★）

   <payeeAcct>

收款子账号（★）

   <payeeName>

收款子帐号名称（★）

   <PayeeBankName> 收款账户开户机构名称

   <PayeeBankNo>

收款账户开户行号

   <amount>

转出金额（★）

   <currency>

币种（★）CNY

长度

32

240

32

32

240

32

240

240

32

15

3

64 / 95

标记

说明

   <Usage>

用途（★）

民生银行银企直联

长度

128

   <Remark>

备注当 Usage 为其他时，Remark 必输用途

25

和备注总和不得超过 24 个汉字

   <SysFlag>

行内外标志（★）

0：本行，

1：他行

   <EntPerFlag>

收款账户类型

0：对公，

1：对私

   <deptName>

付款账户开户行

   <CertNo>

企业自制凭证号

<trnId>

客户端产生的交易唯一标识

<insId>

业务流水号（★）

</xDataBody>

1

1

240

8

32

32

Usage 字典： 当为对私转账时：工资、绩效工资、奖金、补贴、报销、劳务费、佣金、

服务费 当为对公转账时：网下申购, 贷款, 往来结算款, 工资奖金, 差旅费, 租赁费, 办公费,

合同款,水电费, 运费, 工程款,劳务费, 通讯费, 交通费, 报刊费, 餐费, 增资款, 投资款, 其他

16.2.响应(B2eSalaryMgnRealTransfer)

标记

说明

<xDataBody>

   < _JnlNo>

   <CertNo>

流水号

凭证号

长度

32

32

65 / 95

民生银行银企直联

长度

50

32

240

32

32

标记

说明

   <UserName>

制单员

   <PayeeBankNo>

收款人开户行

   <PayeeDeptName> 收款人开户行名称

客户端产生的唯一标识

业务流水号

<insId>

<trnId>

</xDataBody>

16.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnRealTransfer">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>cmbc1564613165</insId>

<trnId>cmbc1564613165</trnId>
<payerAcct>*********</payerAcct>
<payerName>薪福通实账户测试 001</payerName>
<CustomerSignId>01202109271815380000</CustomerSignId>
<CertNo></CertNo>
<payeeAcct>*********</payeeAcct>
<payeeName>陈丽华测试分行一 17</payeeName>
<payeeBankName></payeeBankName>
<payeeBankNo></payeeBankNo>

66 / 95

民生银行银企直联

<amount>123.00</amount>
<currency>CNY</currency>
<Usage>贷款</Usage>
<Remark>666</Remark>
<SysFlag>0</SysFlag>
<vacctBusiName></vacctBusiName>
<deptName>中国民生银行股份有限公司北京中关村支行</deptName>
<EntPerFlag>0</EntPerFlag>
</xDataBody>
</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnRealTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-06-30 11:51:37</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<UserName>黄武林</UserName>

<PayeeDeptName>中国民生银行股份有限公司长春分行</PayeeDeptName>

<_JnlNo>20220630113368291700</_JnlNo>

<CertNo>*************</CertNo>

<PayeeBankNo>************</PayeeBankNo>

<insId>cmbc1564613165</insId>

<trnId>cmbc1564613165</trnId>

</xDataBody>

</CMBC>

67 / 95

17.实账户模式批量出金(B2eSalaryMgnRealBatch

民生银行银企直联

Transfer)

本部分更新日期:2024-01-08

客户通过自有平台实现通过实账户实现批量代发的功能。

17.1.请求(B2eSalaryMgnRealBatchTransfer)

标记

说明

<xDataBody>

   <issuingInstructions> 代发说明

   <payerAcct>

付款实体账户（★）

   <payerName>

付款实体账号名称（★）

   <CustomerSignId>

客户签约编码（★）

   <CertNo>

企业自制凭证号（非必输）

   <amount>

转出金额（★）

   <currency>

币种（★）RMB

   <Usage>

用途（★）

长度

90

32

240

32

8

15

3

20

   <Remark>

备注和用途总和不得超过 24 个汉字 70

   <TotalCount>

总笔数（★）

   <FileNameContent>

文件内容（★）最大 500 笔数据

   <trnId>

客户端产生的交易唯一标识

   <insId>

业务流水号（★）

4

32

32

68 / 95

标记

说明

</xDataBody>

民生银行银企直联

长度

Usage：字典：工资、绩效工资、奖金、补贴、报销、劳务费、佣金、服务费

Remark：字典：一月, 二月, 三月, 四月, 五月, 六月, 七月, 八月, 九月, 十月, 十一月,

十二月, 一季度, 二季度, 三季度, 四季度, 半年, 年终, + 自定义值（客户自己输入）

FileNameContent: 明细序号|收款人姓名|收款人账号|交易金额|收款人开户行行号|收款人

开户行名称|收款人证件号码|收款人手机号码|备注|

每行数据以^拼接；

1.其中 明细序号、收款人姓名、收款人账号、交易金额 为必输项

2.收款人开户行行号、收款人开户行名称、收款人证件号码、收款人手机号码、备注为非

必输项

3.明细序号 从 1 开始累加

4.交易金额 薪资发放金额，保留 2 位小数

17.2.响应(B2eSalaryMgnRealBatchTransfer)

标记

说明

长度

<xDataBody>

   < _JnlNo>

流水号

   <CertNo>

凭证号

   <UserName> 制单员

   <applyId>

批次请求流水号

   <trnId>

客户端产生的交易唯一标识

   <insId>

业务流水号

</xDataBody>

32

13

50

32

32

32

69 / 95

17.3.例子

请求报文

民生银行银企直联

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryMgnRealBatchTransfer">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<issuingInstructions>02666</issuingInstructions>

<payerAcct>*********</payerAcct>

<payerName>薪福通实账户测试 001</payerName>

<CustomerSignId>01202109271815380000</CustomerSignId>

<CertNo></CertNo>

<amount>3.00</amount>

<currency>RMB</currency>

<Usage>工资</Usage>

<Remark></Remark>

<TotalCount>3</TotalCount>

<FileNameContent>1|胡思梦|****************|1|||||^2| 刘平金

|6228480018536951272|1|************|中国农业银行

|110104195010102440|***********|^3|陶燕

|************|1||||***********|</FileNameContent>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

70 / 95

trnCode="B2eSalaryMgnRealBatchTransfer">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-06-30 12:32:46</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<_JnlNo>20220630123368461200</_JnlNo>

<applyId>91120221114132109491321911491322</applyId>

<UserName>黄武林</UserName>

<CertNo>9800000000061</CertNo>

</xDataBody>

</CMBC>

18.实账户批量出金结果查询(B2eQrySalaryMgnRea

lBatchList)

本部分更新日期:2022-06-29

实账户批量出金的交易结果列表查询。

18.1.请求(B2eQrySalaryMgnRealBatchList)

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <acctNo>

付款实账户（★）

长度

32

32

71 / 95

标记

说明

   <dateFrom>

开始日期（★）(yyyyMMdd)

   <dateTo>

结束日期（★）(yyyyMMdd)

   <pageNo>

当前页,首页传 1

   <pageSize>

查询条数，最大支持 50

民生银行银企直联

长度

8

8

5

5

   <transStatus>

交易状态

</xDataBody>

transStatus 码值说明：

当 transStatus 不送时，默认为全部

01：交易中；

02：交易成功

03：交易失败；

04：交易未知

18.2.响应(B2eQrySalaryMgnRealBatchList)

标记

说明

长度

<xDataBody>

服务消息集

   <total>

   <list>

     <Map>

总笔数

结果集

      <applyId>

请求流水号

      <transStatus> 交易状态

5

32

2

72 / 95

民生银行银企直联

长度

19

4

16，2

4

16，2

4

16，2

3

300

300

19

300

标记

说明

      <createTime> 转账申请时间

      <totNum>

汇总笔数

      <totAmt>

汇总金额（元）

      <sucNum>

成功笔数

      <sucAmt>

成功金额（元）

      <failNum>

失败笔数

      <failAmt>

失败金额（元）

      <sceneType>

场景类型

102-批量出金，

106-批量间转账

      <postscript>

客户附言

      <summary>

银行附言

      <updateTime> 交易更新时间

      <retMsg>

返回信息

     </Map>

   </list>

</xDataBody>

18.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

73 / 95

民生银行银企直联

trnCode="B2eQrySalaryMgnRealBatchList">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202109271815380000</customerSignId>

<acctNo>*********</acctNo>

<dateFrom>20220530</dateFrom>

<dateTo>20220630</dateTo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnRealBatchList">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-06-30 10:53:14</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>1</total>

<applyIdStrRealList>

<item>91120220630083014950747911950748</item>

</applyIdStrRealList>

74 / 95

民生银行银企直联

<list>

<item>

<applyId>91120220630083014950747911950748</applyId>

<transStatus>02</transStatus>

<transStatusDesc></transStatusDesc>

<createTime>2022-06-30 08:30:14:00</createTime>

<totNum>3</totNum>

<totAmt>3.00</totAmt>

<sucNum>3</sucNum>

<sucAmt>3.00</sucAmt>

<failNum>0</failNum>

<failAmt>0.00</failAmt>

<postscript>工资</postscript>

<summary>薪福通转账</summary>

<updateTime>08:34:01</updateTime>

<sceneType>102</sceneType>

<retMsg>交易成功完成</retMsg>

</item>

</list>

</xDataBody>

</CMBC>

19.实账户批量转账明细(B2eQrySalaryMgnBatchR

ealDetail)

本部分更新日期:2022-06-29

查询实账户模式批量转账后的交易明细。 调用该交易前需要先查询

B2eQrySalaryMgnRealBatchList 服务，获取交易参数信息。

19.1.请求(B2eQrySalaryMgnBatchRealDetail)

标记

说明

长度

75 / 95

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <applyId>

原请求流水号（★）

   <transStatus>

交易状态（★）

11：待审批；

12：审批完成；

13：审批拒绝；

14：审批任务失效

01：交易中；

02：交易成功

03：交易失败；

04：交易未知；

00:处理中

   <pageNo>

当前页

   <pageSize>

查询条数（最大支持条数 50）

</xDataBody>

19.2.响应(B2eQrySalaryMgnBatchRealDetail)

标记

<xDataBody>

   <total>

   <list>

说明

服务消息集

总笔数

结果集

民生银行银企直联

长度

32

32

2

5

5

长度

64

76 / 95

标记

说明

     <Map>

      <draweeAcctNo>

付款方账号

      <draweeAcctName>

付款方名称

      <payeeAcctNo>

收款方账号

      <payeeAcctName>

收款方户名

      <payeeBankNo>

收款行行号

      <payeeBankName>

收款行行名

      <transAmt>

交易金额

      <retType>

返回类型：

1：成功；

2：失败；

      <retTypeDesc>

返回类型描述

      <draweeSummary>

收款方备注

      <draweeBankNo>

付款行行号

      <draweeBankName>

付款行行名

      <retMsg>

      <serNo>

返回信息

明细序号

      <othSerialNo>

其他交易流水号

      <retCode>

返回类型码值

      <payeeSummary>

付款方备注

民生银行银企直联

长度

32

240

32

240

12

300

16，

2

1

12

300

12

300

300

10

40

20

300

77 / 95

标记

说明

      <draweeVoucherNo>

付款方凭证编码

      <payeePostscript>

付款方摘要

      <cashOrRemitFlag>

钞汇标志

1-汇

2-钞，默认为 2-钞

      <currency>

币种默认 RMB

      <draweePostscript>

收款方摘要

      <cashOrRemitFlagDesc> 钞汇标志描述

      <refundTime>

退汇时间 YYYY-MM-DD

      <paymentPath>

HH:mm:SS

汇路

0：本转；

1：他转；

      <refundReason>

退汇原因

      <refundFlagDesc>

退汇标识描述

      <draweeVoucherType>

付款方凭证类型

      <payeeAcctType>

收款方账户类型

      <payeeAcctTypeDesc>

收款方账户类型描述

      <paymentPathDesc>

汇路描述

      <refundFlag>

退汇标识：

0-未退汇，

1-已退汇，

民生银行银企直联

长度

20

300

1

120

300

3

8

1

16

1

120

120

2

78 / 95

标记

说明

长度

民生银行银企直联

2-退汇入账

     </Map>

   </list>

</xDataBody>

19.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchRealDetail">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202109271815380000</customerSignId>

<applyId>91120220630083014950747911950748</applyId>

<transStatus>02</transStatus>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchRealDetail">

<responseHeader>

<status>

<code>0</code>

79 / 95

民生银行银企直联

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-06-30 10:48:46</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>3</total>

<list>

<item>

<serNo>1</serNo>

<othSerialNo></othSerialNo>

<draweeVoucherType></draweeVoucherType>

<draweeVoucherNo></draweeVoucherNo>

<draweeAcctNo>*********</draweeAcctNo>

<draweeAcctName> 薪福通实账户测试 001</draweeAcctName>

<draweeBankNo>0105</draweeBankNo>

<draweeBankName>中国民生银行股份有限公司北京中关村支行

</draweeBankName>

<payeePostscript>工资</payeePostscript>

<payeeSummary></payeeSummary>

<payeeAcctNo>****************</payeeAcctNo>

<payeeAcctName>胡思梦</payeeAcctName>

<payeeBankNo>************</payeeBankNo>

<payeeBankName>中国民生银行股份有限公司北京中关村支行

</payeeBankName>

<payeeAcctType>1</payeeAcctType>

<payeeAcctTypeDesc>对私</payeeAcctTypeDesc>

<paymentPath>0</paymentPath>

<paymentPathDesc>本转</paymentPathDesc>

<cashOrRemitFlag>2</cashOrRemitFlag>

<cashOrRemitFlagDesc>钞</cashOrRemitFlagDesc>

<currency>RMB</currency>

<transAmt>1.00</transAmt>

80 / 95

<draweePostscript>工资</draweePostscript>

<draweeSummary>薪福通转账</draweeSummary>

民生银行银企直联

<retType>1</retType>

<retTypeDesc>成功</retTypeDesc>

<retCode>APP00000</retCode>

<retMsg>交易处理成功</retMsg>

<refundFlag>0</refundFlag>

<refundFlagDesc>未退汇</refundFlagDesc>

<refundReason></refundReason>

<refundTime></refundTime>

</item>

<item>

<serNo>2</serNo>

<othSerialNo></othSerialNo>

<draweeVoucherType></draweeVoucherType>

<draweeVoucherNo></draweeVoucherNo>

<draweeAcctNo>*********</draweeAcctNo>

<draweeAcctName> 薪福通实账户测试 001</draweeAcctName>

<draweeBankNo>0105</draweeBankNo>

<draweeBankName>中国民生银行股份有限公司北京中关村支行

</draweeBankName>

<payeePostscript>工资</payeePostscript>

<payeeSummary></payeeSummary>

<payeeAcctNo>6228480018536951272</payeeAcctNo>

<payeeAcctName>刘平金</payeeAcctName>

<payeeBankNo>************</payeeBankNo>

<payeeBankName> 中国农业银行股份有限公司</payeeBankName>

<payeeAcctType>1</payeeAcctType>

<payeeAcctTypeDesc>对私</payeeAcctTypeDesc>

<paymentPath>1</paymentPath>

<paymentPathDesc>他行</paymentPathDesc>

<cashOrRemitFlag>2</cashOrRemitFlag>

<cashOrRemitFlagDesc>钞</cashOrRemitFlagDesc>

<currency>RMB</currency>

<transAmt>1.00</transAmt>

81 / 95

<draweePostscript>工资</draweePostscript>

<draweeSummary>薪福通转账</draweeSummary>

民生银行银企直联

<retType>1</retType>

<retTypeDesc>成功</retTypeDesc>

<retCode>APP00000</retCode>

<retMsg>交易处理成功</retMsg>

<refundFlag>0</refundFlag>

<refundFlagDesc>未退汇</refundFlagDesc>

<refundReason></refundReason>

<refundTime></refundTime>

</item>

<item>

<serNo>3</serNo>

<othSerialNo></othSerialNo>

<draweeVoucherType></draweeVoucherType>

<draweeVoucherNo></draweeVoucherNo>

<draweeAcctNo>*********</draweeAcctNo>

<draweeAcctName> 薪福通实账户测试 001</draweeAcctName>

<draweeBankNo>0105</draweeBankNo>

<draweeBankName>中国民生银行股份有限公司北京中关村支行

</draweeBankName>

<payeePostscript>工资</payeePostscript>

<payeeSummary></payeeSummary>

<payeeAcctNo>************</payeeAcctNo>

<payeeAcctName>陶燕</payeeAcctName>

<payeeBankNo>3308</payeeBankNo>

<payeeBankName>中国民生银行股份有限公司长春临河街支行

</payeeBankName>

<payeeAcctType>1</payeeAcctType>

<payeeAcctTypeDesc>对私</payeeAcctTypeDesc>

<paymentPath>0</paymentPath>

<paymentPathDesc>本转</paymentPathDesc>

<cashOrRemitFlag>2</cashOrRemitFlag>

<cashOrRemitFlagDesc>钞</cashOrRemitFlagDesc>

<currency>RMB</currency>

82 / 95

民生银行银企直联

<transAmt>1.00</transAmt>

<draweePostscript>工资</draweePostscript>

<draweeSummary>薪福通转账</draweeSummary>

<retType>1</retType>

<retTypeDesc>成功</retTypeDesc>

<retCode>APP00000</retCode>

<retMsg>交易处理成功</retMsg>

<refundFlag>0</refundFlag>

<refundFlagDesc>未退汇</refundFlagDesc>

<refundReason></refundReason>

<refundTime></refundTime>

</item>

</list>

</xDataBody>

</CMBC>

20.实账户批量退汇查询 (B2eQrySalaryMgnBatch

RealReexList)

本部分更新日期:2022-06-29

实账户批量退汇的交易结果列表查询。

20.1.请求(B2eQrySalaryMgnBatchRealReexList)

标记

说明

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <acctNo>

付款实账户（★）

   <dateFrom>

开始日期（★）(yyyyMMdd)

长度

32

32

8

83 / 95

标记

说明

民生银行银企直联

长度

   <dateTo>

结束日期（★）(yyyyMMdd)

   <pageNo>

当前页,首页传 1

   <pageSize>

查询条数，最大支持 50

8

5

5

</xDataBody>

20.2.响应(B2eQrySalaryMgnBatchRealReexList)

标记

说明

长度

<xDataBody>

服务消息集

   <total>

   <list>

     <Map>

总笔数

结果集

      <oriApplyId>

原交易流水号

      <acctNo>

账户号码

      <acctName>

账户名称

      <oriSceneType>

原交易类型

      <oriSerNo>

汇原明细序号

      <oriOthSerialNo>

原其他流水号

      <oriPayeeAcctNo>

原收款账号

      <oriPayeeAcctName> 原收款账号名称

      <oriPayeeAcctType>

原收款账号类型

5

32

32

32

3

5

32

32

240

1

84 / 95

标记

说明

      <oriPayeeBankNo>

原收款账号开户行行号

      <oriPayeeBankName> 原收款账号开户行名称

      <oriTransAmt>

原交易金额

      <oriTransDate>

原交易日期

      <oriPostscript>

原交易摘要

      <oriSummary>

原交易备注

      <refundReason>

退汇原因

      <refundTime>

退汇时间

民生银行银企直联

长度

12

300

16，2

19

300

300

300

8

     </Map>

   </list>

</xDataBody>

20.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchRealReexList">

<requestHeader>

<dtClient>2021-06-23 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

85 / 95

<xDataBody>

<customerSignId>01202109271815380000</customerSignId>

民生银行银企直联

<acctNo>*********</acctNo>

<dateFrom>20220530</dateFrom>

<dateTo>20220630</dateTo>

</xDataBody>

</CMBC>

响应报文

响应报文：

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQrySalaryMgnBatchRealReexList">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-06-30 12:39:48</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>0</total>

<list/> </xDataBody>

</CMBC>

21.批量代发批次结果详情 pdf 文件下载(B2eQryCom

monSalaryMgnBatchByFile)

本部分更新日期:2022-06-29

在调用该交易前需要先查询 B2eQrySalaryMgnRealBatchList 服务获取到 applyId 与

transStatus 的值，通过该交易实现批量出金的交易结果详情 pdf 文件下载。

86 / 95

21.1.请求(B2eQryCommonSalaryMgnBatchByFile)

民生银行银企直联

标记

说明

长度

<xDataBody>

   <customerSignId> 客户签约编码（★）

   <transStatus>

交易状态（★）

   <transType>

批量交易类型（★）

   <applyId>

原请求流水号（★）

</xDataBody>

21.2.响应(B2eQryCommonSalaryMgnBatchByFile)

标记

说明

<xDataBody>

服务消息集

  <fileName>

文件名称

  <fileContent> pdf 文件内容

</xDataBody>

说明：

transStatus 码值：

11：待审批；

12：审批完成；

13：审批拒绝；

14：审批任务失效

01：交易中；

02：交易成功

32

2

1

32

长度

60

500

87 / 95

03：交易失败；

04：交易未知；

民生银行银企直联

当码值送 02 时才会查询数据并下载，其他状态时无法返回数据，如确认交易已经成功请

上送 02 码值。

transType 码值：

1-子账簿模式，

2-实账户模式。

21.3.例子

请求报文

<?xml version = "1.0" encoding = "GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryCommonSalaryMgnBatchByFile">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********605</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<customerSignId>01202105130947000001</customerSignId>

<applyId>91101********1642370023911010026</applyId>

<transStatus>02</transStatus>

<transType>2</transType>

</xDataBody>

</CMBC>

响应报文

88 / 95

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs" trnCode="

B2eQryCommonSalaryMgnBatchByFile">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-06-30 14:31:35</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<fileName>91101********1642370023911010026_RES.pdf</fileName>

<fileContent>JVBERi0xLjQKJeLjz9MKMyAwIG9iago8PC9Db2xvclNwYWNlL0Rl

dmlm5nQIbjfvLzy3EG+BWM/mU8nNrWAFya3W/MyLEF3rC8GAreRyWhUwCruK

QXYgHwNgS8rU88tsfbmJfXe4sIMQTGlcDFbWVEJWZAdJX/JJLhiGcOZ/7mAsZE77

HJgW80R34WN2C8PWh5XXIIpvbNaeqWAuQavtUuClsclF2LGGfne0O/dloZ7sS

hCPbgeIR7cDxKPbAeLR7QDx6HaAeHQ7QDy6HSAe3Q4Qj24HiEe3A8Sj2wHi0e

0A8eh2gHh0O0A8uh0gHt0OEI9uB4hHtwPEo9sB4tHtAPHodoB4dDtA

mnlhUzRvHr1LYORJbqdtN6Z53/hH+Zc4XR7WxnP8+yeB04P7phvWJrkOd+75Z

53CdMjb1/f57Fv/Lclw3GjUu3k9bi4nd/5sN51jDfkv370/nGpnNDO1/bcX8Zm3atr

u6fa26c72e5tpa3nr/Q9+/9KpQ+NvVh2+fNpg3KB0O0kd+eHsx5av8tjvC8O

Tlo384X/90d5VjK+P9dB6VXXu/tObNub4ZjJldfc1vnm5pGZppYrnb1jvVumazzFK

cPraTCi20nsVs5vzTUdPdtT2PmtZ4Z2XugZLHWr39iy6/TmkctdfcXequn219Ng3

Oh0O2ndypmJy3nWc6Orfb61yEmVi4PDJV3X/U7n+4bODY3M5b6Xx+2vp8G40

el20rotz506mGdVU20tec7NX9bl7v7vP/Cja/NSVA+nOTr68Hie23kYjFTpdtJ6Z75

2+Lk8a1toyH3VxOVc6+p9faTmC8XkcXzb3sP7H608bs7X02DcuHQ7ad2RZ48

eyLnOG71lXVuy0dmz2rpMMgSfnCsd7qdpD73G5/NvOzBB2u4pUW2T+PZSntFl

TeO1a/25AM/nPmBTm7eaTBuULqdpJ760nOZlz1b+zmb2Y6BrPTw3DvPclpobM

g2LT9d+xM4uP/RzC/R0x/5pMG4Qel2kjp7eTLP4md2ZLlX0VxTltNRFxo2zeS4nV

/mKxtc6Nmc+UEf+83k3ZWQyYzQ4Pjw2Y2I5ZWRlMDFjOTRmMGEzYTdhMmZi

NTI5N2VkMmM0OD5dL1Jvb3QgMTMgMCBSL1NpemUgMTU+PgolaVRleHQtNS

41LjAKc3RhcnR4cmVmCjMwNzc5CiUlRU9GCg==</fileContent>

</xDataBody>

</CMBC>

89 / 95

民生银行银企直联

22.工资卡信息校验(B2eSalaryCheckBankCard)

本部分更新日期:2024-04-11

校验批量代发时代发文件中本行卡信息的正确性

22.1.请求(B2eSalaryCheckBankCard)

  标记

说明

 <trnId>

客户端产生的交易唯一标识

 <insId>

业务流水号

 <acctNo>

签约账号（★）

 <FileNameContent> 文件内容（★）最大 5000 笔数据

长度

32

32

32

说明：最大 5000 笔,上送数据格式 明细序号|收款人姓名|收款人账号|交易金额|收款人开

户行行号|收款人开户行名称|收款人证件号码|收款人手机号码| 每行数据按照"^"拼

接； 其中 明细序号、收款人姓名、收款人账号、交易金额 为必输项 收款人开户行行

号、收款人开户行名称、收款人证件号码、收款人手机号码为非必输项 明细序号 从 1 开

始累加，交易金额、薪资发放金额，保留 2 位小数

22.2.响应(B2eSalaryCheckBankCard)

  标记

说明

 <trnId>

客户端产生的交易唯一标识

 <insId>

业务流水号

 <batchNo> 批次号

长度

32

32

32

90 / 95

民生银行银企直联

22.3.报文示例

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryCheckBankCard">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<acctNo>*********</acctNo>

<FileNameContent>1|胡思梦|****************|1|||||^2|刘平金

|6228480018536951272|1|************|中国农业银行

|110104195010102440|***********|^3|陶燕

|************|1||||***********|^4|胡思梦

|****************|1|||||</FileNameContent>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eSalaryCheckBankCard">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-04-09 09:23:41</dtServer>

<userKey>N</userKey>

91 / 95

民生银行银企直联

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<batchNo>BIFO2024040918354800000004676226</batchNo>

</xDataBody>

</CMBC>

23.工资卡信息校验(B2eSalaryCheckBankCard)

本部分更新日期:2024-04-11

获取该接口（B2eSalaryCheckBankCard）接口中返回工资卡校验的校验结果。

23.1.请求(B2eSalaryCheckBankCard)

  标记

说明

 <trnId>

客户端产生的交易唯一标识

 <insId>

业务流水号

 <CustomerSignId> 客户签约编码（★）

 <batchNo>

批次号（★）

23.2.响应(B2eSalaryCheckBankCard)

  标记

说明

 <trnId>

客户端产生的交易唯一标识

 <insId>

业务流水号

 <fileContent> 文件内容

长度

32

32

32

32

长度

32

32

32

92 / 95

民生银行银企直联
说明：fileContent 格式： 明细序号|收款人姓名|收款人账号|交易金额|收款人开户行行

号|收款人开户行名称|收款人证件号码|收款人手机号码|错误结果| 每行数据按照"^"拼

接；

23.3.报文示例

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eDownLoadSalaryCheckBankCardFile">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<batchNo>BIFO2024040909234100000004671226</batchNo>

<acctNo>*********</acctNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eDownLoadSalaryCheckBankCardFile">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-04-09 18:36:14</dtServer>

<userKey>N</userKey>

93 / 95

民生银行银企直联

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<fileContent>1|胡思梦|****************|1|||||户名校验失败|^2|刘平金

|6228480018536951272|1|************|中国农业银行

|110104195010102440|***********|他行账号，不支持查验|^3|陶燕

|************|1||||***********|户名校验失败|^4|胡思梦

|****************|1|||||户名校验失败|</fileContent>

</xDataBody>

</CMBC>

94 / 95

