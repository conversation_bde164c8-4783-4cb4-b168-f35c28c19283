银企直联接口文档

（公告查询）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 6

目录

民生银行银企直联

目录 .............................................................................................2

1. 公告查询(ENTNOTICEQRYB2E) ....................................................... 3

1.1. 请求(ENTNOTICEQRYB2E) ........................................................... 3

1.2. 响应(ENTNOTICEQRYB2E) ........................................................... 3

1.3. 例子 ...................................................................................... 4

2 / 6

民生银行银企直联

1.公告查询(EntNoticeQryB2e)

本部分更新日期:2021-04-02

查询企业网银最新公告。

公告内容 noticeContent：含 html 标签，可直接使用 html 页面解析，可根据标签自行

更改 css 样式。

1.1.请求(EntNoticeQryB2e)

  标记

说明

<xDataBody>

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

64

</xDataBody>

1.2.响应(EntNoticeQryB2e)

  标记

说明

<xDataBody>

 <trnId>

 <noticeList>

公告列表，重复项

  <noticeInfo>

  <noticeName>

公告标题

  <noticeContent>

公告内容（含标题）

  </noticeInfo>

长度

64

3 / 6

民生银行银企直联

 </noticeList>

 <compelNoticeShowFlag> 是否强制展示标志 （true-强制展示；

false 或者空值不强制）

 <compelNoticeIndex>

强制展示的公告在公告列表中的下标 （从

0 开始）

</xDataBody>

1.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="EntNoticeQryB2e">

<requestHeader>

<dtClient>2014-04-25 09:53:32</dtClient>

<clientId>2200002437</clientId>

<userId>2200002437001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>ceshi000</trnId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="EntNoticeQryB2e" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

4 / 6

民生银行银企直联

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2015-04-03 15:27:53</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>ceshi000</trnId>

<noticeList>

<noticeInfo>

<noticeName>版本机测试 1</noticeName>

<noticeContent>版本机测试 1</noticeContent>

</noticeInfo>

<noticeInfo>

<noticeName>版本测试 2 版本测试 2 自动弹出</noticeName>

<noticeContent>&lt;p&gt;版本测试 2 版本测试 2 自动弹出版本测试 2 版本

测试&lt;strong&gt;2 自动弹出版本测试 2&lt;/strong&gt;版本测试 2 自动弹出版本

&lt;/p&gt;&lt;p&gt;测试 2 版本测试 2&lt;span

style=&quot;font-size:18px;&quot;&gt;自动弹出版本测试 2 版本

&lt;/span&gt;测试 2 自动弹出版本测试 2 版&lt;em&gt;本测试 2 自动弹出版本测

&lt;/em&gt;试 2 版本测试 2 自动&lt;u&gt;弹出版本测试 2 版本测试 2 自动弹出版

&lt;/u&gt;本测试 2 版本测试 2 自动弹出版本测试 2 版本测试 2 自动弹出版本测试 2 版

本测试 2 自动弹出&lt;/p&gt;</noticeContent>

</noticeInfo>

</noticeList>

<compelNoticeShowFlag>true</compelNoticeShowFlag>

<compelNoticeIndex>1</compelNoticeIndex>

</xDataBody>

</CMBC>

5 / 6

