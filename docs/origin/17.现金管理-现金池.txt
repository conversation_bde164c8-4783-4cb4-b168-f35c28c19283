银企直联接口文档

（现金管理-现金池）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 81

目录

民生银行银企直联

目录 .............................................................................................2

１ 币种对应货币代号 ............................................................................ 5

２ 现金池账户信息查询(CASHACTQRY) ................................................... 6

2.1. 请求(CASHACTQRY) .................................................................. 6

2.2. 响应(CASHACTQRY) .................................................................. 6

2.3. 例子 ...................................................................................... 8

３ 现金池上存下拨(B2ECASHPOOLUPDOWN) ....................................... 10

3.1. 请求(B2ECASHPOOLUPDOWN) ...................................................10

3.2. 响应(B2ECASHPOOLUPDOWN) ...................................................11

3.3. 例子 .................................................................................... 11

４ 账户资金归集关系查询(CASHPOOLSUBCOMPACTPAYLEVELQRY) .........13

4.1. 请求(CASHPOOLSUBCOMPACTPAYLEVELQRY) ................................ 13

4.2. 响应(CASHPOOLSUBCOMPACTPAYLEVELQRY) ................................ 13

4.3. 例子 .................................................................................... 15

５ 账户资金归集属性查询(CASHSIGNUPQRYGROUPQRY) ........................ 18

5.1. 请求(CASHSIGNUPQRYGROUPQRY) ............................................. 18

5.2. 响应(CASHSIGNUPQRYGROUPQRY) ............................................. 18

5.3. 例子 .................................................................................... 25

６ 不定期归集时间查询(NONTIMEQUERYB2E) ........................................34

6.1. 请求(NONTIMEQUERYB2E) ........................................................ 34

6.2. 响应(NONTIMEQUERYB2E) ........................................................ 34

6.3. 例子 .................................................................................... 36

７ 归集排除日期查询(REMOVETIMEQUERYB2E) .................................... 38

7.1. 请求(REMOVETIMEQUERYB2E) ................................................... 39

2 / 81

民生银行银企直联
7.2. 响应(REMOVETIMEQUERYB2E) ................................................... 39

7.3. 例子 .................................................................................... 40

８ 现金池内部归集明细查询(CASHPOOLTRANSDETAILQRY) ..................... 41

8.1. 请求(CASHPOOLTRANSDETAILQRY) ............................................. 42

8.2. 响应(CASHPOOLTRANSDETAILQRY) ............................................. 42

8.3. 例子 .................................................................................... 44

９ 银企直联现金池定期归集时间查询(FIXTIMEQUERYB2E) ......................... 46

9.1. 请求(FIXTIMEQUERYB2E) ..........................................................46

9.2. 响应(FIXTIMEQUERYB2E) ..........................................................46

9.3. 例子 .................................................................................... 48

１０ 现金池可支付能力查询(XJACCTAMT) ............................................... 49

10.1. 请求(XJACCTAMT) ................................................................. 50

10.2. 响应(XJACCTAMT) ................................................................. 50

10.3. 例子 ...................................................................................50

１１ 现金池支出限额查询(CASHSIGNLIMITPAYB2E) ................................52

11.1. 请求(CASHSIGNLIMITPAYB2E) ..................................................52

11.2. 响应(CASHSIGNLIMITPAYB2E) ..................................................52

11.3. 例子 ...................................................................................53

１２ 现金池下级支付预算控制 (CASHDOWNPAYCONTROL) .......................55

12.1. 请求(CASHDOWNPAYCONTROL) ................................................ 55

12.2. 响应(CASHDOWNPAYCONTROL) ................................................ 56

12.3. 示例： ................................................................................ 57

１３ 现金池汇总余额信息查询 (SIGNUPACTFINCDETAILB2E) .................... 58

13.1. 请求(CASHDOWNPAYCONTROL) ................................................ 58

13.2. 响应(CASHDOWNPAYCONTROL) ................................................ 59

3 / 81

民生银行银企直联
13.3. 示例： ................................................................................ 61

１４ 内部收支汇总查询(CASHRECPAYSUM) ........................................... 63

14.1. 请求(CASHRECPAYSUM) ......................................................... 63

14.2. 响应(CASHRECPAYSUM) ......................................................... 63

14.3. 例子 ...................................................................................65

１５ 现金池明细对应子公司入账信息查询(CASHPOOLSUBUPPAMTQRY) ....... 67

15.1. 请求(CASHPOOLTRANSDETAILQRY) ............................................67

15.2. 响应(CASHPOOLTRANSDETAILQRY) ............................................68

15.3. 例子 ...................................................................................70

１６ 银企直联现金池账户资金信息查询(XJCACTFINCDETAIL) ...................... 73

16.1. 请求(XJCACTFINCDETAIL) ....................................................... 73

16.2. 响应(XJCACTFINCDETAIL) ....................................................... 74

16.3. 例子 ...................................................................................75

１７ 现金池上存下拨交易状态查询

(B2ECASHPOOLQUERYCOLLECTALLOCATESTAT) .............................78

17.1. 请求(B2ECASHPOOLQUERYCOLLECTALLOCATESTAT) ...................... 78

17.2. 响应(B2ECASHPOOLQUERYCOLLECTALLOCATESTAT) ...................... 78

17.3. 例子 ...................................................................................79

4 / 81

1.币种对应货币代号

入 参

映射值

01

12

13

14

15

16

17

18

20

21

22

23

24

25

26

27

28

29

38

42

50

CNY

GBP

HKD

USD

CHF

DEM

FRF

SGD

NLG

SEK

DKK

NOK

ATS

BEF

ITF

JPY

CAD

AUD

EUR

FIM

NZD

民生银行银企直联

码值含义

人民币

英镑

港币

美元

瑞士法郎

德国马克

法国法郎

新加坡元

荷兰盾

瑞典克郎

丹麦克郎

挪威克郎

奥地利先令

比利时法郎

意大利里拉

日元

加拿大元

澳大利亚元

欧元

芬兰马克

新西兰元

5 / 81

2.现金池账户信息查询(CashActQry)

民生银行银企直联

本部分更新日期:2021-05-08

本功能用于查询客户现金池签约账户信息。

2.1.请求(CashActQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志(必输，但无作用)(★)

</xDataBody>

2.2.响应(CashActQry)

长

度

64

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志(必输，但无作

64

用)(★)

 <List>

  <Map>

   <acNo>

查询账号

   <acName>

账户名称

   <currency>

账号币种

   <acAlias>

别名

35

70

2

70

6 / 81

民生银行银企直联

20

70

30

20

70

20

70

12

255

2

   <entCode>

企业代码

   <entName>

企业名称

   <hostCifNo>

核心客户号

   <openDeptCode> 开户机构代码

   <openDeptName> 开户机构名称

   <signDeptCode>

签约机构代码

   <signDeptName> 签约机构名称

   <bankNo>

开户行号

   <bankName>

开户行名

   <acType>

账户种类

01 - 活期户

02 - 定期户

03 - 通知户

04 - 活存透支户

05 - 协定户

06 - 贷款户

   <acProperties>

账户性质

2

00 - 基本存款账户

01 - 一般存款账户

02 - 专用存款账户

03 - 临时存款账户

04 - 注册验资临时存款账户

05 - 保证金账户

06 - 外汇账户

07 - 个人银行结算账户

7 / 81

   <inOutType>

收支类型：

0-收入类

1-收支类

2-支出类

   <acNoLevel>

关系级别, 只允许为 0-9 数字, 1-顶级

民生银行银企直联

2

n

  </Map>

 </List>

</xDataBody>

2.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CashActQry">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>**********</trnId>

</xDataBody>

</CMBC>

响应报文：

8 / 81

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashActQry" security="none" lang="chs" header="100"

民生银行银企直联

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-14 11:03:26</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>**********</trnId>

<List>

<Map>

<acNo>*********</acNo>

<currency>01</currency>

<acName>董新瓒版本 02</acName>

<acAlias></acAlias>

<entCode>**********</entCode>

<entName>董新瓒版本 02</entName>

<hostCifNo>**********</hostCifNo>

<openDeptCode>3307</openDeptCode>

<openDeptName>中国民生银行长春民丰大街支行</openDeptName>

<signDeptCode>3307</signDeptCode>

<signDeptName>中国民生银行长春民丰大街支行</signDeptName>

<bankNo></bankNo>

<acType>01</acType>

<acProperties>01</acProperties>

<inOutType>1</inOutType>

<acNoLevel>1</acNoLevel>

</Map>

</List>

</xDataBody>

</CMBC>

9 / 81

3.现金池上存下拨(B2ECashPoolUpDown)

民生银行银企直联

本部分更新日期:2021-04-02

主要提供现金池归集关系账户之间的手动划拨功能，包括上划及下拨。

1.上级账户需签约网银

2.该操作员需对此上级账户有查询权限

3.签约账户归集关系中，下级账户的操作权限为所有上级

4.上存时，收款账号是付款账号的上级

5.上存时付款账号不能是支出类账户

6.下拨时，付款账号是收款账号的上级

7.下拨时付款账号不能是收入类账户

3.1.请求(B2ECashPoolUpDown)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <cltcookie>

可选，客户端 cookie，响应时原值返回（★）

 < insId >

指令 ID，一条转账指令在客户端的唯一标识（★）

 <acNo>

客户号下加挂网银的账号，是付款账号和收款账号的

上级账号 (★)

 <payerAcNo> 付款账号（★）

 <payeeAcNo> 收款账号（★）

 <amount>

金额（★）

 <upFlag>

上存下拨标识（★）1-上存 2-下拨

长度

64

64

35

35

35

15,2

1

10 / 81

 <remark>

用途

 <currency>

币种（★）

2

民生银行银企直联

</xDataBody>

3.2.响应(B2ECashPoolUpDown)

  标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <svrId>

服务器该笔交易的标识（★）

 <insId>

指令 ID，请求时给出的 ID（★）

</xDataBody>

3.3.例子

请求报文

长度

32

64

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ECashPoolUpDown">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>80531224</trnId>

11 / 81

<cltcookie></cltcookie>

<insId>80535099</insId>

<acNo>0101014130000653</acNo>

<payerAcNo>0101014830000648</payerAcNo>

<payeeAcNo>0101014830000648</payeeAcNo>

<amount>1</amount>

<upFlag>1</upFlag>

<remark>平台付款 fafsd</remark>

<currency>01</currency>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2ECashPoolUpDown"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-17 09:43:47</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>80531224</trnId>

<svrId></svrId>

<insId>80535099</insId>

</transfer>

</xDataBody>

</CMBC>

民生银行银企直联

12 / 81

民生银行银企直联
4.账户资金归集关系查询(CashPoolSubCompActPa

yLevelQry)

本部分更新日期:2021-04-02

主要提供现金池（集团总账户及委贷资金池）的账户归集关系树查询功能。以账户为条
件，查询该账户所属现金池中自身及其所有下级账户信息，若该账户在现金池中是最高
级，则返回整个现金池归集树，否则返回结果为现金池的一个子树。

账号需要加挂网银。

4.1.请求(CashPoolSubCompActPayLevelQry)

  标记

说明

<xDataBody>

 <AcNo>

加挂网银账号(★)

 <Currency> 币种(★)

</xDataBody>

4.2.响应(CashPoolSubCompActPayLevelQry)

  标记

说明

<xDataBody>

 <Levelist>

 <Map>

  <acNo>

  <curCode>

账号

币种

  <acName>

账户名称

长度

32

2

长度

32

2

70

13 / 81

民生银行银企直联

  <acnoAlias>

账户别名

  <acnoOrder>

显示顺序

  <cmsCorpNo>

企业代码

  <corpCnName> 企业名称

  <hostCustNo>

核心客户号

  <orgCode>

开户机构码

  <inoutType>

收支类型：0-收入类 ，1-收支类 ，2 支出类

  <acnoLevel>

关系级别（集团联动节点等级）：1-总账户 2-级

  <endLevel>

是否最底层(视查询效率尽量实现)

  <uppAcno>

上级账号

  <uppCurCode> 上级币种

  <uppAcname> 上级户名

  <topAcno>

最高级账号

  <topCurCode> 最高级币种

  <opFlag>

操作权限： 0-直接上级 1-最顶级 2-直接上级和

最顶级 3-所有上级 4-本级

  <qryFlag>

查询权限： 0-直接上级 1-最顶级 2-直接上级和

最顶级 3-所有上级 4- 本级

 <Map>

 <Levelist>

</xDataBody>

70

3

20

70

30

20

35

2

70

35

2

14 / 81

民生银行银企直联

4.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="CashPoolSubCompActPayLevelQry" version="100">

<requestHeader>

<dtClient>2014-11-11 15:00:00</dtClient>

<clientId>**********</clientId>

<userId>**********002</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<AcNo>*********</AcNo>

<Currency>01</Currency>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashPoolSubCompActPayLevelQry" security="none"

lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-14 09:23:14</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

15 / 81

<Levelist>

<Map>

<acNo>*********</acNo>

<curCode>CNY</curCode>

<acName>董新瓒版本 02</acName>

<acnoAlias></acnoAlias>

<acnoOrder></acnoOrder>

<cmsCorpNo>**********</cmsCorpNo>

<corpCnName>董新瓒版本 02</corpCnName>

<hostCustNo>**********</hostCustNo>

<orgCode>3307</orgCode>

<orgName>中国民生银行长春民丰大街支行</orgName>

<inoutType>1</inoutType>

<acnoLevel>1</acnoLevel>

<endLevel>0</endLevel>

<uppAcno>*********</uppAcno>

<uppCurCode>CNY</uppCurCode>

<uppAcname>董新瓒版本 02</uppAcname>

<topAcno>*********</topAcno>

<topCurCode>CNY</topCurCode>

<opFlag></opFlag>

<qryFlag></qryFlag>

</Map>

<Map>

<acNo>*********</acNo>

<curCode>CNY</curCode>

<acName>董新瓒版本 01</acName>

<acnoAlias></acnoAlias>

<acnoOrder></acnoOrder>

<cmsCorpNo>2200004252</cmsCorpNo>

<corpCnName>董新瓒版本 01</corpCnName>

<hostCustNo>2200004252</hostCustNo>

<orgCode>3307</orgCode>

<orgName>中国民生银行长春民丰大街支行</orgName>

<inoutType>1</inoutType>

<acnoLevel>2</acnoLevel>

<endLevel>0</endLevel>

民生银行银企直联

16 / 81

民生银行银企直联

<uppAcno>*********</uppAcno>

<uppCurCode>CNY</uppCurCode>

<uppAcname>董新瓒版本 02</uppAcname>

<topAcno>*********</topAcno>

<topCurCode>CNY</topCurCode>

<opFlag>3</opFlag>

<qryFlag>3</qryFlag>

</Map>

<Map>

<acNo>*********</acNo>

<curCode>CNY</curCode>

<acName>董新瓒版本 01</acName>

<acnoAlias></acnoAlias>

<acnoOrder></acnoOrder>

<cmsCorpNo>2200004252</cmsCorpNo>

<corpCnName>董新瓒版本 01</corpCnName>

<hostCustNo>2200004252</hostCustNo>

<orgCode>3307</orgCode>

<orgName>中国民生银行长春民丰大街支行</orgName>

<inoutType>1</inoutType>

<acnoLevel>3</acnoLevel>

<endLevel>1</endLevel>

<uppAcno>*********</uppAcno>

<uppCurCode>CNY</uppCurCode>

<uppAcname>董新瓒版本 01</uppAcname>

<topAcno>*********</topAcno>

<topCurCode>CNY</topCurCode>

<opFlag>3</opFlag>

<qryFlag>3</qryFlag>

</Map>

</Levelist>

</xDataBody>

</CMBC>

17 / 81

民生银行银企直联
5.账户资金归集属性查询(CashSignUpQryGroupQr

y)

本部分更新日期:2021-04-02

1.主要提供现金池（集团总账户及委贷资金池）的账户归集属性查询功能，返回单个账户

在归集关系树中的归集属性。 交易要求

2.上级账号需要加挂网银

3.被查询账号是上级账号的下级

4.签约账户归集关系中，上级账号需要有被查询账号的查询权限

5.1.请求(CashSignUpQryGroupQry)

  标记

说明

<xDataBody>

 <AcNo>

客户号下加挂网银的账号，是查询账号的上级账号(★)

 <Currency> 币种(★)

 <SubAcNo> 查询账号，是加挂网银账号的下级账号(★)

</xDataBody>

5.2.响应(CashSignUpQryGroupQry)

长度

32

2

32

  标记

说明

长度

<xDataBody>

 <Levelist>

 <Map>

18 / 81

民生银行银企直联

  <acNo>

  <currency>

账号

币种

  <acName>

账户名称

  <acAlias>

账户别名

  <acOrder>

显示顺序

  <entCode>

企业代码

  <entName>

企业名称

  <hostCifNo>

核心客户号

  <openDeptCode>

开户机构码

  <enDeptName>

开户机构名称

  <signDeptCode>

签约机构代码

  <signDeptName>

签约机构名称

  <inoutType>

收支类型：0-收入类 ，1-收支类 ，2 支出类

  <productNo>

产品号

  <seqNo>

归集关系号

  <relInherit>

层级关系继承标志

  <acnoLevel>

关系级别

  <uppAcno>

上级账号

  <uppCurrency>

上级币种

  <uppAcName>

上级户名

35

2

70

70

30

20

70

30

20

70

20

70

38

5

60

35

2

70

19 / 81

  <topAcNo>

最高级账号

  <tTopCurrency>

最高级币种

民生银行银企直联

35

2

  <reverseFlag>

反向归集标志(此属性仅 1 级账号可以设

置): 0- 开通 1-不开通

  <accrualFlag>

上存计息标志：0-不计息；1-计息

  <accrualMode>

透支是否计息：0-不计息；1-计息

  <accrualCyc>

利息再分配周期：1-每月；2-每季

  <assignFlag>

自动分配利息标志：0 -不分配；1 -分配

  <neCashFlag>

是否动用上存：1- 动用上存；2 -不动用上存

  <neCashMode>

上级余额不足(负头寸)时处理方式： 0-随

机 1-先大后小 2-先小后大 3-账户优先 4-加

  <stampTax>

  <alesTax>

权平均

印花税

营业税

  <salesTax>

营业税附加

18,2

18,2

18,2

  <gatherType>

归集类型：1-实时归集；2-批量归集；V-归

集规则

  <conSignFlag>

委托贷款标志：0-是；1-否

  <assignee>

受托人

70

  <crRateType>

上存利率类型： 0-固定利率 1-分段利率 2-

浮动利率

  <crRate>

上存利率（固定利率有效/浮动利率为执行利 8,5

20 / 81

率）

民生银行银企直联

  <crRateBaseType>

上存浮动利率基准类型（浮动利率有效）

  <crRatePrefer>

上存浮动利率浮动比例（浮动利率有效）

  <crAmountOne>

上存分段起始金额 1（分段利率有效）

  <crRateOne>

上存分段利率 1（分段利率有效）

  <crAmountTwo>

上存分段起始金额 2（分段利率有效）

  <crRateTwo>

上存分段利率 2（分段利率有效）

  <crAmountThree>

上存分段起始金额 3（分段利率有效）

  <crRateThree>

上存分段利率 3（分段利率有效）

  <crAmountFour>

上存分段起始金额 4（分段利率有效）

  <crRateFour>

上存分段利率 4（分段利率有效）

  <crAmountFive>

上存分段起始金额 5（分段利率有效）

  <crRateFive>

上存分段利率 5（18,2）

2

8,5

18,2

8,5

18,2

8,5

18,2

8,5

18,2

8,5

18,2

8,5

  <drRateType>

透支利率类型： 0-固定利率 1-分段利率 2-

浮动利率

  <drRate>

透支利率（固定利率有效/浮动利率为执行利

8,5

率）

  <drRateBaseType>

透支浮动利率基准类型（浮动利率有效）

  <drRatePrefer>

透支浮动利率浮动比例（浮动利率有效）

  <drAmountOne>

透支分段起始金额 1（分段利率有效）

  <drRateOne>

透支分段利率 1（分段利率有效）

2

8,5

18,2

8,5

21 / 81

民生银行银企直联

  <drAmountTwo>

透支分段起始金额 2（分段利率有效）

  <drRateTwo>

透支分段利率 2（分段利率有效）

  <drAmountThree>

透支分段起始金额 3（分段利率有效）

  <drRateThree>

透支分段利率 3（分段利率有效）

  <drAmountFour>

透支分段起始金额 4（分段利率有效）

  <drRateFour>

透支分段利率 4（分段利率有效）

  <drAmountFive>

透支分段起始金额 5（分段利率有效）

  <drRateFive>

透支分段利率 5（分段利率有效）

  <startDate>

生效日期

  <endDate>

终止日期

18,2

8,5

18,2

8,5

18,2

8,5

18,2

8,5

8

8

  <payMode>

支控方式（仅收支账户有效）： 0- 统收统

支 1- 以收定支 2- 超额定支 3- 下拨支付

  <exPayAmt>

超额支取额度（仅收支账户有效）

  <exPayFlag>

超额支取标志（仅实时归集有效）： 0 - 不

能超额支取 1 - 可以超额支取

  <exUpFlag>

超额上划标志（仅实时归集有效）： 0 - 不

能超额上划 1 - 可以超额上划

  <GatherCyc>

归集周期（仅批量归集有效）： 1 - 每天 2 -

每月 3 - 不定期归集

  <gatherDate>

归集日期（仅批量归集有效）： 每月归集时

有效，取值范围 1 – 31

  <gatherMode>

归集方式（仅批量归集有效）： 02 - 取整归

22 / 81

民生银行银企直联

集(账户余额) 03 - 限额归集(超出限额差额

上划) 04 - 全额归集 05 - 超限额后全额归集

(超过限额全额上划) 06-定额归集 07 - 比例

归集(自身余额)

  <fundDirect>

归集方向（仅批量归集有效）： 1-收入、支

出 2-收支

  <upPercent>

向上归集百分比（仅批量归集有效）

8,4

  <lowAmt>

最低留存限额（仅批量归集有效）：支出、

18,2

收支账户时显示留存金额

  <hightAmt>

最高划拨限额（仅批量归集有效）

18,2

  <fullUnit>

取整归集单位（仅批量归集有效）

  <objectAmt>

目标限额（仅批量归集有效）

18,2

  <pileAmtFlag>

最高累计归集标志（归集方式为全部上存时

有效） 0 -否 1 -是

  <maxBal>

最高累计归集余额(归集方式为全部上存时有

18,2

效)

  <acNoPri>

上级余额不足账户优先级（仅批量归集有

效） 000-级别最高 999-级别最低

  <returnFlag>

透支归还下级透支标志：0-归还；1-不归还

  <updateFlag>

归集更新支付限额标志

  <autoFreStat>

自动冻结标志：0 – 自动冻结；1 – 非自动冻

结

  <selfFreeze>

自身冻结状态：0 - 正常；1 - 冻结

23 / 81

民生银行银企直联

  <uppFreeze>

上级冻结状态：0 - 正常；1 - 冻结

  <upDateStat>

更新标志

  <opFlag>

操作权限： 0 -直接上级 1 -最顶级 2 -直接

上级和最顶级 3 -所有上级 4 -本级

  <qryFlag>

查询权限： 0 -直接上级 1 -最顶级 2 -直接

上级和最顶级 3 -所有上级 4 -本级

  <reserved1>

备用字段 1

  <reservede2>

备用字段 2

  <reserved3>

备用字段 3

  <reserved4>

备用字段 4

  <reserved5>

备用字段 5

  <realFrequence>

实时频率：01 - 实时归集

  <dayendFrequence> 日终频率： 02 - 日终每天 03 - 日终每月，

日期为 gather_date 04 - 日终不定期，网

银可不显示此记录

  <timeFrequence>

定时频率： 05 - 定时每天 06 - 定时每

周 07 - 定时每月 08 - 定时月末

  <aperiodFrequence> 不定期频率： 09 - 不定期隔天 10 - 不定期

每周 11 - 不定期每月 12 - 不定期月末

 <Map>

 <Levelist>

</xDataBody>

24 / 81

民生银行银企直联

5.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="CashSignUpQryGroupQry" version="100">

<requestHeader>

<dtClient>2014-11-11 15:00:00</dtClient>

<clientId>**********</clientId>

<userId>**********002</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<AcNo>*********</AcNo>

<SubAcNo></SubAcNo>

<Currency>01</Currency>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashSignUpQryGroupQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-14 09:31:04</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

25 / 81

<xDataBody>

<Levelist>

<Map>

民生银行银企直联

<acNo>*********</acNo>

<currency></currency>

<acName>董新瓒版本 02</acName>

<acAlias></acAlias>

<acOrder></acOrder>

<entCode></entCode>

<entName></entName>

<hostCifNo></hostCifNo>

<openDeptCode></openDeptCode>

<openDeptName></openDeptName>

<signDeptCode></signDeptCode>

<signDeptName></signDeptName>

<inOutType></inOutType>

<productNo></productNo>

<seqNo></seqNo>

<relInherit></relInherit>

<acnoLevel>1</acnoLevel>

<uppAcno>*********</uppAcno>

<uppCurrency></uppCurrency>

<uppAcName></uppAcName>

<topAcNo></topAcNo>

<tTopCurrency></tTopCurrency>

<reverseFlag></reverseFlag>

<accrualFlag></accrualFlag>

<accrualMode></accrualMode>

<accrualCyc></accrualCyc>

<assignFlag></assignFlag>

<neCashFlag></neCashFlag>

<neCashMode></neCashMode>

<stampTax></stampTax>

<salesTax></salesTax>

<saleTaxExt></saleTaxExt>

<gatherType></gatherType>

<conSignFlag></conSignFlag>

<assignee></assignee>

26 / 81

<crRateType></crRateType>

<crRate></crRate>

<crRateBaseType></crRateBaseType>

<crRatePrefer></crRatePrefer>

<crAmountOne></crAmountOne>

<crRateOne></crRateOne>

<crAmountTwo></crAmountTwo>

<crRateTwo></crRateTwo>

<crAmountThree></crAmountThree>

<crRateThree></crRateThree>

<crAmountFour></crAmountFour>

<crRateFour></crRateFour>

<crAmountFive></crAmountFive>

<crRateFive></crRateFive>

<drRateType></drRateType>

<drRate></drRate>

<drRateBaseType></drRateBaseType>

<drRatePrefer></drRatePrefer>

<drAmountOne></drAmountOne>

<drRateOne></drRateOne>

<drAmountTwo></drAmountTwo>

<drRateTwo></drRateTwo>

<drAmountThree></drAmountThree>

<drRateThree></drRateThree>

<drAmountFour></drAmountFour>

<drRateFour></drRateFour>

<drAmountFive></drAmountFive>

<drRateFive></drRateFive>

<startDate></startDate>

<endDate></endDate>

<payMode></payMode>

<exPayAmt></exPayAmt>

<exPayFlag></exPayFlag>

<exUpFlag></exUpFlag>

<GatherCyc></GatherCyc>

<gatherDate></gatherDate>

<gatherMode></gatherMode>

<fundDirect></fundDirect>

<upPercent></upPercent>

民生银行银企直联

27 / 81

<lowAmt></lowAmt>

<hightAmt></hightAmt>

<fullUnit></fullUnit>

<objectAmt></objectAmt>

<pileAmtFlag></pileAmtFlag>

<maxBal></maxBal>

<acNoPri></acNoPri>

<returnFlag></returnFlag>

<updateFlag></updateFlag>

<autoFreStat></autoFreStat>

<selfFreeze></selfFreeze>

<uppFreeze></uppFreeze>

<upDateStat></upDateStat>

<opFlag></opFlag>

<qryFlag></qryFlag>

<reserved1></reserved1>

<reserved2></reserved2>

<reserved3></reserved3>

<reserved4></reserved4>

<reserved5></reserved5>

<realFrequence></realFrequence>

<dayendFrequence></dayendFrequence>

<timeFrequence></timeFrequence>

<aperiodFrequence></aperiodFrequence>

</Map>

<Map>

<acNo>*********</acNo>

<currency></currency>

<acName>董新瓒版本 01</acName>

<acAlias></acAlias>

<acOrder></acOrder>

<entCode></entCode>

<entName></entName>

<hostCifNo></hostCifNo>

<openDeptCode></openDeptCode>

<openDeptName></openDeptName>

<signDeptCode></signDeptCode>

<signDeptName></signDeptName>

民生银行银企直联

28 / 81

<inOutType></inOutType>

<productNo></productNo>

<seqNo></seqNo>

<relInherit></relInherit>

<acnoLevel>2</acnoLevel>

<uppAcno>*********</uppAcno>

<uppCurrency></uppCurrency>

<uppAcName></uppAcName>

<topAcNo></topAcNo>

<tTopCurrency></tTopCurrency>

<reverseFlag></reverseFlag>

<accrualFlag></accrualFlag>

<accrualMode></accrualMode>

<accrualCyc></accrualCyc>

<assignFlag></assignFlag>

<neCashFlag></neCashFlag>

<neCashMode></neCashMode>

<stampTax></stampTax>

<salesTax></salesTax>

<saleTaxExt></saleTaxExt>

<gatherType></gatherType>

<conSignFlag></conSignFlag>

<assignee></assignee>

<crRateType></crRateType>

<crRate></crRate>

<crRateBaseType></crRateBaseType>

<crRatePrefer></crRatePrefer>

<crAmountOne></crAmountOne>

<crRateOne></crRateOne>

<crAmountTwo></crAmountTwo>

<crRateTwo></crRateTwo>

<crAmountThree></crAmountThree>

<crRateThree></crRateThree>

<crAmountFour></crAmountFour>

<crRateFour></crRateFour>

<crAmountFive></crAmountFive>

<crRateFive></crRateFive>

<drRateType></drRateType>

<drRate></drRate>

民生银行银企直联

29 / 81

<drRateBaseType></drRateBaseType>

<drRatePrefer></drRatePrefer>

<drAmountOne></drAmountOne>

<drRateOne></drRateOne>

<drAmountTwo></drAmountTwo>

<drRateTwo></drRateTwo>

<drAmountThree></drAmountThree>

<drRateThree></drRateThree>

<drAmountFour></drAmountFour>

<drRateFour></drRateFour>

<drAmountFive></drAmountFive>

<drRateFive></drRateFive>

<startDate></startDate>

<endDate></endDate>

<payMode></payMode>

<exPayAmt></exPayAmt>

<exPayFlag></exPayFlag>

<exUpFlag></exUpFlag>

<GatherCyc></GatherCyc>

<gatherDate></gatherDate>

<gatherMode></gatherMode>

<fundDirect></fundDirect>

<upPercent></upPercent>

<lowAmt></lowAmt>

<hightAmt></hightAmt>

<fullUnit></fullUnit>

<objectAmt></objectAmt>

<pileAmtFlag></pileAmtFlag>

<maxBal></maxBal>

<acNoPri></acNoPri>

<returnFlag></returnFlag>

<updateFlag></updateFlag>

<autoFreStat></autoFreStat>

<selfFreeze></selfFreeze>

<uppFreeze></uppFreeze>

<upDateStat></upDateStat>

<opFlag>3</opFlag>

<qryFlag>3</qryFlag>

<reserved1></reserved1>

民生银行银企直联

30 / 81

<reserved2></reserved2>

<reserved3></reserved3>

<reserved4></reserved4>

<reserved5></reserved5>

<realFrequence></realFrequence>

<dayendFrequence></dayendFrequence>

<timeFrequence></timeFrequence>

<aperiodFrequence></aperiodFrequence>

</Map>

<Map>

<acNo>*********</acNo>

<currency></currency>

<acName>董新瓒版本 01</acName>

<acAlias></acAlias>

<acOrder></acOrder>

<entCode></entCode>

<entName></entName>

<hostCifNo></hostCifNo>

<openDeptCode></openDeptCode>

<openDeptName></openDeptName>

<signDeptCode></signDeptCode>

<signDeptName></signDeptName>

<inOutType></inOutType>

<productNo></productNo>

<seqNo></seqNo>

<relInherit></relInherit>

<acnoLevel>3</acnoLevel>

<uppAcno>*********</uppAcno>

<uppCurrency></uppCurrency>

<uppAcName></uppAcName>

<topAcNo></topAcNo>

<tTopCurrency></tTopCurrency>

<reverseFlag></reverseFlag>

<accrualFlag></accrualFlag>

<accrualMode></accrualMode>

<accrualCyc></accrualCyc>

<assignFlag></assignFlag>

<neCashFlag></neCashFlag>

民生银行银企直联

31 / 81

<neCashMode></neCashMode>

<stampTax></stampTax>

<salesTax></salesTax>

<saleTaxExt></saleTaxExt>

<gatherType></gatherType>

<conSignFlag></conSignFlag>

<assignee></assignee>

<crRateType></crRateType>

<crRate></crRate>

<crRateBaseType></crRateBaseType>

<crRatePrefer></crRatePrefer>

<crAmountOne></crAmountOne>

<crRateOne></crRateOne>

<crAmountTwo></crAmountTwo>

<crRateTwo></crRateTwo>

<crAmountThree></crAmountThree>

<crRateThree></crRateThree>

<crAmountFour></crAmountFour>

<crRateFour></crRateFour>

<crAmountFive></crAmountFive>

<crRateFive></crRateFive>

<drRateType></drRateType>

<drRate></drRate>

<drRateBaseType></drRateBaseType>

<drRatePrefer></drRatePrefer>

<drAmountOne></drAmountOne>

<drRateOne></drRateOne>

<drAmountTwo></drAmountTwo>

<drRateTwo></drRateTwo>

<drAmountThree></drAmountThree>

<drRateThree></drRateThree>

<drAmountFour></drAmountFour>

<drRateFour></drRateFour>

<drAmountFive></drAmountFive>

<drRateFive></drRateFive>

<startDate></startDate>

<endDate></endDate>

<payMode></payMode>

<exPayAmt></exPayAmt>

民生银行银企直联

32 / 81

<exPayFlag></exPayFlag>

<exUpFlag></exUpFlag>

<GatherCyc></GatherCyc>

<gatherDate></gatherDate>

<gatherMode></gatherMode>

<fundDirect></fundDirect>

<upPercent></upPercent>

<lowAmt></lowAmt>

<hightAmt></hightAmt>

<fullUnit></fullUnit>

<objectAmt></objectAmt>

<pileAmtFlag></pileAmtFlag>

<maxBal></maxBal>

<acNoPri></acNoPri>

<returnFlag></returnFlag>

<updateFlag></updateFlag>

<autoFreStat></autoFreStat>

<selfFreeze></selfFreeze>

<uppFreeze></uppFreeze>

<upDateStat></upDateStat>

<opFlag>3</opFlag>

<qryFlag>3</qryFlag>

<reserved1></reserved1>

<reserved2></reserved2>

<reserved3></reserved3>

<reserved4></reserved4>

<reserved5></reserved5>

<realFrequence></realFrequence>

<dayendFrequence></dayendFrequence>

<timeFrequence></timeFrequence>

<aperiodFrequence></aperiodFrequence>

</Map>

</Levelist>

</xDataBody>

</CMBC>

民生银行银企直联

33 / 81

6.不定期归集时间查询(NonTimeQueryB2e)

民生银行银企直联

本部分更新日期:2021-04-02

主要提供批量归集中归集周期设置为不定期归集的账号归集时间查询功能。 上级账户需
签约网银 该操作员需对此上级账户有查询权限 签约账户归集关系中，下级账户的查询权
限为所有上级.

6.1.请求(NonTimeQueryB2e)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标（必输，但无作用）（★）

 <acntNo>

上级账户（★）

 <subAcntNo> 下级账户（★）

 <currency>

下级账户币种（★）

</xDataBody>

6.2.响应(NonTimeQueryB2e)

长

度

64

32

32

3

  标记

说明

长度

<xDataBody>

 <trnId>

客户端交易的唯一标志（★）

 <timeList>

  <timeMap>

   <acName>

户名

70

34 / 81

民生银行银企直联

   <acNo>

   <curCode>

账号

币种

   <state>

状态：0 - 归集 ；1 – 取消归集

   <productNo>

产品号（暂时无用）

   <gatherFlag>

归集类型： 空 - 未设置 0 - 每天 1 -隔天 2 -每

周 3 -每月 4 -月末

   <nextTime>

下次归集时间 （yyyyMMddHHmmss）

35

2

8

38

1

14

   <weeksCode>

周归集标志： 7 位位串，每位 0 为不归集，1 为归

7

集

   <tertianStart>

隔天归集起始日(每月几号开始归集)

   <tertianDays>

隔天归集天数(隔几天归集一次)

   <monthendFlag

月末归集标志(0 为不归集，1 为归集)

>

1

1

1

   <janCode>

1 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

   <febCode>

2 月归集标志：29 位位串，每位 0 为不归集，1

29

为归集

   <marCode>

3 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

   <aprCode>

4 月归集标志：30 位位串，每位 0 为不归集，1

30

为归集

   <mayCode>

5 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

35 / 81

   <junCode>

6 月归集标志：30 位位串，每位 0 为不归集，1

30

为归集

民生银行银企直联

   <julCode>

7 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

   <augCode>

8 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

   <sepCode>

9 月归集标志：30 位位串，每位 0 为不归集，1

30

为归集

   <octCode>

10 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

   <novCode>

11 月归集标志：30 位位串，每位 0 为不归集，1

30

为归集

   <decCode>

12 月归集标志：31 位位串，每位 0 为不归集，1

31

为归集

   <updateFlag>

当日更新标志

   <reserved1>

备用字段 1

   <reserved2>

备用字段 2

   <reserved3>

备用字段 3

  </timeMap>

 </timeList>

</xDataBody>

6.3.例子

请求报文

1

255

255

255

36 / 81

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="NonTimeQueryB2e">

<requestHeader>

<dtClient>2014-10-17 14:25:11</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>2014101714251062afb035-a4ed-4f30-a825-fds</trnId>

<acntNo>*********</acntNo>

<subAcntNo>*********</subAcntNo>

<currency>CNY</currency>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="NonTimeQueryB2e" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-13 16:49:12</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>2014101714251062afb035-a4ed-4f30-a825-fds</trnId>

37 / 81

民生银行银企直联

<timeList>

<timeMap>

<acName>董新瓒版本 01</acName>

<acNo>*********</acNo>

<curCode>01</curCode>

<state>0</state>

<productNo>0300001222</productNo>

<gatherFlag>3</gatherFlag>

<nextTime>20141130</nextTime>

<weeksCode>0000000</weeksCode>

<tertianStart>1</tertianStart>

<tertianDays>1</tertianDays>

<monthendFlag>0</monthendFlag>

<janCode>0000000000000000000000000000000</janCode>

<febCode>00000000000000000000000000000</febCode>

<marCode>0000000000000000000000000000000</marCode>

<aprCode>000010000000000000000000000000</aprCode>

<mayCode>0000000000000000000000000000000</mayCode>

<junCode>000000000000000000000000000000</junCode>

<julCode>0000000000000000000000000000000</julCode>

<augCode>0000000000000000000000000000000</augCode>

<sepCode>001000010001000100000000000000</sepCode>

<octCode>0000000000000000000000000000001</octCode>

<novCode>000000010001000000000000000001</novCode>

<decCode>0000000000000000000000000000000</decCode>

<updateFlag></updateFlag>

<reserved1>20141112</reserved1>

<reserved2></reserved2>

<reserved3></reserved3>

</timeMap>

</timeList>

</xDataBody>

</CMBC>

7.归集排除日期查询(RemoveTimeQueryB2e)

本部分更新日期:2021-04-02

38 / 81

主要提供现金池的账户归集排除日期查询功能。

7.1.请求(RemoveTimeQueryB2e)

民生银行银企直联

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）（★）

 <entCode>

企业代码（★）

</xDataBody>

7.2.响应(RemoveTimeQueryB2e)

长度

64

20

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <timeList>

  <timeMap>

   <state>

状态：0 - 正常 ；1 - 注销

   <productNo> 产品号（暂时无用）

   <cmsCorpNo> 企业代码

   <acNo>

   <curCode>

账号

币种

   <debarDate> 排除日期(yyyyMMdd)

   <reserved1>

备用字段 1

1

38

20

35

8

255

39 / 81

民生银行银企直联

255

255

70

   <reserved2>

备用字段 2

   <reserved3>

备用字段 3

   <acName>

户名

  </timeMap>

 </timeList>

</xDataBody>

7.3.例子

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="RemoveTimeQueryB2e">

<requestHeader>

<dtClient>2014-10-17 14:25:11</dtClient>

<clientId>2200004252</clientId>

<userId>2200004252001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>2014101714251062afb035-a4ed-4f30-a825-ff</trnId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="RemoveTimeQueryB2e" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

40 / 81

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-13 16:55:14</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>2014101714251062afb035-a4ed-4f30-a825-ff</trnId>

<timeList>

<timeMap>

<state>0</state>

<productNo>0300001222</productNo>

<cmsCorpNo>2200004252</cmsCorpNo>

<acNo>*********</acNo>

<curCode>01</curCode>

<debarDate>20141113</debarDate>

<reserved1></reserved1>

<reserved2></reserved2>

<reserved3></reserved3>

<acName>董新瓒版本 01</acName>

</timeMap>

</timeList>

</xDataBody>

</CMBC>

8.现金池内部归集明细查询(CashPoolTransDetailQr

y)

本部分更新日期:2021-04-02

1.上级账号需要加挂网银

2.被查询账号是上级账号的下级

3.签约账户归集关系中，上级账号需要有被查询账号的查询权限

41 / 81

8.1.请求(CashPoolTransDetailQry)

民生银行银企直联

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回（★）

64

 <uppAcNo>

查询账号的上级账号（★）

 <acNo>

查询账号（★）

 <startDate>

起始日期（★）

 <endDate>

结束日期（★）

 <currentIndex> 起始位置（★）

 <pageSize>

查询笔数（★）

</xDataBody>

8.2.响应(CashPoolTransDetailQry)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <allNum>

总笔数（★）

 <List>

  <Map>

   <transDate>

交易日期

长度

64

64

32

1

42 / 81

   <transTime>

交易时间

民生银行银企直联

   <serialNo>

交易流水号

32

   <payAmt>

   <rcvAmt>

收入

支出

   <supDownAmt> 上级下拨本级

   <uppAmt>

本级上存上级

   <subUppAmt>

下级上存本级

   <downAmt>

本级下拨下级

   <creditAmt>

本级透支金额

   <returnAmt>

本级归还透支

   <lendAmt>

借方发生额

   <loanAmt>

贷方发生额

   <balance>

账面余额

   <uppBal>

上存金额

   <selfBal>

自身余额

   <payeeAcNo>

收款账户

   <payerAcNo>

付款账户

   <certNo>

凭证号

   <remark>

摘要

   <transType>

交易类型：

1–自身收付款

43 / 81

民生银行银企直联

2–内部资金划拨

3–结息

4–收费

5–虚户间调账

6–虚户间转账

7–其它

  </xDataBody>

8.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CashPoolTransDetailQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>2200009314</clientId>

<userId>2200009314001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<uppAcNo>*********</uppAcNo>

<acNo>*********</acNo>

<startDate>2018-08-01</startDate>

<endDate>2018-11-01</endDate>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

44 / 81

<?xml version="1.0" encoding="utf-8"?>

民生银行银企直联

<CMBC trnCode="CashPoolTransDetailQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-03-28 14:10:05</dtServer>

<userKey>N</userKey>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<allNum>22</allNum>

<List>

<Map>

<transDate>20180807</transDate>

<transTime>133209</transTime>

<serialNo>37053098</serialNo>

<payAmt/>

<rcvAmt>50.00</rcvAmt>

<supDownAmt/>

<uppAmt/>

<subUppAmt/>

<downAmt/>

<creditAmt>0.00</creditAmt>

<returnAmt>0.00</returnAmt>

<lendAmt>50.00</lendAmt>

<loanAmt>0.00</loanAmt>

<balance/>

<uppBal/>

<selfBal/>

<payeeAcNo/>

<payerAcNo>*********</payerAcNo>

<certNo/>

45 / 81

民生银行银企直联

<remark>对公账户管理费</remark>

<transType>1</transType>

</Map>

</List>

</xDataBody>

</CMBC>

9.银企直联现金池定期归集时间查询(FixTimeQueryB

2e)

本部分更新日期:2021-04-02

1.主要提供批量归集中归集周期设置为非不定期归集的账号归集时间查询功能。

2.上级账号需要加挂网银

3.被查询账号是上级账号的下级

4.签约账户归集关系中，上级账号需要有被查询账号的查询权限

9.1.请求(FixTimeQueryB2e)

  标记

说明

<xDataBody>

 <uppAcno> 上级账号（★）

 <acno>

查询账户（★）

 <currency> 币种（★）

</xDataBody>

9.2.响应(FixTimeQueryB2e)

  标记

说明

长度

35

35

2

长度

46 / 81

<xDataBody>

 <acname>

 <acno>

 <curCode>

户名

账号

币种

 <stat>

取消归集标志：0-归集；1-取消归集

 <productNo>

产品号

民生银行银企直联

70

35

2

1

38

 <gatherFlag>

归集类型： 空- 未设置 0-每天 2-每周 3-每月 4-月末 1

 <nextTime>

下次归集时间(格式：yyyyMMddHHmmss)

 <dayFlag>

每天归集标志(无效字段)

 <weeksCode>

周归集标志(仅当归集类型为每周时生效)： 7 位字

串，每位 0 为不归集，1 为归集

 <monthendFlag> 月末归集标志

14

1

7

0 1

 <monthCode>

月归集标志(仅当归集类型为每月时生效)： 31 位位

31

串，每位 0 为不归集，1 为归集

 <timeCode>

归集时点串：每 6 位(HHmmss)为一个归集时点；无

48

分隔符

 <reserved1>

备用字段 1

 <reserved2>

备用字段 2

 <reserved3>

备用字段 3

</xDataBody>

255

255

255

47 / 81

民生银行银企直联

9.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="FixTimeQueryB2e">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<uppAcno>*********</uppAcno>

<acno>*********</acno>

<currency>01</currency>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="FixTimeQueryB2e" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-24 14:11:07</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

48 / 81

民生银行银企直联

</responseHeader>

<xDataBody>

<List>

<Map>

<acname>测试 2200001303</acname>

<acno>[*********, *********]</acno>

<curCode>[01, 01]</curCode>

<stat>0</stat>

<productNo>0300000762</productNo>

<gatherFlag>4</gatherFlag>

<nextTime>20141130090000</nextTime>

<dayFlag></dayFlag>

<weeksCode>0000000</weeksCode>

<tertianStart>0</tertianStart>

<tertianDays>0</tertianDays>

<monthendFlag></monthendFlag>

<monthCode>0011110000010000000010000000000</monthCode>

<timeCode>090000113000160000173000</timeCode>

<reserved1>20141031090000</reserved1>

<reserved2></reserved2>

<reserved3></reserved3>

</Map>

</List>

</xDataBody>

</CMBC>

10.现金池可支付能力查询(xjAcctAmt)

本部分更新日期:2021-04-02

根据客户号、账号和币种查询此账号的可支付金额。

1、此客户号下所有账号是否有一个或多个对所查账号具有查询权限，如果均没有对所查
询账号的查询权限则报错“没有该账户的现金池查询权限，不允许查询”。

2、有查询权限则调用可支付金额接口获得此账号的可支付金额。

49 / 81

民生银行银企直联

长度

32

12

长度

32

12

15,2

10.1.请求(xjAcctAmt)

  标记

说明

<xDataBody>

 <acntNo>

查询账号（★）

 <currCode> 账号币种

</xDataBody>

10.2.响应(xjAcctAmt)

  标记

说明

<xDataBody>

 <acntNo>

查询账号

 <currCode> 账号币种

 <amount>

可支付金额

</xDataBody>

10.3.例子

请求报文

<?xml version="1.0" encoding="GB2312" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="xjAcctAmt">

<requestHeader>

<dtClient>20120806 13:52:13</dtClient>

<clientId>2008766559</clientId>

<userId>200876655901</userId>

50 / 81

民生银行银企直联

<userPswd>888888</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

<acntNo>0901014210012928</acntNo>

<currCode>01</currCode>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0"?encoding="GB2312"?>

<CMBC ? header="100" ? lang="chs" ? security="none" ? trnCode="

xjAcctAmt " ?

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2012-08-06?13:52:20</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<acntNo>0901014210012928</acntNo>

<currCode>01</currCode>

<amount>1000.01</amount>

</xDataBody>

</CMBC>

51 / 81

11.现金池支出限额查询(CashSignLimitPayB2e)

民生银行银企直联

本部分更新日期:2021-04-02

1.主要提供账户支付限额查询功能。

2.上级账号需要加挂网银

3.被查询账号是上级账号的下级

4.签约账户归集关系中，上级账号需要有被查询账号的查询权限

11.1.请求(CashSignLimitPayB2e)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用） (★)

 <acNo>

客户号下加挂网银的账号，是查询账号的上级账号 (★)

 <acntNo>

查询账号，是加挂网银账号的下级账号 (★)

 <currency> 币种 (★)

</xDataBody>

11.2.响应(CashSignLimitPayB2e)

长度

64

35

35

2

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <recordNumber>

总数

52 / 81

民生银行银企直联

 <List>

  <Map>

   <acNo>

查询账号

   <acName>

账户名称

   <currency>

账号币种

   <limitFlag>

限制标志: 8 位限制位标志串：0-不限制，1-限

制 前 4 位顺序依次为单笔、日、月、不定期 后

4 位保留为 0

35

70

2

8

   <limitPerTrs>

单笔限额，0 为不限制 限制标志为 1 生效

18，2

   <limitPerDay>

日累计限额，0 为不限制 限制标志为 1 生效

18，2

   <limitPerMonth> 月累计限额，0 为不限制 限制标志为 1 生效

18，2

   <limitPerAny>

不定期限额，0 为不限制限 制标志为 1 生效

18，2

   <startDate>

不定期开始日期

   <endDate>

不定期结束日期

8

8

  </Map>

 </List>

</xDataBody>

11.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CashSignLimitPayB2e">

<requestHeader>

53 / 81

<dtClient>2008-03-13 18:44:12</dtClient>

民生银行银企直联

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>**********dsa</trnId>

<acNo>

*********

</acNo>

<acntNo>

*********

</acntNo>

<currency>01</currency>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashSignLimitPayB2e" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-13 16:43:30</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>**********dsa </trnId>

<List>

54 / 81

民生银行银企直联

<Map>

<acNo>*********</acNo>

<currency>01</currency>

<acName>董新瓒版本 01</acName>

<limitFlag>11110000</limitFlag>

<limitPerTrs>5000.00</limitPerTrs>

<limitPerDay>50000.00</limitPerDay>

<limitPerMonth>50000.00</limitPerMonth>

<limitPerAny>50000.00</limitPerAny>

<startDate>20141114</startDate>

<endDate>20141122</endDate>

</Map>

</List>

</xDataBody>

</CMBC>

12.现金池下级支付预算控制 (CashDownPayContro

l)

本部分更新日期:2021-04-02

1、只能设置支出账户和收支账户的支付限额，收入账户不可设置。顶级账户不能设置现
金池支付限额。

2、单笔限额小于等于日限额，日限额小于等于月限额，否则不予通过。

3、如果客户设置不定期限额后，必须选择不定期限额起始时间。时间没有最长时间限
制。

12.1.请求(CashDownPayControl)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）（★） 64

55 / 81

 <insId>

流水号（★）

 <topAcNo>

顶级账户（★）

 <AcNo>

账户（★）

 <AcName>

账户名称（★）

 <currency>

币种（★）

 <limitPerTrs>

单笔限额

 <limitPerDay>

日累计限额

 <limitPerMonth> 月累计限额

 <limitPerAny>

不定期限额

 <startDate>

不定期限额起始日期(yyyyMMdd)

 <endDate>

不定期限额截止日期(yyyyMMdd)

</xDataBody>

12.2.响应(CashDownPayControl)

  标记

说明

<xDataBody>

 <CashDownPayControl>

 <trnId>

 <insId>

原值返回

流水号

 </CashDownPayControl>

</xDataBody>

民生银行银企直联

64

32

32

15,2

15,2

15,2

15,2

8

8

长度

64

64

56 / 81

民生银行银企直联

12.3.示例：

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="CashDownPayControl"

version="100">

<requestHeader>

<dtClient>2016-03-07 10:00:00</dtClient>

<clientId>2200006980</clientId>

<userId>2200006980001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CashDownPayControltest</trnId>

<insId>145752124test19</insId>

<AcNo>*********</AcNo>

<AcName>现金管理优化 17</AcName>

<currency>01</currency>

<limitPerTrs>11</limitPerTrs>

<limitPerDay>174</limitPerDay>

<limitPerMonth>185</limitPerMonth>

<limitPerAny>12</limitPerAny>

<startDate>20160510</startDate>

<endDate>20160516</endDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashDownPayControl" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

57 / 81

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-03-09 16:39:31</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<CashDownPayControl>

<trnId>CashDownPayControltest</trnId>

<insId>145752124test19</insId>

</CashDownPayControl>

</xDataBody>

</CMBC>

13.现金池汇总余额信息查询 (SignUpActFincDetail

B2e)

本部分更新日期:2021-04-02

账号需要加挂网银，并有查询权限

13.1.请求(SignUpActFincDetailB2e)

  标记

说明

<xDataBody>

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）（★）

64

 <AcNo>

账号（★）

 <Currency> 币种（★）

01:人民币

12:英镑

58 / 81

民生银行银企直联

13:英镑

14:美元

15:瑞士法郎

16:德国马克

17:法国法郎

18:新加坡元

20:荷兰盾

21:瑞典克郎

22:丹麦克郎

23:挪威克郎

24:奥地利先令

25:比利时法郎

26:意大利里拉

27:日元

28:加拿大元

29:澳大利亚元

38:欧元

42:芬兰马克

50:新西兰元

</xDataBody>

13.2.响应(SignUpActFincDetailB2e)

  标记

说明

<xDataBody>

 <trnId>

交易唯一标志原值返回

 <Dmbal>

核心余额

 <DmUseBal>

核心可用余额

长度

64

59 / 81

民生银行银企直联

 <List>

  <Map>

   <AcNo>

   <CurCode>

账号

币种

   <AcName>

账户名称

   <AcnoAlias>

账户别名

   <AcnoOrder>

显示顺序

   <InoutType>

收支类型 0:收入类账户 1:收支类账户 2:支出

类账户

   <AcnoLevel>

关系级别

   <UppAcno>

上级账号

   <UppCurCode>

上级币种

   <UppAcname>

上级账户名称

   <TopAcno>

最高级账户

   <TopCurCode>

最高级币种

   <Balance>

余额

   <SelfBalance>

自身余额

   <SelfUseBalance> 自身可用余额

   <UppAmt>

上存金额

   <UppAccrual>

上存利息

   <OverAmt>

透支余额

60 / 81

民生银行银企直联

   <OverAccrual>

透支利息

   <OpFlag>

操作权限

   <QryFlag>

查询权限

  <Map>

 <List>

</xDataBody>

13.3.示例：

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="SignUpActFincDetailB2e" version="100">

<requestHeader>

<dtClient>2014-11-11 15:00:00</dtClient>

<clientId>2200009314</clientId>

<userId>2200009314001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>80531224</trnId>

<AcNo>*********</AcNo>

<Currency>01</Currency>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="SignUpActFincDetailB2e" security="none" lang="chs"

61 / 81

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-03-28 14:17:44</dtServer>

<userKey>N</userKey>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>80531224</trnId>

<List>

<Map>

<AcNo>*********</AcNo>

<CurCode>CNY</CurCode>

<AcName>UAT 银企直联现金池 4</AcName>

<AcnoAlias/>

<AcnoOrder>0</AcnoOrder>

<InoutType>2</InoutType>

<AcnoLevel>2</AcnoLevel>

<UppAcno>*********</UppAcno>

<UppCurCode>CNY</UppCurCode>

<UppAcname>UAT 银企直联现金池 4</UppAcname>

<TopAcno>*********</TopAcno>

<TopCurCode>CNY</TopCurCode>

<Balance>169416.68</Balance>

<SelfBalance>0.00</SelfBalance>

<SelfUseBalance>0.00</SelfUseBalance>

<UppAmt>0.00</UppAmt>

<UppAccrual>0.000000000000</UppAccrual>

<OverAmt>0.00</OverAmt>

<OverAccrual>0.000000000000</OverAccrual>

<OpFlag>3</OpFlag>

<QryFlag>3</QryFlag>

</Map>

</List>

民生银行银企直联

62 / 81

民生银行银企直联

</xDataBody>

</CMBC>

14.内部收支汇总查询(CashRecPaySum)

本部分更新日期:2021-04-02

主要提供现金池的归集账户内部收支汇总查询功能。

1.上级账号需要加挂网银

2.被查询账号是上级账号的下级

3.签约账户归集关系中，上级账号需要有被查询账号的查询权限

14.1.请求(CashRecPaySum)

  标记

说明

<xDataBody>

 <AcNo>

加挂网银账号（★）

 <Currency> 币种（★）

 <SubAcNo> 查询账号（★）

 <startDate> 开始日期（格式：yyyy-MM-dd）

</xDataBody>

14.2.响应(CashRecPaySum)

  标记

说明

<xDataBody>

 <Sumlist>

 <Map>

长度

35

2

35

10

长度

63 / 81

民生银行银企直联

35

  <acNo>

  <currency>

账号

币种

  <acName>

账户名称

  <acNoAlias

账户别名

  <inOutType>

收支类型: 0 -收入类 1- 收支类 2 -支出类

  <dAmt>

支出汇总金额

  <dTranNum>

支出交易笔数

  <cAmt>

收入汇总金额

  <cTranNum>

收入交易笔数

  <tran1Amt>

上存上级汇总金额

  <tran1TranNum> 上存上级交易笔数

  <tran2Amt>

下级上存汇总金额

  <tran2TranNum> 下级上存交易笔数

  <tran3Amt>

上级下拨汇总金额

  <tran3TranNum> 上级下拨交易笔数

  <tran4Amt>

下级下拨汇总金额

  <tran4TranNum> 下级下拨交易笔数

 <Map>

 <Sumlist>

</xDataBody>

64 / 81

民生银行银企直联

14.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="CashRecPaySum" version="100">

<requestHeader>

<dtClient>2014-11-11 15:00:00</dtClient>

<clientId>**********</clientId>

<userId>**********002</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<AcNo>*********</AcNo>

<Currency>01</Currency>

<SubAcNo></SubAcNo>

<StartDate>2014-11-11</StartDate>

<EndDate>2014-09-11</EndDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashRecPaySum" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-14 09:27:58</dtServer>

<userKey>N</userKey>

65 / 81

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<Sumlist>

<Map>

<acNo>*********</acNo>

<currency>01</currency>

<acName>董新瓒版本 01</acName>

<acNoAlias></acNoAlias>

<inOutType>收支类</inOutType>

<dAmt>199683.30</dAmt>

<dTranNum>17.00</dTranNum>

<cAmt>8737197.63</cAmt>

<cTranNum>2.00</cTranNum>

<tran1Amt>1516032.62</tran1Amt>

<tran1TranNum>4.00</tran1TranNum>

<tran2Amt>10017879774.10</tran2Amt>

<tran2TranNum>4.00</tran2TranNum>

<tran3Amt>2500000.00</tran3Amt>

<tran3TranNum>2.00</tran3TranNum>

<tran4Amt>10007935563.76</tran4Amt>

<tran4TranNum>6.00</tran4TranNum>

</Map>

<Map>

<acNo>*********</acNo>

<currency>01</currency>

<acName>董新瓒版本 02</acName>

<acNoAlias></acNoAlias>

<inOutType>收支类</inOutType>

<dAmt>833.33</dAmt>

<dTranNum>1.00</dTranNum>

<cAmt>119386.94</cAmt>

<cTranNum>3.00</cTranNum>

<tran1Amt>0.00</tran1Amt>

<tran1TranNum>0.00</tran1TranNum>

<tran2Amt>1516032.62</tran2Amt>

<tran2TranNum>4.00</tran2TranNum>

民生银行银企直联

66 / 81

<tran3Amt>0.00</tran3Amt>

<tran3TranNum>0.00</tran3TranNum>

<tran4Amt>2500000.00</tran4Amt>

<tran4TranNum>2.00</tran4TranNum>

民生银行银企直联

</Map>

</Sumlist>

</xDataBody>

</CMBC>

15.现金池明细对应子公司入账信息查询(CashPoolSub

UppAmtQry)

本部分更新日期:2021-04-02

1.上级账号需要加挂网银

2.被查询账号是上级账号的下级

3.签约账户归集关系中，上级账号需要有被查询账号的查询权限

15.1.请求(CashPoolTransDetailQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回（★）

64

 <uppAcNo>

查询账号的上级账号（★）

 <acNo>

查询账号（★）

 <startDate>

起始日期（★）

 <endDate>

结束日期（★）

 <currentIndex> 起始位置（★）

 <pageSize>

查询笔数（★）

67 / 81

</xDataBody>

15.2.响应(CashPoolTransDetailQry)

民生银行银企直联

 标记

说明

长度

<xDataBody>

服务消息集

客户端交易的唯一标志（★）

总笔数（★）

 <trnId>

 <allNum>

 <List>

  <Map>

   <transDate>

   <transTime>

交易日期

交易时间

   <serialNo>

交易流水号

   <payAmt>

   <rcvAmt>

收入

支出

   <supDownAmt>

上级下拨本级

   <uppAmt>

本级上存上级

   <subUppAmt>

下级上存本级

   <downAmt>

本级下拨下级

   <creditAmt>

本级透支金额

   <returnAmt>

本级归还透支

   <lendAmt>

借方发生额

64

64

32

1

32

68 / 81

   <loanAmt>

贷方发生额

   <balance>

   <uppBal>

   <selfBal>

账面余额

上存金额

自身余额

   <payeeAcNo>

收款账户

   <payerAcNo>

付款账户

   <certNo>

   <remark>

凭证号

摘要

   <transType>

交易类型：

1–自身收付款

2–内部资金划拨

3–结息

4–收费

5–虚户间调账

6–虚户间转账

7–其它

   <subAbstracts>

子公司入账明细摘要

   <subOppaccountno>

子公司入账对方账号

   <subOppaccountname> 子公司入账对方账号名称

   <subOppbranch>

子公司入账对方开户行

   <subTranstime>

子公司入账交易时间戳

  </xDataBody>

民生银行银企直联

69 / 81

民生银行银企直联

15.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CashPoolSubUppAmtQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>rtrtrtrrtrtr555</trnId>

<uppAcNo>*********</uppAcNo>

<acNo>*********</acNo>

<startDate>2020-12-23</startDate>

<endDate>2020-12-23</endDate>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CashPoolSubUppAmtQry" security="none" lang="chs"

header="100" version="100" >

<responseHeader>

<status>

<code>

0

</code>

<severity>

info

70 / 81

</severity>

<message>

ok

</message>

</status>

<dtServer>

2020-12-24 14:11:00

</dtServer>

<userKey>

N

</userKey>

<dtDead>

</dtDead>

<language>

chs

</language>

</responseHeader>

<xDataBody>

<trnId>

rtrtrtrrtrtr555

</trnId>

<allNum>

12

</allNum>

<List>

<Map>

<transDate>

20201223

</transDate>

<transTime>

102731

</transTime>

<serialNo>

50384863

</serialNo>

<payAmt>

300.00

</payAmt>

民生银行银企直联

71 / 81

<rcvAmt>

</rcvAmt>

<supDownAmt>

</supDownAmt>

<uppAmt>

</uppAmt>

<subUppAmt>

</subUppAmt>

<downAmt>

</downAmt>

<creditAmt>

0.00

</creditAmt>

<returnAmt>

0.00

</returnAmt>

<lendAmt>

0.00

</lendAmt>

<loanAmt>

300.00

</loanAmt>

<balance>

300.00

</balance>

<uppBal>

-310.00

</uppBal>

<selfBal>

600.00

</selfBal>

<payeeAcNo>

*********

</payeeAcNo>

<payerAcNo>

*********

</payerAcNo>

<certNo>

</certNo>

民生银行银企直联

72 / 81

民生银行银企直联

<remark>

跨级划拨

</remark>

<transType>

1

</transType>

<subAbstracts>

</subAbstracts>

<subOppaccountno>

</subOppaccountno>

<subOppaccountname>

</subOppaccountname>

<subOppbranch>

</subOppbranch>

<subTranstime>

</subTranstime>

</Map>

</List>

</xDataBody>

</CMBC>

16.银企直联现金池账户资金信息查询(XJCActFincDet

ail)

本部分更新日期:2021-04-02

主要提供现金池的归集账户资金信息查询功能。

上级账号需要加挂网银

被查询账号是上级账号的下级

签约账户归集关系中，上级账号需要有被查询账号的查询权限

16.1.请求(XJCActFincDetail)

  标记

说明

<xDataBody>

长度

73 / 81

民生银行银企直联

 <uppAcno>

上级账号（★）

 <acno>

查询账户（★）

 <curCode>

币种（★）

 <beginDate> 起始日期(格式：YYYYMMDD)；当查询标志为 2 时必输

 <queryFlag> 查询类型（★）：1-当前日期查询；2-历史查询

</xDataBody>

16.2.响应(XJCActFincDetail)

  标记

说明

<xDataBody>

 <List>

  <Map>

   <acno>

   <curCode>

   <acname>

   <acnoAlias>

账号

币种

户名

别名

   <acnoOrder>

显示顺序

   <inoutType>

收支类型： 0-收入类 1-收支类 2-支出类

   <acnoLevel>

关系级别

   <uppAcno>

上级账号

35

35

2

8

1

长

度

35

2

70

70

30

2

1

35

74 / 81

   <uppCurCode> 上级币种

   <uppAcname> 上级户名

   <topAcno>

最高级账号

   <topCurCode> 最高级币种

   <bal>

余额(queryFlag=1 是显示)

   <selfBal>

自身余额(queryFlag=1 是显示)

   <uppAmt>

上存金额

   <uppAccrual>

上存利息

   <overAmt>

透支金额

   <overAccrual> 透支利息

民生银行银企直联

2

70

35

2

18,2

18,2

18,2

18,2

18,2

18,2

   <opFlag>

操作权限： 0-直接上级 1-最顶级 2-直接上级和最顶

1

级 3-所有上级 4-本级

   <qryFlag>

查询权限： 0-直接上级 1-最顶级 2-直接上级和最顶

1

级 3-所有上级

  <Map>

 <List>

</xDataBody>

16.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="XJCActFincDetail">

<requestHeader>

75 / 81

<dtClient>2008-03-13 18:44:12</dtClient>

民生银行银企直联

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<uppAcno>*********</uppAcno>

<acno>*********</acno>

<curCode>01</curCode>

<beginDate>20141111</beginDate>

<queryFlag>1</queryFlag>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="XJCActFincDetail" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-11-24 10:59:51</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<acno>*********</acno>

<curCode>01</curCode>

76 / 81

<acname>董新瓒版本 01</acname>

<acnoAlias></acnoAlias>

<acnoOrder></acnoOrder>

<inoutType>1</inoutType>

<acnoLevel>3</acnoLevel>

<uppAcno>*********</uppAcno>

<uppCurCode>01</uppCurCode>

<uppAcname>董新瓒版本 01</uppAcname>

<topAcno>*********</topAcno>

<topCurCode>01</topCurCode>

<bal>1608336.62</bal>

<selfBal>11552546.96</selfBal>

<selfUseBal>11552546.96</selfUseBal>

<uppAmt>9944210.34</uppAmt>

<uppAccrual>55245.61</uppAccrual>

<overAmt>0.00</overAmt>

<overAccrual>0.00</overAccrual>

<opFlag>3</opFlag>

<qryFlag>3</qryFlag>

</Map>

<Map>

<acno>*********</acno>

<curCode>01</curCode>

<acname>董新瓒版本 01</acname>

<acnoAlias></acnoAlias>

<acnoOrder></acnoOrder>

<inoutType>1</inoutType>

<acnoLevel>2</acnoLevel>

<uppAcno>*********</uppAcno>

<uppCurCode>01</uppCurCode>

<uppAcname>董新瓒版本 02</uppAcname>

<topAcno>*********</topAcno>

<topCurCode>01</topCurCode>

<bal>10025697150.39</bal>

<selfBal>10014768995.67</selfBal>

<selfUseBal>0.00</selfUseBal>

<uppAmt>0.00</uppAmt>

<uppAccrual>0.00</uppAccrual>

民生银行银企直联

77 / 81

民生银行银企直联

<overAmt>983944.38</overAmt>

<overAccrual>5466.36</overAccrual>

<opFlag>3</opFlag>

<qryFlag>3</qryFlag>

</Map>

</List>

</xDataBody>

</CMBC>

17.现金池上存下拨交易状态查询(B2ECashPoolQuer

yCollectAllocateStat)

本部分更新日期:2022-09-09

通过交易流水查询交易的状态。

17.1.请求(B2ECashPoolQueryCollectAllocateStat)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标识（★）

 <insId>

客户端产生的交易唯一标识（★）

 <serialNo>

原交易流水号（★）

</xDataBody>

长度

64

32

17.2.响应(B2ECashPoolQueryCollectAllocateStat)

  标记

说明

长度

<xDataBody> 服务消息集

 <stat>

交易状态:

78 / 81

民生银行银企直联

A-交易成功

B-等待柜面审批

C-柜面审批拒绝

D-结果未知，待查询

E-交易失败

 <serialNo>

原交易流水号

 <errorInfo> 错误信息

</xDataBody>

17.3.例子

请求字段：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ECashPoolQueryCollectAllocateStat">

<requestHeader>

<dtClient>2022-09-02 15:12:50</dtClient>

<clientId>2300459516</clientId>

<userId>2300459516001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202209021512501</trnId>

<insId>CMBCINS202209021512502</insId>

<serialNo>CMBCINS202209021130512</serialNo>

</xDataBody>

</CMBC>

返回字段：

79 / 81

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ECashPoolQueryCollectAllocateStat">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-09-09 09:50:04</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<errorInfo></errorInfo>

<stat>D</stat>

<serialNo>CMBCINS202209021130512</serialNo>

</xDataBody>

</CMBC>

80 / 81

