银企直联接口文档

（法人账户透支）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 20

目录

民生银行银企直联

目录 .............................................................................................2

1. 额度信息查询(CORPCREDITLIMITQRY) ............................................. 3

1.1. 请求(CORPCREDITLIMITQRY) ........................................................3

1.2. 响应(CORPCREDITLIMITQRY) ........................................................3

1.3. 例子 ...................................................................................... 5

2. 借据信息查询(CORPCREDITINFOQRY) .............................................. 6

2.1. 请求(CORPCREDITINFOQRY) ........................................................ 7

2.2. 响应(CORPCREDITINFOQRY) ........................................................ 7

2.3. 例子 ...................................................................................... 9

3. 利率信息查询(CORPINTERESTINFOQRY) ......................................... 11

3.1. 请求(CORPINTERESTINFOQRY) .................................................... 11

3.2. 响应(CORPINTERESTINFOQRY) .................................................... 11

3.3. 例子 .................................................................................... 12

4. 透支还款(CORPREPAYSUBMIT) ..................................................... 14

4.1. 请求(CORPREPAYSUBMIT) ......................................................... 14

4.2. 响应(CORPREPAYSUBMIT) ......................................................... 14

4.3. 例子 .................................................................................... 15

5. 透支转账汇款(CORPCREDITTRANSFER) .......................................... 16

5.1. 请求(CORPCREDITTRANSFER) ..................................................... 16

5.2. 响应(CORPCREDITTRANSFER) ..................................................... 17

5.3. 例子 .................................................................................... 17

2 / 20

民生银行银企直联

1.额度信息查询(CorpCreditLimitQry)

本部分更新日期:2022-10-20

当客户不输入额度编号时，返回可发起透支的额度列表（已过期的不展示），当客户输入
额度编号时，返回该额度编号的额度信息（可查询历史额度）。

1.1.请求(CorpCreditLimitQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <cltcookie> 可选，客户端 cookie，响应时原值返回

长度

64

 <insId>

指令 ID，一条转帐指令在客户端的唯一标识（★）

64

 <code>

额度编码

非必输

</xDataBody>

1.2.响应(CorpCreditLimitQry)

  标记

说明

长

度

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <cltcookie>

如果客户端发送 cookie，同步的历史记录不包

12

括原有的 cltcookie（★）

 <svrId>

服务器该笔交易的标识（★）

32

3 / 20

 <insId>

指令 ID ， 请求时给出的 ID（★）

 <custAccountNo>

签约账号

 <code>

额度编号（用于发起透支）

 <protocolNo>

风控出账申请书号

 <validFrom>

额度生效日期

 <validTo>

额度到期日期

 <isCycle>

是否可循环，

0-否

1-是

 <perOverdrawTime>

逐笔透支期限

 <amount>

透支额度

 <currency>

币种

 <unUsedAmt>

可用余额

 <basicRateType>

基准利率类型

0-固定利率

1-浮动利率（1 年 1 期 LPR）

 <fixedRate>

固定利率(%)

 <floatPercentage>

加减基点（BP）

 <advancePayFlag>

允许提前还款标志,

0-是

1-否

 <autAdvanceRepayFlag> 自动提前还款标志,

0-是

民生银行银企直联

64

50

40

20

10

10

2

50

20

20

20

2

50

50

2

2

4 / 20

1-否

民生银行银企直联

</xDataBody>

1.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpCreditLimitQry">

<requestHeader>

<dtClient>2022-07-19 17:44:12</dtClient>

<clientId>2300279714</clientId>

<userId>2300279714001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

响应报文

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpCreditLimitQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-08-16 18:40:58</dtServer>

<userKey>N</userKey>

5 / 20

民生银行银企直联

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

<List>

<item>

<custAccountNo>61*****10</custAccountNo>

<code>99**************</code>

<protocolNo>sc20211029113608b1b3</protocolNo>

<validFrom>2021-10-29</validFrom>

<validTo>2022-09-22</validTo>

<isCycle>1</isCycle>

<perOverdrawTime>90</perOverdrawTime>

<amount>1004000.00</amount>

<unUsedAmt>1001399.00</unUsedAmt>

<basicRateType>0</basicRateType>

<fixedRate>5.0000%</fixedRate>

<floatFlag>0</floatFlag>

<advancePayFlag>0</advancePayFlag>

<autAdvanceRepayFlag>1</autAdvanceRepayFlag>

<currency>RMB</currency>

</item>

</List>

</xDataBody>

</CMBC>

2.借据信息查询(CorpCreditInfoQry)

本部分更新日期:2022-10-21

根据客户号、查询范围起息日开始日到起息日截止日区间的借据，跨度最长不超过 1 年；
如果输入贷款借据号，则查询起息区间范围该笔借据号的信息。

6 / 20

2.1.请求(CorpCreditInfoQry)

民生银行银企直联

长度

64

64

16

10

10

50

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易流水

 <insId>

客户端流水号唯一（★）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <creditCode>

贷款借据号

 <applyStartDate> 起息日开始日（★）

 <applyEndDate> 起息日截止日

 <payeeAcNo>

收款账号

 <curPage>

当前页数

 <pageLine>

每页条数

</xDataBody>

2.2.响应(CorpCreditInfoQry)

  标记

说明

长度

<xDataBody>

 <sumPage>

 <sumLine>

总页数

总条数

 <curPage>

当前页数

7 / 20

 <pageLine>

每页条数

 <loanApplyInfoList>

融资申请信息列表

   <creditCode>

贷款借据号

   <vaildFrom>

起息日

   <currency>

币种

   <validTo>

到期日

   <financingAmount> 贷款金额

   <creditBalance>

当前余额

   <loanStatus>

借据状态:

01-结清

02-未结清

03-逾期

   <applyStatus>

审批状态：

民生银行银企直联

16

10

10

10

16，2

16，2

2

1

S-审批通过,

E-审批拒绝,

R-审批中，

C-协议待签署

落地审批的借据有返回值，其他模式不返回该

值

   <creditClearDate>

结清日期

10

 <loanApplyInfoList>

流水号

</xDataBody>

8 / 20

民生银行银企直联

2.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpCreditInfoQry">

<requestHeader>

<dtClient>2022-08-18 9:44:12</dtClient>

<clientId>23******08</clientId>

<userId>23******08**1</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<curPage>1</curPage>

<pageLine>10</pageLine>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

响应报文

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpCreditInfoQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-08-18 11:15:21</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

9 / 20

<xDataBody>

<loanApplyInfoList>

<item>

<creditCode>ZX22050000*****8</creditCode>

<vaildFrom>2022-05-31</vaildFrom>

<currency>RMB</currency>

<validTo>2022-11-27</validTo>

<financingAmount>5000.0</financingAmount>

<creditBalance>5000.0</creditBalance>

<loanStatus>02</loanStatus>

<applyStatus>S</applyStatus>

<creditClearDate/>

</item>

<item>

<creditCode>ZX22050000*****1</creditCode>

<vaildFrom>2022-05-30</vaildFrom>

<currency>RMB</currency>

<validTo>2022-11-26</validTo>

<financingAmount>10000.0</financingAmount>

<creditBalance>0.0</creditBalance>

<loanStatus>01</loanStatus>

<applyStatus>S</applyStatus>

<creditClearDate>2022-06-11</creditClearDate>

</item>

</loanApplyInfoList>

<sumPage>1</sumPage>

<curPage>1</curPage>

<pageLine>10</pageLine>

<sumLine>2</sumLine>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

民生银行银企直联

10 / 20

3.利率信息查询(CorpInterestInfoQry)

民生银行银企直联

本部分更新日期:2022-10-20

根据贷款借据号查询利率信息。

3.1.请求(CorpInterestInfoQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转帐指令在客户端的唯一标识（★）

 <creditCode> 贷款借据号

</xDataBody>

3.2.响应(CorpInterestInfoQry)

长度

64

64

16

  标记

说明

长度

<xDataBody>

 <rttyTyp>

基准利率类型

 <sprd>

加减基点（BP），浮动利率返回该值

 <baseRate>

固定利率,固定利率时返回改值

 <interest>

应付利息

 <pricingRateInfoList>

智能定价价格列表

11 / 20

民生银行银企直联

10

10

10

   <bizDate>

定价日期

   <finalExecuteRate> 利率（%）

   <creditBalance>

未还本金额

 </pricingRateInfoList>

</xDataBody>

3.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpInterestInfoQry">

<requestHeader>

<dtClient>2022-08-18 14:49:00</dtClient>

<clientId>23******08</clientId>

<userId>23******08001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<creditCode>ZX22060000*****3</creditCode>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

响应报文

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpInterestInfoQry">

<responseHeader>

<status>

12 / 20

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-08-19 10:44:57</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

<xDataBody>

<pricingRateInfoList>

<item>

<bizDate>2022-08-18</bizDate>

<finalExecuteRate>55.000000</finalExecuteRate>

</item>

<item>

<bizDate>2022-08-17</bizDate>

<finalExecuteRate>55.000000</finalExecuteRate>

</item>

<item>

<bizDate>2022-08-16</bizDate>

<finalExecuteRate>55.000000</finalExecuteRate>

</item>

</pricingRateInfoList>

<baseRate>55.0</baseRate>

<interest>214.34</interest>

<rttyTyp>4</rttyTyp>

<sprd>0.0</sprd>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

民生银行银企直联

13 / 20

4.透支还款(CorpRepaySubmit)

民生银行银企直联

本部分更新日期:2023-06-07

4.1.请求(CorpRepaySubmit)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <insId>

客户端流水号唯一（★）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <creditCode>

贷款借据号

 <accountNo>

还款账号

 <refundAmount> 偿还本金金额

</xDataBody>

4.2.响应(CorpRepaySubmit)

长度

64

64

16

  标记

说明

长度

<xDataBody>

服务消息集

 <refundAmount> 本金还款金额

 <interest>

利息还款金额

 <failReason>

失败原因

32

</xDataBody>

14 / 20

民生银行银企直联

4.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpRepaySubmit">

<requestHeader>

<dtClient>2022-08-19 15:46:00</dtClient>

<clientId>23******08</clientId>

<userId>23******08001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

<requestHeader>

<xDataBody>

<accountNo>61*****90</accountNo>

<creditCode>****************</creditCode>

<refundAmount>9901.10</refundAmount>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

响应报文

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpRepaySubmit">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功 message>

<status>

<dtServer>2022-08-19 18:07:54</dtServer>

<userKey>N</userKey>

<dtDead/>

15 / 20

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<interest>12.10</interest>

<refundAmount>9901.10</refundAmount>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

5.透支转账汇款(CorpCreditTransfer)

本部分更新日期:2023-05-26

5.1.请求(CorpCreditTransfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易流水

 <insId>

客户端流水号唯一（★）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <signCode>

签约账号

 <code>

额度编号

 <payChannel>

汇路: 0002：本行 1225：大额 1238：小

额 1216：网银互联

 <payeeAcNo>

收款账号

 <payeeAcName>

收款账户名称

长度

64

64

32

40

50

50

200

16 / 20

民生银行银企直联

 <payeeBankNo>

开户行行号

 <paymentAmount> 总支付金额

 <selffund>

自有资金支付金额

 <purpose>

用途（★） 送值范围： 01-往来结算款， 02-合同

款， 03-运费， 04-工程款， 99-其他

 <remark>

备注（用途为其他时，不能为空）

</xDataBody>

5.2.响应(CorpCreditTransfer)

  标记

说明

<xDataBody>

 <trnId>

原值返回

 <svrId>

服务器流水

 <insId>

流水号

</xDataBody>

5.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpCreditTransfer">

<requestHeader>

<dtClient>2022-08-16 14:49:00</dtClient>

50

16，

2

16，

2

50

60

长度

64

32

64

17 / 20

民生银行银企直联

<clientId>230*****08</clientId>

<userId>230******8001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</xDataBody>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

<signCode>61******90</signCode>

<code>9922******4568</code>

<payChannel>1216</payChannel>

<payeeAcNo>111111</payeeAcNo>

<payeeAcName>收款账户名名称</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<paymentAmount>10001</paymentAmount>

<selffund>99.9</selffund>

<purpose>99</purpose>

<remark>备注信息</remark>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CorpCreditTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2022-08-16 17:12:43</dtServer>

<userKey>N</userKey>

<dtDead/>

<language>chs</language>

</responseHeader>

18 / 20

民生银行银企直联

<xDataBody>

<trnId>123456</trnId>

<insId>20220825000000000001</insId>

<cltcookie>123456</cltcookie>

</xDataBody>

</CMBC>

19 / 20

