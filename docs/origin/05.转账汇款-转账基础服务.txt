银企直联接口文档

（转账汇款-转账基础服务）

邮箱：<EMAIL>

版本

日期

说明

文档修改记录

V1.0.0 2021-03-18

定义接口文档

V1.1

2024-07-26

批量费用报销、代发工资

(batchCostReimbNew)接口，

<payModel>付款类型，支持一借多贷

V1.2

2024-09-24

批量费用报销、代发工资

(batchCostReimbNew)<Usage>字

段逻辑更新。

V1.3

2024-11-18

batchCostReimbNew、

BatchTransferCostReimb 接口新增

代发工资用途代码。

Xfer 新增说明：如果向备付金机构转

账，请使用 ReserveTransfer 接口。

删除 batchCostReimb 批量费用报销

接口。

V2.0

2025-03-26

1.付款接口，对私付款费用报销用途对

应中文释义修改，与企业网银一致。

2.调整接口文档展示格式。

V2.1

2025-04-10

1.batchXfer 接口修改请求支付明细数

据说明，支持网银互联行号

2.备付金转账 (ReserveTransfer)增加

交易结果查询说明，qryXfer 接口支持

备付金转账交易结果查询。

民生银行银企直联

编写

审核者

者

1 / 149

文档说明：（必读）

1.请 求 报 文 头 需 上 送 客 户 号 （ clientId ） 、 操 作 员

（userId）、银企直联密码（userPswd）（开通银企

直联后邮件或短信下发的银企交易密码，非 Ukey 密

码，如有疑问请联系客户经理）。其他 XML 报文说明

请参照接口文档：01.银企接口文档公共说明

2.XML 格式字段：接口字段名称。

3.字段说明：字段中文释义

4.是否必输：是否为必输字段：Y（是） / N（不是）

5.长度：

汉字长度：使用字符数（例如“最多小于等于 20 个汉

字”，即：“20”）来限制。

数字长度：使用位数描述整数部分和小数部分的长度限

制（例如“最多 10 位数字，其中 2 位小

数”）即：10,2。

目录

文档说明：（必读） ............................................................................. 0

目录 .............................................................................................0

转账基础服务 .................................................................................. 1

1． 公转公 ..................................................................................................... 1

2． 公转私-费用报销 ....................................................................................... 2

3． 公转私-代发工资 ....................................................................................... 3

1. 单笔转账(XFER) ............................................................................ 4

1.1. 请求(XFER) ............................................................................. 4

1.2. 响应(XFER) ............................................................................. 7

1.3. 资常用用途代码及其含义 .............................................................. 8

1.4. 报文示例 ................................................................................. 9

2. 制单转账交易(TRANSFERXFER) ......................................................11

2.1. 请求(TRANSFERXFER) .............................................................. 12

2.2. 费用报销、代发工资常用用途代码及其含义： .................................... 13

2.3. 响应(TRANSFERXFER) .............................................................. 15

2.4. 报文示例 ............................................................................... 15

3. 总公司以子公司名义付款交易(TOPXFER) .............................................17

3.1. 请求(TOPXFER) .......................................................................18

3.2. 费用报销、代发工资常用用途代码及其含义： .................................... 20

3.3. 响应(TOPXFER) .......................................................................21

3.4. 例子 .................................................................................... 22

4. 备付金转账 (RESERVETRANSFER) ..................................................24

4.1. 请求(RESERVETRANSFER) .......................................................... 24

4.2. 响应(RESERVETRANSFER) .......................................................... 25

4.3. 报文示例 ............................................................................... 26

民生银行银企直联
5. 单笔转账交易结果查询(QRYXFER) .................................................... 27

5.1. 请求(QRYXFER) ...................................................................... 28

5.2. 响应(QRYXFER) ...................................................................... 28

5.3. 报文示例 ............................................................................... 30

6. 批量付款(BATCHXFER) .................................................................34

6.1. 请求(BATCHXFER) ................................................................... 35

6.2. 响应(BATCHXFER) ................................................................... 36

6.3. 支付文件数据格式<FILECONTENT>： .............................................36

6.4. 常用用途代码及其含义 ............................................................... 38

6.5. 报文示例 ............................................................................... 40

7. 批量转账制单(BATCHTRANSFERXFER) ........................................... 41

7.1. 请求(BATCHTRANSFERXFER) ...................................................... 42

7.2. 响应(BATCHTRANSFERXFER) ...................................................... 43

7.3. 支付文件数据格式<FILECONTENT>： .............................................43

7.4. 常用用途代码及其含义 ............................................................... 45

7.5. 报文示例 ............................................................................... 46

8. 批量费用报销、代发工资 (BATCHCOSTREIMBNEW) ............................ 48

8.1. 请求(BATCHCOSTREIMBNEW) ..................................................... 49

8.2. 响应(BATCHCOSTREIMBNEW) ..................................................... 52

8.3. 支付文件数据格式<FILECONTENT>： .............................................52

8.4. 常用用途代码及其含义 ............................................................... 55

8.5. 报文示例 ............................................................................... 61

9. 批量制单费用报销、代发工资(BATCHTRANSFERCOSTREIMB) ............... 64

9.1. 请求(BATCHTRANSFERCOSTREIMB) .............................................. 65

9.2. 响应(BATCHTRANSFERCOSTREIMB) .............................................. 66

1 / 149

民生银行银企直联
9.3. 本行代发工资、费用报销数据格式<FILECONTENT>： ......................... 66

9.4. 跨行代发工资、费用报销数据格式<FILECONTENT>： .......................... 66

9.5. 常用用途代码及其含义 ............................................................... 67

9.6. 报文示例 ............................................................................... 73

10. 批量付款对账(QRYBATCHXFER) ................................................... 76

10.1. 请求(QRYBATCHXFER) ............................................................ 76

10.2. 响应(QRYBATCHXFER) ............................................................ 76

10.3. 报文示例 ............................................................................. 77

11. 批量费用报销、代发工资查询(QRYBATCHCOSTREIMBNEW) ................79

11.1. 请求(QRYBATCHCOSTREIMBNEW) .............................................. 79

11.2. 响应(QRYBATCHCOSTREIMBNEW) .............................................. 81

加解密流程说明 .............................................................................................82

支付文件数据格式<fileContent>： ...............................................................82

11.3. 报文示例 ............................................................................. 83

12. 汇款交易结果明细查询(QRYREMITTDETAIL) .....................................85

12.1. 请求(QRYREMITTDETAIL) ......................................................... 85

12.2. 响应(QRYREMITTDETAIL) ......................................................... 86

12.3. 报文示例 ............................................................................. 88

13. 通过行名查询行号（单笔）(B2EBANKNOSINGLEQRY) ........................98

13.1. 请求(B2EBANKNOSINGLEQRY) ................................................. 98

13.2. 响应(B2EBANKNOSINGLEQRY) ................................................. 99

13.3. 报文示例 ............................................................................. 99

14. 通过行名查询行号(批量)(B2EBATCHBANKNO) ................................102

14.1. 请求(B2EBATCHBANKNO) ..................................................... 102

14.2. 响应(B2EBATCHBANKNO) ..................................................... 102

2 / 149

民生银行银企直联
14.3. 报文示例 ........................................................................... 103

15. 获取行名查询行号结果（批量）(B2EQRYBATCHBANKNO) ................. 104

15.1. 请求(B2EQRYBATCHBANKNO) ................................................ 105

15.2. 响应(B2EQRYBATCHBANKNO) ................................................ 105

15.3. 报文示例 ........................................................................... 106

16. 转账可达汇路查询(单笔)(B2EABLEROUTESINGLEQRY) .................... 110

16.1. 请求(B2EABLEROUTESINGLEQRY) ............................................111

16.2. 响应(B2EABLEROUTESINGLEQRY) ............................................112

16.3. 报文示例 ........................................................................... 113

17. 转账可达汇路查询(批量)(B2EBATCHABLEROUTE) ........................... 116

17.1. 请求(B2EBATCHABLEROUTE) ..................................................117

17.2. 响应(B2EBATCHABLEROUTE) ..................................................118

17.3. 报文示例 ........................................................................... 118

18. 获取转账可达汇路查询结果(批量)(B2EQRYBATCHROUTE) ................. 120

18.1. 请求(B2EQRYBATCHROUTE) ...................................................121

18.2. 响应(B2EQRYBATCHROUTE) ...................................................121

18.3. 报文示例 ........................................................................... 123

19. 借记卡有效性校验 (QRYACCTSTATUS) ......................................... 126

19.1. 请求(QRYACCTSTATUS) .........................................................126

19.2. 响应(QRYACCTSTATUS) .........................................................127

19.3. 报文示例 ........................................................................... 127

20. 行名行号信息查询(QRYALLBANKCODE) ........................................129

20.1. 请求(QRYALLBANKCODE) ...................................................... 129

20.2. 响应(QRYALLBANKCODE) ...................................................... 130

20.3. 报文示例 ........................................................................... 131

3 / 149

民生银行银企直联
21. 单笔转账(支持行号匹配)(SINGLEXFER) ..........................................133

21.1. 请求(SINGLEXFER) ...............................................................133

21.2. 响应(SINGLEXFER) ...............................................................135

21.3. 报文示例 ........................................................................... 135

22. 银企直联公钥查询(QUERYPUBLICKEY) ......................................... 138

22.1. 请求(QUERYPUBLICKEY) ........................................................ 138

22.2. 响应(QUERYPUBLICKEY) ........................................................ 138

22.3. 例子 .................................................................................139

4 / 149

民生银行银企直联

转账基础服务

常用转账场景使用说明

术语释义：

制单：是指银企直联的转账交易仅为制单动作，客户需要在企业网银上复核之后才能

转出。

模式：在批量转账中，一借一贷是指每笔转出均从付款账户单独扣款，如一个批次包

括 100 笔交易，付款账户的交易明细中详细列举 100 笔转出明细，付款回单也为 100

张，每张回单均展示收、付方信息。一借多贷是值一个批次汇总后仅从付款账户扣一笔

款。如一个批次包括 100 笔交易，付款账户的 交易明细中仅展示 1 笔转出，付款回单为

1 张，回单不显示收方信息。

本他行：是指本接口支持向本行转账，还是向他行转账，或者均支持。

1．公转公

【单笔】

场景

可使用接口名 本他行 最大支

对应的转账查询接口

单笔转账

Xfer

均支持 1

qryXfer

单笔制单转账

TransferXfer 均支持 1

qryXfer

持笔数

总公司以子公司名义付款交易 topXfer

均支持 1

qryXfer

【批量】

场景

可使用接口名

本他行

最大支持笔数 模式

对应的转账结果
查询

批量转账

batchXfer

均支持

5000

一借一贷 qryBatchXfer

批量制单转账 batchTransferX
fer

均支持

5000

一借一贷 qryBatchXfer

1 / 149

民生银行银企直联

2．公转私-费用报销

【单笔】

场景

可使用接口名 本他

单笔费用报销

Xfer

行

本他
行

单笔制单费用报销

TransferXfer 均支

总公司以子公司名义付款
交易

topXfer

持

均支
持

最大支持笔
数

对应的转账查询接
口

1

1

1

qryXfer

qryXfer

qryXfer

【批量】

场景

可使用的接口名

本他行 最大支持的

笔数

批量代
发工资

batchCostReimb 均支持 本行：
10000
他行：5000

批量代
发工资

batchCostReimb
New

均支持 均为 5000

批量制
单代发
工资

BatchTransferCo
stReimb

均支持 本行：

10000
他行：5000

对应的转账结果查询接
口

qryBatchCostReimb

qryBatchCostReimb
New

qryBatchCostReimb

模
式

一
借
多
贷

一
借
一
贷
，
一
借
多
贷

一
借
多
贷

2 / 149

3．公转私-代发工资

【单笔】

民生银行银企直联

场景

可使用的接口名 本他行

最大支持
的笔数

对应的转账结果查询接
口

单笔代发工资

单笔制单代发工资

总公司以子公司名义
付款交易

Xfer
TransferXfer 他行

本他行

topXfer

均支持

1

1

1

qryXfer

qryXfer

qryXfer

【批量】

场景

可使用的接口名 本他行 最大支持的

模式

对应的转账结

笔数

果查询接口

批量代发工

batchCostReim

均支持 本行 10000

一借多贷 qryBatchCost

资

b

他行：5000

Reimb

批量代发工

batchCostReim

均支持 5000

一借一贷

qryBatchCost

资

bNew

一借多贷

ReimbNew

批量制单代

BatchTransfer

均支持 本行 10000

一借多贷 qryBatchCost

发工资

CostReimb

他行 5000

Reimb

批量制单代

batchTransferX

他行

5000

一借一贷 qryBatchXfer

发工资

fer

3 / 149

民生银行银企直联

1.单笔转账(Xfer)

本部分更新日期:2023-08-11

转账交易，用于企业财务系统向银行提出单笔转账时使用，名称为 Xfer。

接口说明：

1．此接口支持如下交易场景：

公转公（本行/跨行）

公转私-代发工资（本行/跨行）

公转私-费用报销（本行/跨行）

此接口支持操作子公司（授权账户）付款

2．交 易 返 回 WYHL01- 交 易 状 态 未 知 ， 为 网 银 互 联 清 算 渠 道 正 常 返 回 。 请 调 用

qryXfer 接口进行交易结果确认。

3．使用此接口交易后需使用单笔转账交易结果查询（qryXfer）查询此交易的最终状

态。

4．此接口支持操作子公司（授权账户）付款。

5．使用此接口交易后需使用单笔转账交易结果查询（qryXfer）查询此交易的最终状

态。

6．如果向备付金机构转账，请使用 ReserveTransfer 接口。

7．如需大额预转功能请联系分行老师向总行申请开通白名单。

1.1.请求(Xfer)

标记

说明

是否

长度

<xDataBody>

必输

4 / 149

 <trnId>

客户端技术流水号，代表每一次请求的唯一

Y

64

标识，不可重复

民生银行银企直联

 <cltcookie>

客户端 cookie，响应时原值返回

 <insId>

客户业务流水号，代表每一次业务请求的唯

一标识，同一业务请求不可重复

 <acntNo>

付款账号

 <acntName>

付款人名称

 <acntToNo>

收款账号

 <acntToName>

收款人名称

 <externBank>

跨行标识：

0：本行

1：跨行

N

Y

Y

N

Y

Y

Y

64

32

60

32

60

1

 <localFlag>

汇路：

Y

1

1.汇路 0：本行转账，<externBank>需输

0

2.汇路 2：跨行转账-小额支付系统，

<externBank>需输 1

3.汇路 3：跨行转账-大额支付系统，

<externBank>需输 1

4.汇路 5：跨行转账-网银互联，

<externBank>需输 1

5.汇路 9：跨行转账-自动计算汇路，

<externBank>需输 1

注意： 必须提供完整的行名行号；

5 / 149

民生银行银企直联

说明：

1.汇路 2、5 最多支持 100 万元，7*24 小

时运行。

2.汇路 3 金额无上限，工作时间：工作日的

前一日 20:30 至工作日当日的 17:15

3.汇路 9，如 bankCode 字段上送收款行总

行行号，则无法使用大额、小额汇路，仅

会尝试使用网银互联汇路；如 bankCode

字段上送收款行支行行号，则会在大额、

小额、网银互联三个汇路中选中当前时

间、金额可达的最优汇路。

 <rcvCustType>

收款人账户类型：

Y

1

1:对公

2:对私

 <bankCode>

收款人开户行行号

N

12

是否跨行标志<externBank>选择 1 时必

填

 <bankName>

收款人开户行名称

N

80

大/小额、网银互联汇路此项必填

 <bankAddr>

收款人开户行地址(可选)

 <areaCode>

收款行地区编号(可选)

 <amount>

转账金额

 <explain>

1. 公转私，需输入“常用用途代码|备

注”，备注可不输入，用途代码(见下方）

对应转义的中文+备注总汉字长度不得大

N

N

Y

N

15,2

50

6 / 149

于 50，例如：“311” 或者“311|测试”；

2. 公转公，不得包含“|”字符

民生银行银企直联

 <certNo>

企业自制凭证号(数字)

 <actDate>

备用字段

</xDataBody>

1.2.响应(Xfer)

  标记

说明

<xDataBody>

服务消息集

 <transfer>

 <trnId>

客户端交易的唯一标志

 <cltcookie> 原值返回

 <svrId>

银行渠道流水号，网银互联汇路不返回

 <insId>

原值返回

 <balance> 余额

 <route>

小额支付系统：2

大额支付系统：3

网银互联：5

注意： 本字段仅当<localFlag>上送 9 且交易正

常时返回，否则不返回本字段

N

N

8

10

是否

长度

必返

Y

N

N

Y

N

N

32

32

64

15,

2

1

7 / 149

民生银行银企直联

 <transfer>

</xDataBody>

1.3.资常用用途代码及其含义

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

316 报刊费

317 餐费

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

8 / 149

民生银行银企直联

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

394 运费

395 公积金

396 代收货款

代发工资：

4111 工资奖金

1.4.报文示例

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="Xfer">

<requestHeader>

<dtClient>2008-03-20 10:05:33</dtClient>

<clientId>**********</clientId>

<userId>20**********</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

9 / 149

民生银行银企直联

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<cltcookie></cltcookie>

<insId>8053qwe</insId>

<acntNo>****************</acntNo>

<acntName>民生网银集团总公司</acntName>

<acntToNo>****************</acntToNo>

<acntToName>中冶集团财务公司</acntToName>

<externBank>0</externBank>

<localFlag>9</localFlag>

<rcvCustType></rcvCustType>

<bankCode></bankCode>

<bankName>中国民生银行</bankName>

<bankAddr>上海上海</bankAddr>

<areaCode></areaCode>

<amount>1</amount>

<explain>平台付款 fafsd</explain>

<certNo>********</certNo>

<actDate>2008-03-28</actDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="utf-8" security="none" trnCode="Xfer"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:29:10</dtServer>

<userKey>N</userKey>

10 / 149

民生银行银企直联

<dtDead>

</dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>********</trnId>

<svrId>

</svrId>

<insId>8053qwe</insId>

<balance>********.60</balance>

<route>2</route>

</transfer>

</xDataBody>

</CMBC>

2.制单转账交易(TransferXfer)

本部分更新日期:2021-05-08

说明：制单转账交易，用于企业端程序向银行系统提出支付或主动收款时使用，名称为

TransferXfer。交易成功后需企业管理员在企业网银待办中审批通过。

另外，由于转账交易需要保证客户端与服务器间的同步，因此需要定义同步机制。

本接口支持：

公转公（本行）

公转公（跨行）

公转私-费用报销（本行）

公转私-费用报销（跨行）

公转私-代发工资（跨行）

本接口发出的交易的转账结果查询，请使用接口：qryXfer。

11 / 149

2.1.请求(TransferXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标

识

 <acntNo>

付款账号

 <acntName>

付款人名称(可选)

 <acntToNo>

收款账号

 <acntToName> 收款人名称

 <externBank>

是否跨行

 <localFlag>

0:同行；

1：跨行；

汇路：

2:小额;

3 大额：

民生银行银企直联

长度

是否

必输

Y

Y

Y

Y

Y

Y

Y

Y

64

64

32

64

32

64

1

1

5:网银互联 （行内转账为非必输项）

 <rcvCustType> 收款人账户类型：

Y

1

1:对公；

2:对私；

 <bankCode>

收款人开户行行号

Y

12

12 / 149

  标记

说明

民生银行银企直联

长度

是否

必输

 <bankName>

收款人开户行名称

Y

80

 <bankAddr>

收款人开户行地址

 <areaCode>

收款行地区编号(可选)

 <amount>

转账金额

 <explain>

摘要/用途

Y

Y

15，

2

50

跨行公转私，需输入“常用用途代码|备

注”，备注可不输入，用途代码对应

转义的“中文+备注”总汉字长度不

得大于 50，例如：“311” 或者“311|

测试”；

本行公转私只输入常用用途代码；本行对

公、跨行对公时，摘要用途不得包含

“|”字符

 <actDate>

要求的转账日期 YYYY-MM-DD(上海同城

10

汇路是实时转账，此项无用)

</xDataBody>

2.2.费用报销、代发工资常用用途代码及其含义：

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

13 / 149

316 报刊费

317 餐费

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

民生银行银企直联

14 / 149

民生银行银企直联

是否

长度

必返

Y

Y

Y

Y

32

32

64

394 运费

395 公积金

396 代收货款

代发工资：

4111 工资奖金

2.3. 响应(TransferXfer)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志

 <cltcookie>

如果客户端发送 cookie，同步的历史记录不包

括原有的 cltcookie

 <svrId>

服务器该笔交易的标识

 <insId>

指令 ID，请求时给出的 ID

</xDataBody>

2.4.报文示例

请求报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="TransferXfer">

<requestHeader>

15 / 149

民生银行银企直联

<dtClient>2008-03-20 10:05:33</dtClient>

<clientId>**********</clientId>

<userId>20**********</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<cltcookie/>

<insId>8053qwe</insId>

<acntNo>****************</acntNo>

<acntName>民生网银集团总公司</acntName>

<acntToNo>****************</acntToNo>

<acntToName>中冶集团财务公司</acntToName>

<externBank>0</externBank>

<localFlag>1</localFlag>

<rcvCustType/>

<bankCode/>

<bankName>中国民生银行</bankName>

<bankAddr>上海上海</bankAddr>

<areaCode/>

<amount>1</amount>

<explain>平台付款 fafsd</explain>

<actDate>2008-03-28</actDate>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="chs" security="none" trnCode="TransferXfer"

version="100">

<responseHeader>

<status>

<code>0</code>

16 / 149

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:29:10</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>********</trnId>

<svrId></svrId>

<insId>8053qwe</insId>

</transfer>

</xDataBody>

</CMBC>

3.总公司以子公司名义付款交易(topXfer)

本部分更新日期:2021-05-08

说明：在银行与企业协议的可操作账户范围内，企业可直接通过银企直联平台操作其协议

许可范围内账户和已获得其下属公司协议授权的下属公司协议许可范围的账户进行

总公司以分公司名义付款操作。该转账操作可进行如下类型交易：

系统内转账(本行转账)：

<externBank>0</externBank>

<localFlag>0</localFlag>

跨行小额转账：

<externBank>1</externBank>

<localFlag>2</localFlag>

跨行大额转账：

<externBank>1</externBank>

<localFlag>3</localFlag>

17 / 149

对转账限额的控制目前银行端不考虑，由企业进行控制。另外，由于转账交易需要保证客

民生银行银企直联

户端与服务器间的同步，因此需要定义同步机制。

本接口支持的交易场景：

公转公（本行）

公转公（跨行）

公转私-费用报销（跨行）

公转私-代发工资（跨行）

本接口发出的交易的转账结果查询，请使用接口：qryXfer。

3.1.请求(topXfer)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志（必输，但无作

N

64

用）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <topAcntNo>

总公司账号

 <topAcntName>

总公司名称

 <acntNo>

分公司付款账号

 <acntName>

分公司付款人名称

 <acntToNo>

收款账号

N

Y

Y

Y

Y

Y

Y

64

32

60

32

32

32

18 / 149

民生银行银企直联

 <acntToName>

收款人名称

 <externBank>

是否跨行

 <localFlag>

0:同行,

1:跨行

是否异地

0:本地；

1:异地；

2:小额;

3 大额

 <bankCode>

收款人开户行行号

 <bankName>

收款人开户行名称

 <bankAddr>

收款人开户行地址

 <areaCode>

收款行地区编号(可选)

 <amount>

转账金额

 <explain>

跨行公转私，需输入“常用用途代码|备

注”，备注可不输入，用途代码对应转义的

中文+备注总长度不得大于 40，例如：

“311” 或者“311|测试”；本行对公、跨行对

公时，摘要用途不得包含“|”字符

Y

Y

64

1

Y

1

Y

Y

N

N

Y

12

80

12

50

 <certNo>

企业自制凭证号(8 位以内的数字)

N

8

</xDataBody>

19 / 149

3.2.费用报销、代发工资常用用途代码及其含义：

民生银行银企直联

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

316 报刊费

317 餐费

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

20 / 149

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

394 运费

395 公积金

396 代收货款

代发工资：

4111 工资奖金

3.3.响应(topXfer)

  标记

说明

<xDataBody>

服务消息集

 <transfer>

民生银行银企直联

是否

长度

必返

 <trnId>

客户端交易的唯一标志

Y

64

 <cltcookie>

如果客户端发送 cookie，同步的历史记录不包括

原有的 cltcookie

 <svrId>

服务器该笔交易的标识

 <insId>

指令 ID，请求时给出的 ID

Y

Y

Y

32

64

21 / 149

 <balance>

总公司账户余额

民生银行银企直联

15

 </transfer>

</xDataBody>

3.4.例子

请求报文

<?xml version = "1.0" encoding = "utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"
trnCode="topXfer">

<requestHeader>

<dtClient>20020615 10:20:45</dtClient>

<clientId>**********</clientId>

<userId>20**********</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>*********</insId>

<topAcntNo>****************</topAcntNo>

<topAcntName>民生网银集团总公司</topAcntName>

<acntNo>****************</acntNo>

<acntName>guojia</acntName>

<acntToNo>000122223333444400</acntToNo>

<acntToName>XX 集团第一股份公司</acntToName>

<externBank>1</externBank>

<localFlag>3</localFlag>

<bankCode>************</bankCode>

22 / 149

<bankName>兴业银行总行</bankName>

民生银行银企直联

<bankAddr>上海</bankAddr>

<areaCode></areaCode>

<amount>1</amount>

<explain>货款</explain>

<certNo>********</certNo>

</xDataBody>

</CMBC>

响应报文

<?xml version = "1.0" encoding = "utf-8"?>

<CMBC header="100" lang="utf-8" security="none" trnCode="topXfer"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-03 15:19:22</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>1001</trnId>

<svrId></svrId>

<insId>21341q12</insId>

<balance>********.71</balance>

</transfer>

</xDataBody>

</CMBC>

23 / 149

4.备付金转账 (ReserveTransfer)

民生银行银企直联

本部分更新日期:2025-04-10

备付金转账交易，用于企业向支付机构备付金账户转账，支持网联、银联汇路转账。

支付机构的备付金账户的开户行号，一般为 991 开头。

请通过 5.单笔转账交易结果查询(qryXfer)接口获取交易结果。

4.1.请求(ReserveTransfer)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

作用）

客户端产生的交易唯一标志（必输，但无

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

识（★）

指令 ID，一条转账指令在客户端的唯一标

 <acntNo>

付款账号（★）

 <acntName>

付款人名称（★）

 <acntToNo>

收款账号（★）

 <acntToName>

收款人名称（★）

 <localFlag>

汇路，

8：网联，

9：银联，

非必输，银企直联根据客户输入要素查询

是否

长度

必输

Y

N

Y

Y

Y

Y

64

64

32

60

32

60

N

1

24 / 149

可用汇路，如果客户指定汇路，则判断此

汇路是否可以使用

民生银行银企直联

 <bankCode>

收款人开户行行号（★）

 <bankName>

收款人开户行名称（★）

 <amount>

转账金额（★）

 <explain>

摘要、用途。最长 20 字符。

 <certNo>

企业自制凭证号(8 位以内的数字)

</xDataBody>

4.2.响应(ReserveTransfer)

  标记

说明

<xDataBody>

服务消息集

 <transfer>

 <trnId>

客户端交易的唯一标志

 <cltcookie>

如果客户端发送 cookie，同步的历史记录

不包括原有的 cltcookie

 <svrId>

服务器该笔交易的标识

 <insId>

指令 ID，请求时给出的 ID

 <transfer>

</xDataBody>

Y

Y

Y

N

N

12

128

15,2

20

8

是否

长度

必返

Y

Y

Y

Y

32

64

25 / 149

民生银行银企直联

4.3.报文示例

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="ReserveTransfer">

<requestHeader>

<dtClient>2019-11-12 16:39:12</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<cltcookie></cltcookie>

<insId>************</insId>

<acntNo>*********</acntNo>

<acntName>SAP 回归 01</acntName>

<acntToNo>********</acntToNo>

<acntToName>刘平金 123</acntToName>

<localFlag>9</localFlag>

<bankCode>************</bankCode>

<bankName>北京恒信通电信服务有限公司</bankName>

<bankAddr></bankAddr>

<areaCode></areaCode>

<amount>11.11</amount>

<explain>备付金银联测试 002</explain>

<certNo>12345</certNo>

<extent1></extent1>

<extent2></extent2>

<extent3></extent3>

26 / 149

</xDataBody>

</CMBC>

返回报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="utf-8" security="none"
trnCode="ReserveTransfer"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:29:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>********</trnId>

<svrId>3136220000322000176303</svrId>

<insId>***********</insId>

</transfer>

</xDataBody>

</CMBC>

5.单笔转账交易结果查询(qryXfer)

本部分更新日期:2022-10-19

根据流水号（insId）查询一笔转账交易是否成功

民生银行银企直联

27 / 149

请注意，单笔对账交易，若在报文头 code 字段返回 E1602，则表示该笔交易银行

民生银行银企直联

未受理，可以视为交易失败 。

退汇交易，非白名单客户，statusCode 返回状态码为 2，是失败。

5.1.请求(qryXfer)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志（必输，但

Y

64

无作用）

 <cltcookie>

可选，客户端 cookie，响应时原值返

回

N

 <insId>

指令 ID，一条转账指令在客户端的唯一

Y

64

标识

 <svrId>

服务器返回转账消息的标识（非必输） N

32

</xDataBody>

5.2.响应(qryXfer)

  标记

说明

是否

长度

必返

<xDataBody>

服务消息集

28 / 149

 <trnId>

 <insId>

客户端交易的唯一标志（★）

指令 ID，一条转账指令在客户端

的唯一标识（★）

民生银行银企直联

Y

Y

64

64

 <svrId>

服务器对转账消息的标识（★）

Y

32

 <statusId>

 <statusCode>

状态码：

Y

1

0: 原交易成功；

2: 原交易失败；

3: 对账因为网络原因失败，请过

一会再试，原转账交易状态未

知；

4: 原交易处理中

5: 交易成功（已退汇）（仅白名

单客户返回）

6：由于目前为非工作时间，该笔

交易将为您转为下一交易时间的

预约交易。

 <statusSeverity>

当状态码为 0 时，返回 ok；

Y

16

当状态码为 2 时，返回

W6191；

当状态码为 3 时，返回 F

当状态码为 4 时:

所有汇路

10 —— 等待银行审批

12 —— 银行审批通过，等待发送

核心

当汇路是大、小额时

29 / 149

民生银行银企直联

1 —— 已受理

5 —— 冲账

当汇路是网银互联时

PR00 —— 已转发

PR02 —— 已付款

PR06 —— 待处理

PR07 —— 已处理

PR08 —— 已撤销

PR10 —— 已确认

当状态码为 5 时，返回 PS15

 <statusErrMsg>

 </statusId>

描述信息

Y

</xDataBody>

5.3.报文示例

<?xml version = "1.0" encoding = "utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="qryXfer">

<requestHeader>

<dtClient>20020615 10:20:45</dtClient>

<clientId>2200003220</clientId>

<userId>2200003220001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

30 / 149

民生银行银企直联

<xDataBody>

<trnId>1001</trnId>

<insId>00sa01ds01006861</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

</xDataBody>

</CMBC>

1.请求的流水号不存在

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="chs" security="none" trnCode="qryXfer"

version="100">

<responseHeader>

<status>

<code>E1602</code>

<severity>Error</severity>

<message>此流水号不存在,请查证</message>

</status>

<dtServer>2008-09-02 14:05:58</dtServer>

<dtDead></dtDead>

<lanaguge>utf-8</lanaguge>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>46tr456</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

<statusId>

<statusCode></statusCode>

<statusSeverity></statusSeverity>

31 / 149

<statusErrMsg></statusErrMsg>

民生银行银企直联

</statusId>

</xDataBody>

</CMBC>

2.请求的流水号存在且转账成功的交易

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="utf-8" security="none" trnCode="qryXfer"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-02 14:11:48</dtServer>

<dtDead></dtDead>

<lanaguge>utf-8</lanaguge>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>8053qwe</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>转账交易已成功！</statusErrMsg>

</statusId>

32 / 149

</xDataBody>

</CMBC>

民生银行银企直联

3.请求的流水号已退汇

<<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="qryXfer" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2022-07-14 14:44:28</dtServer>

<userKey>N</userKey>

<dtDead />

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>22071410264600000000000203428461</insId>

<svrId>31301202207146127589058313000000</svrId>

<statusId>

<statusCode>5</statusCode>

<statusSeverity>PS15</statusSeverity>

<statusErrMsg>交易成功（已退汇）</statusErrMsg>

</statusId>

</xDataBody>

33 / 149

</CMBC>

民生银行银企直联

报文头的<code>节点返回错误码说明

1、E1602 —— 则表示该笔交易银行未受理，可以视为交易失败

2、EYQ13 —— 此汇路目前不支持状态未知交易的对账查询，原交易的状态为未知

3、E6031 —— 转账状态未知

该交易存在的问题：

1、目前上海同城汇路交易如果转账时候接口明确返回成功或者失败，此处可以返回处理
结果，如果转账当时返回超时（WEC02）异常，此处无法调核心接口进行查询。

2：对于原交易状态未知的情况，大、小额、网银互联和行内转账能够从核心获得原交易
的状态，其他汇路，目前不能获得原交易的终态。

6.批量付款(batchXfer)

本部分更新日期:2021-05-08

批量转账交易，用于企业端程序向银行系统提出多笔支付或主动收款时使用，名称为

batchXfer。另外，由于转账交易需要保证客户端与服务器间的同步，因此需要定义同步

机制。

注意：本接口返回成功标记只表示申请已接收，每条支付是否成功需要调用批量付款

对账接口 qryBatchXfer 查询。

本接口支持的场景如下：

场景

公转公（本行）

公转公（跨行）

说明

支持 最大笔数：5000 借贷模式：一借一贷

支持 最大笔数：5000 借贷模式：一借一贷

公转私-费用报销（本行）

不支持

公转私-费用报销（跨行）

支持 最大笔数：5000 借贷模式：一借一贷

公转私-代发工资（本行）

不支持

公转私-代发工资（跨行）

支持 最大笔数：5000 借贷模式：一借一贷

34 / 149

民生银行银企直联

借贷模式：

在批量转账中，一借一贷是指每笔转出均从付款账户单独扣款，如一个批次包括 100 笔

交易，付款账户的交易明细中详细列举 100 笔转出明细，付款回单也为 100 张，每张回

单均展示收、付方信息。一借多贷是值一个批次汇总后仅从付款账户扣一笔款，如一个批

次包括 100 笔交易，付款账户的交易明细中仅展示 1 笔转出，付款回单为 1 张，回单不

显示收方信息。

本接口发出的交易的转账结果查询，请使用接口：qryBatchXfer。

跨行公转私交易若涉及需重点测试，并把测试报文补充到测试报告中，否则测试报告

审核不通过。

6.1.请求(batchXfer)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标（必输，但无作用）

64

 <insId>

流水号

Y

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <payerAcct>

付款账户

Y

32

 <payType>

付款类型 :

0:支付

1:代发工资(预留、暂不支持)

2:费用报销(预留、暂不支持)

 <totalRow>

总记录数

 <totalAmt>

总金额数

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为数据行分

N

Y

Y

Y

6

15,2

35 / 149

割符，具体格式定义与付款类型有关

具体字段释义及上送规则请参照 6.3

民生银行银企直联

 <certNo>

企业自制凭证号，非必输

N

</xDataBody>

6.2.响应(batchXfer)

  标记

说明

<xDataBody>

 <batchTransfers>

 <trnId>

原值返回

 <insId>

流水号

 </batchTransfers>

</xDataBody>

是否

长度

必返

Y

Y

64

64

6.3.支付文件数据格式<fileContent>：

交易参考号|汇路|企业自制凭证号|支付业务种类|业务种类代码|收款账户类型|收款账

号|收款户名|收款行联行号|收款行名|备注|发送短信给收款方标识|收款方手机号|收

款方 email 地址|常用用途代码|金额^

  域名称

说明

 交易参考号

非必输，数字字母或组合

长度

64

36 / 149

 汇路

民生银行银企直联

2

行内交易输入：0

跨行交易汇路：

小额实时支付：6

大额实时支付：7

网银互联：11

 企业自制凭证号：输入企业自

必输 用于企业 ERP 系统更新流水状态的标

8

制企业自制凭证号

识，目前支持 8 位

 支付业务种类

 业务种类代码

 收款账户类型

 收款账号

 收款户名

可不输入

可不输入

必输

企业

个人卡

2.个人折

必输

必输

 收款行联行号

行内转账不用输入 大小额时填支付行号 网银

互联时填写网银互联支付行号

1

32

60

12

 收款行名

行内转账不输，跨行转账必须输入。 大小额

40

时填写支付行名 网银互联是填写网银互联支

付行名

 备注

可不输入。最长 30 字符。 当公转私时，本

30

字段长度+常用用途代码对应的中文含义的

长度不能超过 40。

37 / 149

民生银行银企直联

 发送短信给收款方标识

0:不通知收款方(目前只支持 0)

1:通知收款方

 收款方手机号

如果通知收款方，手机号码和邮箱至少输入

一项。手机号必须是 11 位。

 收款方 email 地址

如果通知收款方，手机号码和邮箱至少输入

一项。手机号必须是 11 位。

 常用用途代码

公转私时必输。

 金额

必输

15

，2

6.4.常用用途代码及其含义

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

316 报刊费

317 餐费

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

38 / 149

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

394 运费

395 公积金

396 代收货款

代发工资：

4111 工资奖金

民生银行银企直联

39 / 149

民生银行银企直联

6.5.报文示例

请求报文

<?xml?version="1.0"?encoding="utf-8"?>

<CMBC ? header="100" ? version="100" ? security="none" ? lang="utf-

8" ? trnCode="batchXfer">

<requestHeader>

<dtClient>2012-05-11?11:08:33</dtClient>

<clientId>2005340790</clientId>

<userId>200534079001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>batchxfer00</trnId>

<cltcookie></cltcookie>

<insId>batchxfer027</insId>

<payerAcct>0103014170024987</payerAcct>

<payType>0</payType>

<totalRow>1</totalRow>

<totalAmt>99.00</totalAmt>

<fileContent>123456asd|7|123456|||0|21|21|102659000491|中国工

商银行股份有限公司四川省绵阳剑门支行|测试

|1|21212121|<EMAIL>||99.00</fileContent>

</xDataBody>

</CMBC>

响应报文

<?xml?version="1.0"?encoding="utf-8"?>

<CMBC ? header="100" ? lang="utf-8" ? security="none" ?

trnCode="batchXfer" ?

version="100">

<responseHeader>

<status>

40 / 149

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2012-05-15?14:32:10</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<batchTransfers>

<trnId> batchxfer00</trnId>

<insId> batchxfer027</insId>

</batchTransfers>

</xDataBody>

</CMBC>

7.批量转账制单(batchTransferXfer)

本部分更新日期:2021-04-02

批量转账制单接口是通过银企直联进行制单，复核人员登陆企业网银进行复核，批量

转账才可生效。

注意：本接口返回成功标记只表示申请已接收，每条支付是否成功需要调用批量付款

对账接口 qryBatchXfer 查询。

本接口支持的场景如下：

场景

说明

公转公（本行）

公转公（跨行）

支持 最大笔数：5000 借贷模式：一借一贷

支持 最大笔数：5000 借贷模式：一借一贷

公转私-费用报销（本行）

不支持

公转私-费用报销（跨行）

支持 最大笔数：5000 借贷模式：一借一贷

公转私-代发工资（本行）

不支持

公转私-代发工资（跨行）

支持 最大笔数：5000 借贷模式：一借一贷

41 / 149

民生银行银企直联

借贷模式：在批量转账中，一借一贷是指每笔转出均从付款账户单独扣款，如一个批

次包括 100 笔交易，付款账户的交易明细中详细列举 100 笔转出明细，付款回单也为

100 张，每张回单均展示收、付方信息。一借多贷是值一个批次汇总后仅从付款账户扣

一笔款，如一个批次包括 100 笔交易，付款账户的交易明细中仅展示 1 笔转出，付款回

单为 1 张，回单不显示收方信息。

本接口发出的交易的转账结果查询，请使用接口：qryBatchXfer。

7.1.请求(batchTransferXfer)

  标记

说明

是否

长度

必输

<xDataBody>

 <trnId>

客户端产生的交易流水

 <insId>

客户端流水号唯一

Y

 <cltcookie>

可选，客户端 cookie，响应时原值返

回

64

64

 <payerAcct>

付款账户

Y

32

 <payType>

付款类型：

0:支付；

 <totalRow>

总记录数

 <totalAmt>

总金额数

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为

数据行分割符，具体格式定义与付款

类型有关

6

15,2

Y

Y

Y

42 / 149

 <extendData>

扩展信息格式采用:key1=v1

民生银行银企直联

key2

=v2

</xDataBody>

7.2.响应(batchTransferXfer)

  标记

说明

<xDataBody>

 <batchTransfer>

 <trnId>

原值返回

 <svrId>

服务器流水

 <insId>

流水号

 <outExtendData>

 </batchTransfer>

</xDataBody>

是否

长度

必返

Y

Y

Y

64

32

64

7.3.支付文件数据格式<fileContent>：

汇路|企业自制凭证号|支付业务种类|业务种类代码|收款账户类型|收款账号|收款户名|收

款行联行号|收款行名|备注|发送短信给收款方标识|收款方手机号|收款方 email 地址|常

用用途代码|金额^

支付文件数据字段说明 ：

43 / 149

  域名称

说明

 汇路

行内交易输入 0

民生银行银企直联

长度

2

跨行交易汇路为: 6

小额实时支付 :7

大额实时支付 :11

网银互联(暂不支持)

 企业自制凭证号：

非必输 用于企业 ERP 系统更新流水状态的标识，目

8

前支持 8 位数字

 支付业务种类

可不输入

 业务种类代码

可不输入

 收款账户类型

非必输：

企业

个人卡

2.个人折

必输

必输

 收款账号

 收款户名

 收款行联行号

行内转账不用输入 大小额时填支付行号 网银互联时

填写网银互联支付行号

1

32

64

12

 收款行名

非必须； 大小额时填写支付行名 网银互联是填写网

40

银互联支付行名

 备注

可不输入。最长 30 字符。 当公转私时，本字段长

30

度+常用用途代码对应的中文含义的长度不能超过

40。

44 / 149

民生银行银企直联

 发送短信给收款方标识 0:不通知收款方(目前只支持 0) 1:通知收款方

 收款方手机号

如果通知收款方，手机号码和邮箱至少输入一项。

手机号必须是 134——139。

 收款方 email 地址

如果通知收款方，手机号码和邮箱至少输入一项。

手机号必须是 134——139。

 常用用途代码

公转私时必输

 金额

必输

7.4.常用用途代码及其含义

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

316 报刊费

317 餐费

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

15，

2

45 / 149

347 个人纳税退还

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

394 运费

395 公积金

396 代收货款

代发工资：

4111 工资奖金

7.5.报文示例

请求报文：

民生银行银企直联

46 / 149

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

民生银行银企直联

trnCode="batchTransferXfer">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>2200013747</clientId>

<userId>2200013747001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>201806040000002</trnId>

<cltcookie></cltcookie>

<insId>201806040000002</insId>

<payerAcct>609797800</payerAcct>

<payType>0</payType>

<totalRow>2</totalRow>

<totalAmt>3.29</totalAmt>

<fileContent>0|125486|||1|609797834|UAT 票据测试

020||||0||||1.55^6|6582145|||1|6226190303315570|dssass|102301
000921|中国建设银行||0||||1.74</fileContent>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC security="none" trnCode="batchTransferXfer"
header="100"

lang="utf-8" version="100">

<responseHeader>

<status>

47 / 149

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2018-08-22 10:38:07</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<batchTransfers>

<trnId>201806040000002</trnId>

<insId>201806040000002</insId>

</batchTransfers>

</xDataBody>

</CMBC>

8.批量费用报销、代发工资 (batchCostReimbNew)

本部分更新日期:2024-09-26

说明：

1.批量费用报销交易，用于企业端程序向银行系统提出多笔公转私支付时使用，名称为

batchCostReimbNew。

2.另外，由于转账交易需要保证客户端与服务器间的同步，因此需要定义同步机制。

3.注意：本接口返回成功标记只表示申请已接收，每条支付是否成功需要调用批量费用报

销查询接口查询。

4.本接口支持的场景如下：

场景

说明

公转私-费用报销（本行）

最大笔数：5000 借贷模式：一借一贷

48 / 149

民生银行银企直联

场景

说明

公转私-费用报销（跨行）

最大笔数：5000 借贷模式：一借一贷

公转私-代发工资（本行）

最大笔数：5000 借贷模式：一借一贷

公转私-代发工资（跨行）

最大笔数：5000 借贷模式：一借一贷

公转私-费用报销（本行）

最大笔数：5000 借贷模式：一借多贷

公转私-费用报销（跨行）

最大笔数：5000 借贷模式：一借多贷

公转私-代发工资（本行）

最大笔数：5000 借贷模式：一借多贷

公转私-代发工资（跨行）

最大笔数：5000 借贷模式：一借多贷

5.借贷模式：在批量转账中，一借一贷是指每笔转出均从付款账户单独扣款，如一个批次

包括 100 笔交易，付款账户的交易明细中详细列举 100 笔转出明细，付款回单也为

100 张，每张回单均展示收、付方信息。一借多贷是值一个批次汇总后仅从付款账户

扣一笔款，如一个批次包括 100 笔交易，付款账户的交易明细中仅展示 1 笔转出，付

款回单为 1 张，回单不显示收方信息。

6.结果查询接口：查询每笔转账结果使用 批量费用报销查询（qryBatchCostReimbNe

w）

7.本接口现已支持代发工资到数字人民币账户

8.1.请求(batchCostReimbNew)

  标记  

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志（必输，但无作

Y

64

用）

49 / 149

  标记  

说明

 <insId>

流水号

 <cltcookie>

可选，客户端 cookie，响应时原值返回

民生银行银企直联

是否

长度

必输

Y

64

 <PayerAcNo>

付款账户

 <payType>

付款类型

1：代发工资

2：费用报销

 <payModel>

转账模式

0：一借一贷，

1：一借多贷

 <totalRow>

总记录数

 <totalAmt>

总金额数

 <fileContentType> 本字段控制 fileContent 的数据格式： 1:明

文 2:SM4 加密 加密时，请客户侧自行随机

生成一次性会话密钥，针对 fileContent 内

容使用 SM4 对称算法进行加密。加密模式

ECB。 如未上送本字段，则系统默认为明

文。如上送本字段，不可为空。

32

1

15,2

Y

Y

Y

Y

Y

N

 <secKeyEnc>

如客户选择加密传输 fileContent，则须将

N

一次性会话密钥使用银行公钥加密后，通过

本字段送给银行，本字段赋值为加密后密文

的 base64 编码。加密算法：SM2；银行公

钥使用 queryPublicKey 接口获取。 如

fileContentType 为 2 ，则本字段必须上送

且不可为空。

50 / 149

  标记  

说明

民生银行银企直联

是否

长度

必输

 <secKeyIndex>

银行公钥的索引，使用 queryPublicKey 接

N

口获取。 如 fileContentType 为 2 ，则本

字段必须上送且不可为空。

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为数据行

Y

分割符，具体格式定义与付款类型有关

请参照 8.3 上送相关字段。

 <Usage>

常用用途代码|备注（用途代码转义中文+备

N

50

注长度不得超过 50，备注可不输入，例如：

“311” 或者“311”)。

一借一贷模式：（非必输，Usage 与

<fileContent>中上送用途不能同时为空）

若<fileContent>中上送用途，收付款双

方的附言信息均取自<fileContent>中

上送的用途与备注信息。

若<fileContent>中上未送用途，收付款

双方的附言信息则取<Usage>(用途代

码|备注)。

一借多贷模式：必输

付款方附言信息为<Usage>用途字段。

收款方付款附言信息优先上送

<fileContent>中的用途与备注信息。若明

细中为空，则上送批次信息中的用途字段

<Usage>信息。

</xDataBody>

51 / 149

8.2.响应(batchCostReimbNew)

民生银行银企直联

  标记

说明

<xDataBody>

 <batchCostReimbNew>

 <trnId>

 <insId>

原值返回

原值返回

 </batchCostReimbNew>

</xDataBody>

加密流程说明

是否

长度

必返

Y

Y

64

64

1.生成一次性会话密钥。 客户应采用随机的方法，生成一次性会话密钥。密钥长度：16

字节。

2.针对明细进行对称加密 使用 SM4 算法，ECB 模式，对明细内容进行加密。密钥使用

上述一次性会话密钥。由于明细中包含中文，因此约定明细 String 转为 Byte 数组

时编码格式为：UTF-8。 加密后，密文 Byte 数组应转为 base64 编码，并赋值于

fileContent 字段。

3.针对“一次性会话密钥”进行非对称加密保护 因银行解密明细需要使用上述“一次性会话

密钥”，因此需要将“一次性会话密钥”进行加密保护后，传输至银行。加密算法：

SM2，银行公钥及索引，见接口文档字段描述。 加密后，密文 Byte 数组应转为

base64 编码，并赋值于 secKeyEnc 字段。

8.3.支付文件数据格式<fileContent>：

支付文件数据格式<fileContent>：

52 / 149

交易参考号|收款账号|收款账户名 |用途|备注|本他行标志|汇路|收款行行名|收款行

行号|企业自制凭证号|备用字段 1|备用字段 2|备用字段 3|金额

民生银行银企直联

 域名称  

说明

 交易参考号

非必输，数字字母或组合

 收款账号

 收款户名

必输

必输

长度

60

32

64

 用途

可不输入，输入常用用途代码，与 Usage

的常用用途代码不可同时为空,

 备注

可不输入 ，用途代码转义中文+备注长度

50

 本他行标志

不得超过 50

必输

0：同行，

1：他行

 汇路

非必输， 本行转账时不输入；

1

2

跨行转账时，

6：小额，

7：大额，

11：网上支付跨行清算系统（网银互

联），若不输入 100 万以上默认走大额汇

路，100 万以下走网银互联汇路

 收款行行名

非必输，跨行转账，明细返回的收款行行

120

名以行号对应的行名为准。

 收款行行号

行内转账不用输入，跨行转账必须输入 大

12

小额时填支付行号 网银互联时填写网银互

联支付行号

53 / 149

民生银行银企直联

长度

8

 域名称  

说明

 企业自制凭证号

非必输

 备用字段 1

暂未启用，传空值

 备用字段 2

暂未启用，传空值

 备用字段 3

暂未启用，传空值

 金额

必输

15,2

数字人民币代发工资模板：

支付文件数据格式<fileContent>：

交易参考号|收款账号|收款账户名 |用途|备注|本他行标志|汇路|收款行行名|收款行行号|

企业自制凭证号|备用字段 1|备用字段 2|备用字段 3|金额

 域名称  

说明

 交易参考号

非必输，数字字母或组合

 收款账号

 收款户名

必输

必输

长度

60

32

64

 用途

可不输入，输入常用用途代码，与 Usage 的常用用途

代码不可同时为空,

 备注

可不输入 ，用途代码转义中文+备注长度不得超过 50

50

 本他行标志

无需输入

 汇路

无需输入

 收款行行名

无需输入

 收款行行号

无需输入

1

2

120

12

54 / 149

 域名称  

说明

 企业自制凭证号 非必输

 备用字段 1

暂未启用，传空值

 备用字段 2

暂未启用，传空值

 备用字段 3

暂未启用，传空值

 金额

必输

8.4.常用用途代码及其含义

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

316 报刊费

317 餐费

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

民生银行银企直联

长度

8

55 / 149

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

394 运费

395 公积金

396 代收货款

代发工资：

4111 工资奖金

100

101

102

103

保健费

抚恤费

养老金余额

小额医保补助

民生银行银企直联

56 / 149

104

105

106

11

12

13

14

15

16

17

171

172

173

174

175

176

177

178

179

18

180

19

20

技能鉴定费

自治州津贴

佣金

工资

奖金

过节费

补贴

劳务费

薪资

绩效工资

演出费

津贴

节日慰问

节日补助

旅游补贴

讲课酬金

实习补贴

生育保险

伤残补助金

报销

年终奖轧差

1 月工资

2 月工资

民生银行银企直联

57 / 149

21

22

23

24

25

26

27

28

29

30

31

32

33

34

35

36

37

38

39

40

41

42

43

3 月工资

4 月工资

5 月工资

6 月工资

7 月工资

8 月工资

9 月工资

10 月工资

11 月工资

12 月工资

保洁奖励

半年奖

补助费

餐费

春节过节费

春运保障奖

DOC 奖励

第二季度奖

第三季度奖

代通知金

二次年终奖

防爆补贴

防暑费

民生银行银企直联

58 / 149

44

45

46

47

48

49

50

51

52

53

54

55

56

57

58

59

60

61

62

63

64

65

66

服务保障奖

服务提升奖励

服装费

稿费奖励

黄金周补贴

加班费

季度奖

经济补偿金

奖励

绩效奖金

就业补助金

考核奖

劳保费

两节保障奖

门诊补贴

年终奖

培训奖励

取暖费

暑运保障奖

十一过节费

生育津贴

通报奖励

特殊贡献奖

民生银行银企直联

59 / 149

67

68

69

70

71

72

73

74

75

76

77

78

79

80

81

82

83

84

85

86

87

88

89

通讯费

旺季生产奖

慰问金

休假补贴

元旦过节费

演练奖励

援藏补助

中秋过节费

助学金

志愿者补贴

安全奖

履职奖

周年奖

双过半奖

设备奖

治安消防奖

任务完成奖

修日利废奖

立功竞赛奖

高温津贴

年金

道口奖励

文明单位奖励

民生银行银企直联

60 / 149

民生银行银企直联

90

91

92

93

94

95

96

97

98

补充医保

三不让

丧抚费

医保卡余额

公积金

工伤报销

房补

零星医药费

风险抵押金

99独生子女奖励

8.5.报文示例

请求报文 跨行公转私：

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="batchCostReimbNew">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>zBATb07207trnID012ZXZ001</trnId>

<cltcookie></cltcookie>

<insId>202102090002</insId>

61 / 149

民生银行银企直联

<PayerAcNo>*********</PayerAcNo>

<payType>2</payType>

<payModel>0</payModel>

<totalRow>3</totalRow>

<totalAmt>36</totalAmt>

<fileContentType>2</fileContentType>

<secKeyEnc>XXXXXXX</secKeyEnc>

<secKeyIndex>XXXXXXX</secKeyEnc>

<fileContent>xxxxxxxxxxxxxxxxxxxxx</fileContent>

<Usage>522|测试</Usage>

<extent1></extent1>

<extent2></extent2>

<extent3></extent3>

</xDataBody>

</CMBC>

数字人民币代发：

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="UTF-8"

trnCode="batchCostReimbNew">

<requestHeader>

<dtClient>2022-11-14 15:25:14</dtClient>

<clientId>2200003220</clientId>

<userId>2200003220001</userId>

<userPswd>******</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2022111409515287</trnId>

<cltcookie>1111111111136</cltcookie>

<insId>CMBCINS2022111409515288</insId>

<PayerAcNo>*********</PayerAcNo>

<payType>1</payType>

<payModel>0</payModel>

<totalRow>1</totalRow>

<totalAmt>72</totalAmt>

62 / 149

<Usage>4111|测试</Usage>

<fileContent>zBATb07207trnID012ZXZ123|0021642528910858|周

民生银行银企直联

超|||1|6|||********||||72</fileContent>

<extent1></extent1>

<extent2></extent2>

<extent3></extent3>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="batchCostReimbNew" security="none" lang="utf-8"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2013-10-24 09:42:05</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<batchCostReimbNew>

<trnId>zBATb07207trnID012ZXZ001</trnId>

<insId>JAN07228insIDZ033</insId>

</batchCostReimbNew>

</xDataBody>

</CMBC>

63 / 149

9.批量制单费用报销、代发工资(BatchTransferCost

民生银行银企直联

Reimb)

本部分更新日期:2021-05-08

批量制单费用报销交易，用于企业端程序向银行系统提出多笔公转私支付时使用；由

制单员制单，审批岗审批，此交易用于制单员制单；审批岗审批完成后交易发出。由于转

账交易需要保证客户端与服务器间的同步，因此需要定义同步机制。

本行公转私一个批次最大笔数建议为 10000 笔，跨行公转私一个批次最大笔数建议

为 5000 笔。

注意：本接口返回成功标记只表示制单交易成功，待审批岗审批完成后，每条支付是

否成功需要调用（qryBatchCostReimb）接口查询。

本接口支持的场景如下：

场景

说明

公转私-费用报销（本行）

最大笔数：10000 借贷模式：一借多贷

公转私-费用报销（跨行）

最大笔数：5000 借贷模式：一借一贷

公转私-代发工资（本行）

最大笔数：10000 借贷模式：一借多贷

公转私-代发工资（跨行）

最大笔数：5000 借贷模式：一借多贷

借贷模式：在批量转账中，一借一贷是指每笔转出均从付款账户单独扣款，如一个批

次包括 100 笔交易，付款账户的交易明细中详细列举 100 笔转出明细，付款回单也为

100 张，每张回单均展示收、付方信息。一借多贷是值一个批次汇总后仅从付款账户扣

一笔款，如一个批次包括 100 笔交易，付款账户的交易明细中仅展示 1 笔转出，付款回

单为 1 张，回单不显示收方信息。

本接口发出的交易的转账结果查询，请使用接口：qryBatchCostReimb。

64 / 149

9.1.请求(BatchTransferCostReimb)

  标记

说明

<xDataBody>

民生银行银企直联

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

 <insId>

流水号

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <payerAcNo>

付款账户

 <payType>

付款类型

1:代发工资

2:费用报销

 <totalRow>

总记录数

 <totalAmt>

总金额数

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为数据行分割

符，具体格式定义与付款类型有关

64

64

32

1

15,2

Y

N

Y

N

Y

Y

N

 <Usage>

常用用途代码|备注（用途代码转义中文+备注长

Y

50

度不得超过 50，备注可不输入，常用用途代码必

输）

</xDataBody>

65 / 149

9.2.响应(BatchTransferCostReimb)

民生银行银企直联

  标记

说明

<xDataBody>

 <batchCostReimb>

 <trnId>

 <insId>

原值返回

流水号

 </batchCostReimb>

</xDataBody>

是否

长度

必返

Y

Y

64

64

9.3.本行代发工资、费用报销数据格式<fileContent>：

收款账号|收款账户名 |备注（预留、暂不支持）|金额 |企业自制凭证号

  域名称

 收款账号

 收款户名

 备注

 金额

说明

必输

必输

可不输入（预留、暂不支持）

必输

 企业自制凭证号 非必输，，(8 位数字)

长度

32

64

15,2

8

9.4.跨行代发工资、费用报销数据格式<fileContent>：

收款账号|收款账户名 |用途|备注|汇路|收款行行号|企业自制凭证号|金额

66 / 149

民生银行银企直联

  域名称

 收款账号

 收款户名

 用途

 备注

 汇路

说明

必输

必输

可不输入（预留、暂不支持）

可不输入（预留、暂不支持）

必输，

6：小额，

7：大额，

11：网上支付跨行清算系统（网银互联）， 代发

工资时暂不支持 6：小额、7：大额，默认走“网

上支付跨行清算系统”

 收款行行号

必输

 企业自制凭证号

非必输，(8 位数字)

 金额

必输

9.5.常用用途代码及其含义

费用报销：

311 差旅费

312 办公费

313 水电费

314 通讯费

315 交通费

316 报刊费

317 餐费

长度

32

64

40

2

12

8

15,2

67 / 149

318 医药费

319 会议费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

349 银联资金清算

374 律师费

375 资金退还

376 拆迁补偿款

377 垫付资金

378 柜台销售结算款

379 网上交易款

380 小件商品销售收入

381 制片费

383 奖助学金

384 退学费

385 日常报销费用

386 日常经营费用

388 案件款

389 投资赎回款

390 投资退款

391 分红

392 工程款

393 租赁费

394 运费

民生银行银企直联

68 / 149

395 公积金

396 代收货款

代发工资：

4111 工资奖金

100

101

102

103

104

105

106

11

12

13

14

15

16

17

171

172

173

174

175

176

保健费

抚恤费

养老金余额

小额医保补助

技能鉴定费

自治州津贴

佣金

工资

奖金

过节费

补贴

劳务费

薪资

绩效工资

演出费

津贴

节日慰问

节日补助

旅游补贴

讲课酬金

民生银行银企直联

69 / 149

177

178

179

18

180

实习补贴

生育保险

伤残补助金

报销

年终奖轧差

19

20

21

22

23

24

25

26

27

28

29

30

31

32

33

34

35

36

1 月工资

2 月工资

3 月工资

4 月工资

5 月工资

6 月工资

7 月工资

8 月工资

9 月工资

10 月工资

11 月工资

12 月工资

保洁奖励

半年奖

补助费

餐费

春节过节费

春运保障奖

民生银行银企直联

70 / 149

37

38

39

40

41

42

43

44

45

46

47

48

49

50

51

52

53

54

55

56

57

58

59

DOC 奖励

第二季度奖

第三季度奖

代通知金

二次年终奖

防爆补贴

防暑费

服务保障奖

服务提升奖励

服装费

稿费奖励

黄金周补贴

加班费

季度奖

经济补偿金

奖励

绩效奖金

就业补助金

考核奖

劳保费

两节保障奖

门诊补贴

年终奖

民生银行银企直联

71 / 149

60

61

62

63

64

65

66

67

68

69

70

71

72

73

74

75

76

77

78

79

80

81

82

培训奖励

取暖费

暑运保障奖

十一过节费

生育津贴

通报奖励

特殊贡献奖

通讯费

旺季生产奖

慰问金

休假补贴

元旦过节费

演练奖励

援藏补助

中秋过节费

助学金

志愿者补贴

安全奖

履职奖

周年奖

双过半奖

设备奖

治安消防奖

民生银行银企直联

72 / 149

民生银行银企直联

83

84

85

86

87

88

89

90

91

92

93

94

95

96

97

98

任务完成奖

修日利废奖

立功竞赛奖

高温津贴

年金

道口奖励

文明单位奖励

补充医保

三不让

丧抚费

医保卡余额

公积金

工伤报销

房补

零星医药费

风险抵押金

99 独生子女奖励

9.6.报文示例

请求报文：

本行公转私：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BatchTransferCostReimb">

73 / 149

民生银行银企直联

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>2200029172</clientId>

<userId>2200029172001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>zBATb07207trnID012ZXZ001</trnId>

<cltcookie></cltcookie>

<insId>202008240000001</insId>

<payerAcNo>60026860X</payerAcNo>

<payType>1</payType>

<totalRow>2</totalRow>

<totalAmt>70</totalAmt>

<Usage>4111|代发工资测试</Usage>

<fileContent>6226220114234163|西门吹雪

||50|********^6226182900005035|张思跑||20|********</fileContent>

</xDataBody>

</CMBC>

跨行公转私：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="BatchTransferCostReimb">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>2200029172</clientId>

<userId>2200029172001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>zBATb07207trnID012ZXZ001</trnId>

74 / 149

民生银行银企直联

<cltcookie></cltcookie>

<insId>20200821005</insId>

<payerAcNo>60026860X</payerAcNo>

<payType>1</payType>

<totalRow>2</totalRow>

<totalAmt>18</totalAmt>

<Usage>4111|代发工资测试</Usage>

<fileContent>6228480018536951272|刘平金

|||11|103100000026|********|9^6228480018536951272|刘平金

|||11|103100000026|********|9</fileContent>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="BatchTransferCostReimb" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-05-27 09:25:15</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>zBATb07207trnID012ZXZ001</trnId>

<insId>2a020051800003</insId>

</xDataBody>

</CMBC>

75 / 149

10.批量付款对账(qryBatchXfer)

民生银行银企直联

本部分更新日期:2021-04-02

根据流水号（insId）查询一笔批量付款交易明细结果。

10.1.请求(qryBatchXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <payType> 付款类型

0:支付

1:代发工资(预留、暂不支持)

2:费用报销(预留、暂不支持)

</xDataBody>

10.2.响应(qryBatchXfer)

  标记

说明

<xDataBody>

 <statusId>

是否

长度

必输

64

64

Y

Y

是否

长度

必返

 <statusCode>

0-受理成功，

16

2-审批未通过，

76 / 149

  标记

说明

4-等待审批中

 <statusSeverity>

处理结果安全信息

 <statusErrMsg>

返回信息

 </statusId>

 <batchTransfers>

 <trnId>

原值返回

 <insId>

流水号

民生银行银企直联

是否

长度

必返

80

80

64

64

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为数据

行分割符，具体格式定义与付款类型有关

 </batchTransfers>

</xDataBody>

支付文件数据格式<fileContent>：

企业自制凭证号 13|付款人账号 32|付款人名称 60|付款人行名 40|收款人账号 32|收款

人名称 60|收款行行名 40|金额 15,2|交易状态 2|错误信息 80^企业自制凭证号 13|付款

人账号 32|付款人名称 60|付款人行名 40|收款人账号 32|收款人名称 60|收款行行名

40|金额 15,2|交易状态 2|错误信息 80

状态码说明：1:成功、2:失败

10.3.报文示例

请求报文

<?xml version="1.0"?encoding="utf-8"?>

77 / 149

<CMBC header="100" ? version="100" ? security="none" ? lang="utf-8" ?

民生银行银企直联

trnCode="qryBatchXfer">

<requestHeader>

<dtClient>2012-05-11?11:08:33</dtClient>

<clientId>2005340790</clientId>

<userId>200534079001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId> batchxfer00</trnId>

<insId> batchxfer027</insId>

<payType>0</payType>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0"?encoding="utf-8"?>

<CMBC ? header="100" ? lang="utf-8" ? security="none" ?

trnCode="qryBatchXfer" ?

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2012-05-15?14:33:08</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>utf-8</language>

</responseHeader>

<xDataBody>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>批量支付受理成功！</statusErrMsg>

78 / 149

民生银行银企直联

</statusId>

<batchTransfers>

<trnId>batchxfer00</trnId>

<insId> batchxfer027</insId>

<fileContent>

123456|0103014170024987|测试 2005340790|中国民生银行西安高新开

发区支行|21|21|中国工商银行股份有限公司四川省绵阳剑门支行|99|1|

</fileContent>

</batchTransfers>

</xDataBody>

</CMBC>

11.批量费用报销、代发工资查询(qryBatchCostRei

mbNew)

本部分更新日期:2022-06-02

根据流水号（insid）查询一笔批量费用报销交易明细结果。

11.1.请求(qryBatchCostReimbNew)

  标记

说明

<xDataBody>

是否

必返

长

度

 <trnId>

客户端产生的交易唯一标志（必输，但无作

用）

 <insId>

指令 ID，一条转账指令在客户端的唯一标识 Y

 <payType>

付款类型 传值不起作用，系统自动识别

1:代发工资

64

64

1

79 / 149

  标记

说明

2:费用报销

民生银行银企直联

是否

必返

长

度

 <fileContentType> 本字段控制 fileContent 的数据格式： 1:明

文 2:SM4 加密 加密时，请客户侧自行随机

生成一次性会话密钥，针对 fileContent 内

容使用 SM4 对称算法进行加密。加密模式

ECB。 如未上送本字段，则系统默认为明

文。如上送本字段，不可为空。

 <secKeyEnc>

如客户选择加密传输 fileContent，则须将一

次性会话密钥使用银行公钥加密后，通过本

字段送给银行，本字段赋值为加密后密文的

base64 编码。加密算法：SM2；银行公钥

使用 queryPublicKey 接口获取。 如

fileContentType 为 2 ，则本字段必须上送

且不可为空。

 <secKeyIndex>

银行公钥的索引，使用 queryPublicKey 接

口获取。 如 fileContentType 为 2 ，则本

字段必须上送且不可为空。

 <extent1>

扩展字段 1 （暂未启用）

 <extent2>

扩展字段 2（暂未启用）

 <extent2>

扩展字段 3（暂未启用）

</xDataBody>

200

200

200

80 / 149

11.2.响应(qryBatchCostReimbNew)

  标记

说明

<xDataBody>

 <statusId>

 <statusCode>

0-受理成功，

1-审批未通过，

4-等待审批中

 <statusSeverity>

处理结果安全信息 80

 <statusErrMsg>

返回信息

 </statusId>

 <batchTransfers>

 <trnId>

原值返回

 <insId>

流水号

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为数据行

分割符，具体格式定义与付款类型有关 该字

段根据客户上送的 fileContentType 而定，

如客户在 fileContentType 中要求银行以加

密的方式返回，同时上送了 secKeyEnc 和

secKeyIndex，则银行将先解密出客户的一

次性会话密钥，并将明细内容加密并转换为

base64 编码后返回给客户。加密算法：

SM4，加密模式：ECB。

 </batchTransfers>

民生银行银企直联

是否

必返

长

度

8

80

80

64

64

81 / 149

  标记

说明

</xDataBody>

加解密流程说明

民生银行银企直联

是否

必返

长

度

1.客户侧加密流程说明： (1) 生成一次性会话密钥。 客户应采用随机的方法，生成一次性

会话密钥。密钥长度：16 字节。 (2) 对“一次性会话密钥”进行非对称加密保护 因银

行加密明细需要使用上述“一次性会话密钥”，因此需要将“一次性会话密钥”进行

加密保护后，传输至银行。加密算法：SM2，银行公钥及索引，见接口文档字段描

述。 加密后，密文 Byte 数组应转为 base64 编码，并赋值于 secKeyEnc 字段。

2.银行侧加解密流程说明： (1) 银行获取“一次性会话密钥” 银行使用私钥对 secKeyEnc

解密，取得“一次性会话密钥”。 (2) 银行明细进行对称加密 银行使用 SM4 算法，

ECB 模式，对明细内容进行加密。密钥使用上述一次性会话密钥。由于明细中包含

中文，因此约定明细 String 转为 Byte 数组时编码格式为：UTF-8。 加密后，密文

Byte 数组应转为 base64 编码，并赋值于 fileContent 字段。

3.客户侧解密明细流程说明： (1) 客户收到银行响应后，应使用之前客户侧生成的“一次

性会话密钥”对明细内容进行解密。

支付文件数据格式<fileContent>：

企业自制凭证号 13 | 付款人账号 32 | 付款人名称 60 | 付款人行名 40|收款人账号 32|

收款人名称 60|收款行行名 40|金额 15,2|用途 50（暂不返回）|备注 50（暂不返回）|

备用字段 1|备用字段 2|备用字段 3|交易参考号 64|交易状态 2|错误信息 80^企业自制

凭证号 13|付款人账号 32|付款人名称 60|付款人行名 40|收款人账号 32|收款人名称

60|收款行行名 40||金额 15,2|用途 50（暂不返回）|备注 50（暂不返回）|备用字段 1|

备用字段 2|备用字段 3|交易参考号 64|交易状态 2|错误信息 80^

状态码说明：10:成功、20:失败

82 / 149

民生银行银企直联

11.3.报文示例

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="qryBatchCostReimbNew">

<requestHeader>

<dtClient>2015-12-08 10:12:54</dtClient>

<clientId>2200003220</clientId>

<userId>2200003220002</userId>

<userPswd>123123</userPswd>

<language>utf-8<language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>2015120800015505</trnId>

<insId>201512070ffdvv7</insId>

<payType>2</payType>

<fileContentType>2</fileContentType>

<secKeyEnc>XXXXXXX</secKeyEnc>

<secKeyIndex>XXXXXXX</secKeyEnc>

<extent1></extent1>

<extent2></extent2>

<extent3></extent3>

</xDataBody>

</CMBC>

响应报文

83 / 149

<?xml version="1.0" encoding="utf-8"?>

<CMBC security="none" trnCode="qryBatchCostReimbNew" header="100"

民生银行银企直联

lang="utf-8" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-08-24 16:56:06</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

utf-8

</responseHeader>

<xDataBody>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>批量支付受理成功！</statusErrMsg>

</statusId>

<batchTransfers>

<trnId>batchxfer00</trnId>

<insId>2020082400005</insId>

<fileContent>XXXXXXX</fileContent>

</batchTransfers>

</xDataBody>

</CMBC>

84 / 149

12.汇款交易结果明细查询(qryRemittDetail)

民生银行银企直联

本部分更新日期:2021-04-02

交易要求：

1：汇款交易结果查询明细最大查询 6 个月以内的明细；查询区间最大为 1 个月。

2：起始笔数为 1 开始的查询交易 同一账号在设定时间内不能连续查询，目前默认 15 分

钟，起始笔数为其他则认为翻页，不受此限制。

12.1.请求(qryRemittDetail)

  标记

说明

<xDataBody>

长度

是否

必输

 <trnId>

客户端产生的交易唯一标志（必输，但无作

64

用）

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <acntNo>

查询账号

 <qryFlag>

查询标志

0 - 成功，

1 - 退款

 <payChannel> 汇路

2 - 小额，

3-大额

32

Y

Y

Y

 <dateFrom>

开始日期（含）

Y

10

格式为 YYYY-MM-DD

85 / 149

  标记

说明

 <dateTo>

截止日期（含）

格式为 YYYY-MM-DD

 <startNo>

起始笔数，必须是有意义的数字

 <queryRows> 查询笔数(最大查询 200 笔)

 <extendData> 备用字段

</xDataBody>

12.2.响应(qryRemittDetail)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <allAmt>

 <allNum>

总金额

总笔数

 <dtlList>

明细列表开始

 <List>

 <transDate>

交易日期

 <serialNum>

交易流水号

 <trsAmt>

交易金额

民生银行银企直联

是否

必输

Y

Y

Y

长度

10

3

是否

长度

必返

3

32

32

60

86 / 149

Y

Y

Y

  标记

说明

 <payerAcNo>

付款账户账号

 <payerAcName> 付款账户名称

 <payerBankNo> 付款行行号

 <payeeAcNo>

收款账户账号

 <payeeAcName> 收款账户名称

 <payeeBankNo> 收款行行号

 <msgId>

报文标识号

 <detailId>

明细标识号

 <certtype>

付款凭证种类

 <certNo>

付款凭证号码(待定)

 <payChannel>

汇路

2 - 小额；

3 - 大额

 <lendType>

借贷标志:

1-借方（支出）,

2-贷方（收入）

 <remark>

摘要

 <remark2>

摘要 2

 <summary>

备注

 <summary2>

备注 2

民生银行银企直联

是否

长度

必返

Y

Y

Y

Y

Y

Y

Y

Y

1

10

10

13

15,2

32

64

62

87 / 149

民生银行银企直联

是否

长度

必返

  标记

说明

 </List>

 </dtlList>

明细列表结束

</xDataBody>

12.3.报文示例

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="qryRemittDetail">

<requestHeader>

<dtClient>2014-01-06 11:08:33</dtClient>

<clientId>2200013812</clientId>

<userId>2200013812001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>ceshi0001</trnId>

<acntNo>*********0011141</acntNo>

<qryFlag>1</qryFlag>

<payChannel>3</payChannel>

<dateFrom>2015-02-01</dateFrom>

<dateTo>2018-02-12</dateTo>

88 / 149

民生银行银企直联

<startNo>1</startNo>

<queryRows>200</queryRows>

</xDataBody>

</CMBC>

响应

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="qryRemittDetail" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2018-03-13 17:56:30</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>ceshi0001</trnId>

<allAmt>10</allAmt>

<allNum>10</allNum>

<dtList>

<Map>

<transDate>********</transDate>

<serialNum>57100201503050000000001000000001</serialNum>

89 / 149

民生银行银企直联

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>村镇银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501080000001087000001087</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

90 / 149

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

民生银行银企直联

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501150000001442000001442</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

91 / 149

民生银行银企直联

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501150000001450000001450</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

92 / 149

民生银行银企直联

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501150000001452000001452</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

93 / 149

<serialNum>57100201501150000001454000001454</serialNum>

民生银行银企直联

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501150000001462000001462</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

94 / 149

民生银行银企直联

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501150000001464000001464</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

95 / 149

民生银行银企直联

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501150000001468000001468</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

96 / 149

民生银行银企直联

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

<Map>

<transDate>********</transDate>

<serialNum>57100201501190000001574000001574</serialNum>

<trsAmt>123.00</trsAmt>

<payerAcNo>*********0011141</payerAcNo>

<payerAcName>村镇银行测试账号</payerAcName>

<payerBankNo>************</payerBankNo>

<payeeAcNo>876543456787654345</payeeAcNo>

<payeeAcName>兴业银行测试账号</payeeAcName>

<payeeBankNo>************</payeeBankNo>

<msgId>****************</msgId>

<detailId></detailId>

<certtype></certtype>

<certNo></certNo>

<payChannel>3</payChannel>

<lendType>1</lendType>

<remark>附言</remark>

<remark2>附言 2</remark2>

<summary>备注</summary>

<summary2>备注 2</summary2>

</Map>

</dtList>

97 / 149

</xDataBody>

</CMBC>

民生银行银企直联

13.通过行名查询行号（单笔）(B2eBankNoSingleQr

y)

本部分更新日期:2021-04-30

通过上送行名查询出行号。同时查询大小额汇路行号和网银互联汇路行号。

当客户输入行名为准确行名（一字不差），且真实有效，会返回查询类型：1-精确查

询 ，否则为 2-模糊查询。

当客户输入行名为近似行名需模糊匹配时，返回最多 5 条相似数据，相似度从高到低排

列

请客户留意，对于返回的查询类型为模糊查询的，请知晓该服务具有一定的误差。

13.1.请求(B2eBankNoSingleQry)

标记

说明

<xDataBody>

 <trnId>

客户端的唯一标识

 <bankName>

行名

</xDataBody>

是否

长度

必输

Y

Y

64

200

98 / 149

13.2.响应(B2eBankNoSingleQry)

民生银行银企直联

标记

说明

是否必返 长度

<xDataBody>

服务消息集

<trnId>

<svrId>

<MatchResult>

<bankName>

<matchType>

客户端的唯一标识

服务器流水

查询结果

原上送行名

查询类型

1:精确查询

2：模糊查询

<resultList>

返回结果列表。

<item>

<matchBankName> 查询出的行名。

<basBankNo>

大小额汇路行号

<intBankNo>

网银互联汇路行号

</item>

</resultList>

</MatchResult>

</xDataBody>

13.3.报文示例

请求报文：

64

32

200

1

200

30

30

99 / 149

 
 
 
  
  
  
   
    
    
    
   
  
 
<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="B2eBankNoSingleQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

<bankName>工行北京顺义</bankName>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eBankNoSingleQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

100 / 149

<dtServer>2021-04-30 11:11:11</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31311121043011111050170313000000</svrId>

<trnId>111</trnId>

<MatchResult>

<bankName>工行北京顺义</bankName>

<matchType>2</matchType>

<resultList>

<item>

<matchBankName>中国工商银行股份有限公司北京顺义支行

</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国工商银行股份有限公司北京顺义支行牛栏山分

理处</matchBankName>

<basBankNo>************</basBankNo>

</item>

</resultList>

</MatchResult>

<cltcookie>2222</cltcookie>

</xDataBody>

</CMBC>

101 / 149

14.通过行名查询行号(批量)(B2eBatchBankNo)

民生银行银企直联

本部分更新日期:2021-06-22

1.通过上送行名查询出行号。同时查询大小额汇路行号和网银互联汇路行号。

2.当客户输入行名为准确行名（一字不差），且真实有效，会返回查询类型：1-精确查

询 ，否则为 2-模糊查询。

3.当客户输入行名为近似行名需模糊匹配时，返回最多 5 条相似数据，相似度从高到低排

列。

4.请客户留意，对于返回的查询类型为模糊查询的，请知晓该服务具有一定的误差。

14.1.请求(B2eBatchBankNo)

长

度

64

是否

必输

Y

Y

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识

<list>

行名列表 JSON 格式，list 中行名查询数量不超过

5000。包含 bankName 字段。示例：

[{"bankName":"中国工商"},{"bankName":"建

设银行"}]

</xDataBody>

14.2.响应(B2eBatchBankNo)

标记

说明

<xDataBody> 服务消息集

<trnId>

客户端的唯一标识

是否

长度

必返

64

102 / 149

 
 
 
标记

说明

民生银行银企直联

是否

长度

必返

<svrId>

服务器流水 返回服务流水，客户再用该流水调

32

用查询接口，获取查询结果

</xDataBody>

14.3.报文示例

请求报文：

<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eBatchBankNo">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

<list>[{"bankName":"中国工商"},{"bankName":"建设银行"}]</list>

</xDataBody>

</CMBC>

103 / 149

 
响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="B2eBatchBankNo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-04-30 11:13:13</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31311121043011131150828313000000</svrId>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

</xDataBody>

</CMBC>

15.获取行名查询行号结果（批量）(B2eQryBatchBa

nkNo)

本部分更新日期:2021-04-30

在批量查询后，调用该接口获取查询结果。

104 / 149

民生银行银企直联

是否

必输

长

度

Y

Y

64

32

15.1.请求(B2eQryBatchBankNo)

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识

<origSvrId> 查询流水

调用 B2eBatchBankNo 接口返回的服务流水号

svrId

</xDataBody>

15.2.响应(B2eQryBatchBankNo)

标记

说明

是否

长度

必返

<xDataBody>

服务消息集

<trnId>

<origSvrId>

<svrId>

客户端的唯一标识

原查询流水

服务器流水

<matchResultList>

结果列表

<item>

<bankName>

原查询行名

<matchType>

查询类型

64

32

32

200

1

105 / 149

 
 
 
 
 
 
  
   
   
标记

说明

<resultList>

查询结果

<item>

<matchBankName> 查询出的行名

<basBankNo>

大小额行号

<intBankNo>

网银互联行号

民生银行银企直联

是否

长度

必返

200

30

30

</item>

</resultList>

</item>

</matchResultList>

</xDataBody>

15.3.报文示例

请求报文：

<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatchBankNo">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

106 / 149

   
    
    
    
    
   
  
 
民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

<origSvrId>31300202104290538460010313000000</origSvrId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatchBankNo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-04-30 11:14:06</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31311121043011140651169313000000</svrId>

<trnId>111</trnId>

<origSvrId>31311121043011131150828313000000</origSvrId>

107 / 149

民生银行银企直联

<cltcookie>2222</cltcookie>

<matchResultList>

<item>

<bankName>中国工商</bankName>

<matchType>2</matchType>

<resultList>

<item>

<matchBankName>中国工商咸阳秦都支行</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国工商咸阳东风路支行

</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国工商咸阳市人民东路支行

</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国工商股份有限公司贵阳省府路支行

</matchBankName>

<basBankNo>************</basBankNo>

</item>

</resultList>

</item>

<item>

108 / 149

民生银行银企直联

<bankName>建设银行</bankName>

<matchType>2</matchType>

<resultList>

<item>

<matchBankName>中国建设银行轩岗支行</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国建设银行杜蒙支行</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国建设银行林甸支行</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国建设银行鹤北支行</matchBankName>

<basBankNo>************</basBankNo>

</item>

<item>

<matchBankName>中国建设银行桦川支行</matchBankName>

<basBankNo>************</basBankNo>

</item>

</resultList>

</item>

</matchResultList>

109 / 149

</xDataBody>

</CMBC>

民生银行银企直联

16.转账可达汇路查询(单笔)(B2eAbleRouteSingleQ

ry)

本部分更新日期:2021-06-22

1.通过上送出账和入账信息查询可达汇路。

2.该服务查询的是当前时间点可用的汇路，包括：民生行内汇路、人行大额汇路、人行小
额汇路、人行网银互联汇路。由于大额汇路不是 24 小时运行的，仅每工作日的前一
日 20:30 至工作日当日 17:15 运行，非大额汇路工作时间，不会返回大额汇路。

3.小额汇路和网银互联汇路的金额上限为 100 万元，因此超过 100 万元的不会返回该两个

汇路。

4.特殊的，如年终决算、疫情支持等，人民银行会临时调整各个汇路的工作时间和金额限

制，该服务会根据人民银行的要求同步调整。

5.支持按以下方式查询：

6.（1）付款账号+金额+收款账号+收款行号

7.（2）付款账号+金额+收款账号+收款行号+收款行名

8.（3）付款账号+金额+收款账号+收款行名

9.（4）付款账号+金额+收款账号（仅限银联卡，62 开头的 15-19 位置数字，会针对卡号

最后一位校验位检查，卡号错误时无法使用）

10.对于（2）中行号和行名同时上送的，以行号为准，行名不参与判断。

11.对于（3）仅上送行名的，系统会自动尝试匹配行号，对于较为精确，仅匹配出一个行

号的，可进行汇路计算。对于较为模糊，匹配出多个行号或者无行号，会报错：“收

款行名匹配出多个行号，请使用完整、准确的支行行名或上送支行行号。”、“收款

行名未匹配出行号，请使用完整、准确的支行行名或上送支行行号。”请客户留意，

对于客户输入行名不够详细具体的情况，请知晓该服务具有一定的误差。

12.对于（1）、（2）、（3）中上送的收款账号是银联卡号的，收款账号并不会参与判

断。仅当收款行号、行名均未上送，才会去判读账号是否为银联卡号，进而匹配出银

行。

备注：

110 / 149

民生银行银企直联

响应报文中：

1.inBankFlag=1，表示行内汇路。此时 channelList 及子节点不返回（无此节点）

2.inBankFlag=0，表示行外汇路，此时通过 channelList 子节点列表判断支持哪些汇路。可

能会出现多条，如网银互联和小额汇路同时可用。

3.使用第（3）种模式时，建议收款行名中包含支行信息，能将大/小额和网银互联均查回

来。如果收款行名只有总行名称的，建议仅使用查回的网银互联汇路，不要使用大小

额汇路，因为很多银行并不支持大/小额汇路的总行入账。

16.1.请求(B2eAbleRouteSingleQry)

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识

<payerAcNo>

付款账号

<draweeAccType>

付款账户类型

0:对公账户

<transAmount>

金额

<payeeAcNo>

收款账号

<payeeBankNo>

收款行号

<payeeBankName> 收款行名

<payeeName>

收款方户名

</xDataBody>

是否

长度

必输

Y

Y

Y

Y

Y

N

N

Y

64

40

1

20

40

30

200

200

111 / 149

 
 
 
 
 
 
 
 
16.2.响应(B2eAbleRouteSingleQry)

标记

说明

民生银行银企直联

是否

长度

必返

<xDataBody>

服务消息集

<trnId>

<svrId>

客户端的唯一标识

服务器流水

<payerAcNo>

原付款账号（同上送报文中的信息）

<draweeAccType>

原付款账户类型（同上送报文中的信

息）

<transAmount>

原金额（同上送报文中的信息）

<payeeAcNo>

原收款账号（同上送报文中的信息）

<payeeBankNo>

原收款行号（同上送报文中的信息）

<payeeBankName>

原收款行名（同上送报文中的信息）

<payeeName>

原收款方户名（同上送报文中的信

息）

64

32

40

1

20

40

30

200

200

<inBankFlag>

（1）inBankFlag=1，表示行内汇

1

路。此时 channelList 及子节点不返

回（无此节点） （2）

inBankFlag=0，表示行外汇路，此

时通过 channelList 子节点列表判断

支持哪些汇路。可能会出现多条，如

网银互联和小额汇路同时可用。

<channelList>

查询结果（如 inBankFlag 为 1 行内

112 / 149

 
 
 
 
 
 
 
 
 
 
 
标记

说明

民生银行银企直联

是否

长度

必返

汇路，则本字段 channelList 及子字

段不返回。）

<item>

如多个汇路则 item 节点有多个

<path>

小额:1238

大额:1225

网银互联:1216

<pathBankName> 汇路行名（匹配出的行名）

<pathBankCode> 汇路行号（匹配出的行号）

30

200

30

</item>

</channelList>

</xDataBody>

16.3.报文示例

请求报文：

<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eAbleRouteSingleQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

113 / 149

  
   
   
   
  
 
民生银行银企直联

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

<payerAcNo>111</payerAcNo>

<draweeAccType>0</draweeAccType>

<transAmount>111</transAmount>

<payeeAcNo>****************</payeeAcNo>

<payeeBankNo>************</payeeBankNo>

<payeeBankName>中国工商银行股份有限公司</payeeBankName>

<payeeName>李明</payeeName>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eAbleRouteSingleQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-06-17 20:12:31</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

114 / 149

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<transAmount>111</transAmount>

<inBankFlag>1</inBankFlag>

<payeeBankNo>************</payeeBankNo>

<payeeAcNo>****************</payeeAcNo>

<payeeName>李明</payeeName>

<svrId>31300202106170565190006313000000</svrId>

<draweeAccType>0</draweeAccType>

<payerAcNo>111</payerAcNo>

<payeeBankName>中国工商银行股份有限公司</payeeBankName>

<cltcookie>2222</cltcookie>

<trnId>111</trnId>

<channelList>

<item>

<path>1216</path>

<pathBankName>中国工商银行</pathBankName>

<pathBankCode>************</pathBankCode>

</item>

<item>

<path>1225</path>

<pathBankName>中国工商银行股份有限公司青岛开发区支行长江东路分

理处</pathBankName>

<pathBankCode>************</pathBankCode>

</item>

<item>

115 / 149

<path>1238</path>

<pathBankName>中国工商银行股份有限公司青岛开发区支行长江东路分

民生银行银企直联

理处</pathBankName>

<pathBankCode>************</pathBankCode>

</item>

</channelList>

</xDataBody>

</CMBC>

17.转账可达汇路查询(批量)(B2eBatchAbleRoute)

本部分更新日期:2021-06-18

1.批量根据出账和入账信息查询可达汇路，异步。本接口调用后，仅返回 svrId 字段，后

续请使用 B2eQryBatchRoute 接口，上送该 svrId，查询结果。

2.允许同时多笔查询（列表）。每笔可能会查出多个汇路。

3.该服务查询的是当前时间点可用的汇路，包括：民生行内汇路、人行大额汇路、人行小

额汇路、人行网银互联汇路。由于大额汇路不是 24 小时运行的，仅每工作日的前一

日 20:30 至工作日当日 17:15 运行，非大额汇路工作时间，不会返回大额汇路。

4.小额汇路和网银互联汇路的金额上限为 100 万元，因此超过 100 万元的不会返回该两个

汇路。

5.特殊的，如年终决算、疫情支持等，人民银行会临时调整各个汇路的工作时间和金额限

制，该服务会根据人民银行的要求同步调整。

支持按以下方式查询：

（1）付款账号+金额+收款账号+收款行号

（2）付款账号+金额+收款账号+收款行号+收款行名

（3）付款账号+金额+收款账号+收款行名

116 / 149

（4）付款账号+金额+收款账号（仅限银联卡，62 开头的 15-19 位置数字，会针对卡号最

民生银行银企直联

后一位校验位检查，卡号错误时无法使用）

6.对于（2）中行号和行名同时上送的，以行号为准，行名不参与判断。

7.对于（3）仅上送行名的，系统会自动尝试匹配行号，对于较为精确，仅匹配出一个行

号的，可进行汇路计算。对于较为模糊，匹配出多个行号或者无行号，会报错：“收

款行名匹配出多个行号，请使用完整、准确的支行行名或上送支行行号。”、“收款

行名未匹配出行号，请使用完整、准确的支行行名或上送支行行号。”

8.对于（1）、（2）、（3）中上送的收款账号是银联卡号的，收款账号并不会参与判

断。仅当收款行号、行名均未上送，才会去判读账号是否为银联卡号，进而匹配出银

行。

备注：

使用第（3）种模式时，建议收款行名中包含支行信息，能将大/小额和网银互联均查回

来。如果收款行名只有总行名称的，建议仅使用查回的网银互联汇路，不要使用大小额汇

路，因为很多银行并不支持大/小额汇路的总行入账。

17.1.请求(B2eBatchAbleRoute)

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识（★）

<list>

查询列表（★） json 格式字符串，查询列表数量

不超过 5000。 json 的格式见示例报文，要素包

括： payerAcNo-付款账号 draweeAccType-付

款账户类型 0:对公账户 transAmount-金额

payeeAcNo-收款账号 payeeBankNo-收款行号

payeeBankName-收款行名 payeeName-收款

户名

是否

长度

必输

64

Y

Y

117 / 149

 
 
标记

说明

</xDataBody>

17.2.响应(B2eBatchAbleRoute)

标记

说明

<xDataBody> 服务消息集

<trnId>

客户端的唯一标识

<svrId>

服务器流水 客户在批量查询可达汇路后，用该流

水调用查询接口获取结果

民生银行银企直联

是否

长度

必输

是否

长度

必返

64

32

</xDataBody>

17.3.报文示例

请求报文：

<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eBatchAbleRoute">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

118 / 149

 
 
民生银行银企直联

</requestHeader>

<xDataBody>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

<list>

[

{

"payerAcNo" : "*************",

"draweeAccType" : "0",

"transAmount" : "111",

"payeeAcNo" : "****************",

"payeeBankNo" : "************",

"payeeBankName" : "111"

,

"payeeName":"张三"

},

{

"payerAcNo" : "*************",

"draweeAccType" : "0",

"transAmount" : "111",

"payeeAcNo" : "****************",

"payeeBankNo" : "************",

"payeeName":"张三"

}

]

</list>

</xDataBody>

</CMBC>

119 / 149

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eBatchAbleRoute">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-04-30 11:16:35</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31311121043011163452072313000000</svrId>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

</xDataBody>

</CMBC>

18.获取转账可达汇路查询结果(批量)(B2eQryBatchR

oute)

本部分更新日期:2021-06-22

在调用查询汇路接口 B2eBatchAbleRoute 后，待用该接口获取结果。

120 / 149

本接口使用时，应当先判断 ableRouterList 中，每一个查询条码的 status 字段，当为

S 时，再获取 inBankFlag 字段判断是否为行内汇路。如 inBankFlag 字段为行外汇路，

民生银行银企直联

再获取 chnnelList。

18.1.请求(B2eQryBatchRoute)

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识

<origSvrId> 查询流水 B2eBatchAbleRoute 返回的服务流水号

svrId

</xDataBody>

18.2.响应(B2eQryBatchRoute)

标记

说明

<xDataBody>

服务消息集

<trnId>

<origSvrId>

<svrId>

客户端的唯一标识

原查询流水

服务器流水

<ableRouterList>

查询结果列表

<item>

<payerAcNo>

原付款账号

是否

长度

必输

Y

64

32

是否

长度

必返

64

32

32

30

121 / 149

 
 
 
 
 
 
  
   
标记

说明

民生银行银企直联

是否

长度

必返

1

20

40

30

200

200

1

<draweeAccType>

原付款账户类型

<transAmount>

原金额

<payeeAcNo>

原收款账号

<payeeBankNo>

原收款行号

<payeeBankName>

原收款行名

<payeeName>

原收款方户名

<inBankFlag>

inBankFlag=1，表示行内汇路。此

时 channelList 及子节点不返回（无

此节点） inBankFlag=0，表示行

外汇路，此时通过 channelList 子

节点列表判断支持哪些汇路。可能会

出现多条，如网银互联和小额汇路同

时可用。

<status>

该笔查询状态(S:成功，F:失败)，标

1

识该笔是否查回结果。 本接口使用

时，应当先判断本字段，仅为 S 时，

再获取 inBankFlag 字段。如为行外

汇路，再获取 chnnelList。

<message>

该笔查询失败时的错误信息

300

<channelList>

查回的汇路列表。

<item>

<path>

汇路

30

122 / 149

   
   
   
   
   
   
   
   
   
   
    
     
标记

说明

民生银行银企直联

是否

长度

必返

小额:1238

大额:1225

网银互联:1216

<pathBankName>

汇路行名

<pathBankCode> 汇路行号

200

30

</Map>

</channelList>

</item>

</ableRouterList>

</xDataBody>

18.3.报文示例

请求报文：

<?xml version="1.0" encoding="UTF-8" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatchRoute">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

123 / 149

     
    
   
  
 
民生银行银企直联

</requestHeader>

<xDataBody>

<trnId>111</trnId>

<cltcookie>2222</cltcookie>

<origSvrId>31300202104290538460012313000000</origSvrId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatchRoute">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2021-07-08 15:20:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31311202107081582346726313000000</svrId>

<ableRouterList>

<item>

<payerAcNo>*************</payerAcNo>

124 / 149

民生银行银企直联

<transAmount>111</transAmount>

<payeeBankNo>************</payeeBankNo>

<payeeBankName>111</payeeBankName>

<payeeName>张三</payeeName>

<inBankFlag>1</inBankFlag>

<status>S</status>

<channelList />

</item>

<item>

<payerAcNo>*************</payerAcNo>

<transAmount>111</transAmount>

<payeeBankNo>************</payeeBankNo>

<payeeName>张三</payeeName>

<inBankFlag>0</inBankFlag>

<status>S</status>

<channelList>

<item>

<path>1216</path>

<pathBankName>中国工商银行</pathBankName>

<pathBankCode>************</pathBankCode>

</item>

<item>

<path>1225</path>

<pathBankName>中国工商银行股份有限公司青岛开发区支行长江东路

分理处</pathBankName>

<pathBankCode>************</pathBankCode>

</item>

125 / 149

<item>

<path>1238</path>

民生银行银企直联

<pathBankName>中国工商银行股份有限公司青岛开发区支行长江东路

分理处</pathBankName>

<pathBankCode>************</pathBankCode>

</item>

</channelList>

</item>

</ableRouterList>

<trnId>CMBCTRN202107081508027</trnId>

<origSvrId>31311202107081582346653313000000</origSvrId>

<cltcookie>2222</cltcookie>

</xDataBody>

</CMBC>

19.借记卡有效性校验 (QryAcctStatus)

本部分更新日期:2021-04-02

通过卡号和客户姓名，查询卡或折的有效性。

参照已有的银企直联数据接口规范，本接口规则为：

卡或折正常状态返回 1，状态说明为 OK；卡或折状态异常，则状态返回 0，状态说明返

回异常说明。

19.1.请求(QryAcctStatus)

标记

说明

是否

长度

必输

126 / 149

  
民生银行银企直联

是否

长度

必输

Y

Y

Y

Y

64

20

32

20

是否

长度

必返

64

1

标记

说明

<xDataBody>

<trnId>

客户端产生的交易唯一标志（必输，但无作

用）

<acntToNo>

收款账号或卡号（目前只支持人民币账号）

<acntToName> 收款账户名称

<acntNo>

付款账户

</xDataBody>

19.2.响应(QryAcctStatus)

标记

说明

<xDataBody>

<trnId>

原值返回

<statusCode> 账户状态 (1、正常；0、异常)

<statusMsg> 状态说明，卡状态正常时为 ok

</xDataBody>

19.3.报文示例

请求报文：

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryAcctStatus">

127 / 149

  
 
 
 
 
  
 
 
 
民生银行银企直联

<requestHeader>

<dtClient>2016-03-08 10:55:07</dtClient>

<clientId>2200002220</clientId>

<userId>2200002220001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>n3201d08</trnId>

<acntToNo>*********</acntToNo>

<acntToName>测试 2203120</acntToName>

<acntNo>60003123</acntNo>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryAcctStatus" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-05-26 14:19:21</dtServer>

128 / 149

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>n3201d08</trnId>

<statusCode>1</statusCode>

<statusMsg>ok</statusMsg>

</xDataBody>

</CMBC>

20.行名行号信息查询(qryAllBankCode)

本部分更新日期:2021-04-02

根据用户输入的行名查询该行名的行号； 查询笔数最大为 10000 笔

20.1.请求(qryAllBankCode)

标记

说明

<xDataBody>

<insId>

<localFlag>

流水号

汇路：

2:小额;

3 大额;(大小额汇路行名行号信息相同)

4:上海同城;

5:网银互联

是否

长度

必输

129 / 149

  
 
 
民生银行银企直联

是否

长度

必输

标记

说明

<startNo>

开始笔数(开始为 1)

<queryRows> 查询笔数(最大为 10000)

</xDataBody>

20.2.响应(qryAllBankCode)

标记

说明

是否

长度

必返

<xDataBody>

服务消息集

<insId>

流水号

<localFlag>

汇路:请求时的汇路

<totalNum>

总条数

<bankinfoList> 行名行号信息列表开始

<bankinfo>

0..n

<bankCode> 行号

<bankName> 行名

</bankinfo>

</bankinfoList> 列表结束

</xDataBody>

130 / 149

  
 
 
  
 
 
 
 
  
  
  
  
 
民生银行银企直联

20.3.报文示例

请求报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="qryAllBankCode">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********06</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>************</insId>

<localFlag>5</localFlag>

<startNo>1</startNo>

<queryRows>3</queryRows>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="qryAllBankCode" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

131 / 149

民生银行银企直联

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2015-04-08 10:22:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<insId>************</insId>

<localFlag>5</localFlag>

<totalNum>83</totalNum>

<bankinfoList>

<bankinfo>

<bankCode>************</bankCode>

<bankName>平安银行有限责任公司</bankName>

</bankinfo>

<bankinfo>

<bankCode>************</bankCode>

<bankName>上海结算中心</bankName>

</bankinfo>

<bankinfo>

<bankCode>************</bankCode>

<bankName>中国建设银行股份有限公司上海市分行</bankName>

</bankinfo>

132 / 149

</bankinfoList>

</xDataBody>

</CMBC>

民生银行银企直联

21.单笔转账(支持行号匹配)(SingleXfer)

本部分更新日期:2021-12-16

支持本行账户和授权账户进行跨行对公、同行对公交易，不支持本他行对私转账；由于跨

行转账是异步处理操作，无法即时得出最终的转账结果,所以进行跨行转账交易时需要通

过 qryXfer 接口进行对账,得出人行最终转账结果;

21.1.请求(SingleXfer)

标记

说明

<xDataBody>

是否

长度

必输

<trnId>

客户端产生的交易唯一标志

Y

64

必输，原值返回，数字字母组成；可做为客户端

自己的交易标识

<insId>

指令 ID，一条转账指令在服务端的唯一标识

Y

64

必输，不能重复；原值返回，数字字母组成

<acntNo>

付款账号

<acntName>

付款人名称

<acntToNo>

收款账号

<acntToName> 收款人名称

<amount>

转账金额

Y

N

Y

Y

Y

32

60

32

60

15,2

133 / 149

 
 
 
 
 
 
 
标记

说明

<explain>

摘要/用途

本行对公、跨行对公时，摘要用途不得包含

“|”字符

<certNo>

企业自制凭证号

8 位以内（包含）的数字

<localFlag>

汇路

2：小额汇路

3：大额汇路

5：网银互联

民生银行银企直联

是否

长度

必输

N

50

N

N

8

1

<bankCode>

收款人开户行行号

N

12

如果汇路输入为空，输入此字段，则汇路以此为

准，行名必输但不校验

<bankName>

收款人开户行名称

N

80

跨行必填项 填写规则为开户行全称（不支持模

糊匹配） 如：中国民生银行股份有限公司北京

木樨地支行

<extendReq1> 备用字段（未启用）

<extendReq2>

<extendReq3>

</xDataBody>

134 / 149

 
 
 
 
 
 
 
 
21.2.响应(SingleXfer)

民生银行银企直联

标记

说明

是否必

长度

<xDataBody>

服务消息集

<trnId>

客户端交易的唯一标志

原值返回

<svrId>

交易流水号

只有跨行交易的大小额汇路返回，每笔交易的

该流水号唯一

<insId>

指令 ID，请求时给出的 ID

原值返回

<bankCode>

收款人开户行行号

<bankName>

收款人开户行行名

<localFlag>

汇路

2：小额汇路

3：大额汇路

5：网银互联

<extendRep1> 备用字段（未启用）

<extendRep2>

<extendRep3>

</xDataBody>

21.3.报文示例

请求报文：

输

Y

N

Y

N

N

N

64

32

64

64

255

1

135 / 149

 
 
 
 
 
 
 
 
 
 
<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="SingleXfer">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********812</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<insId>20200313aiqiyi0017</insId>

<acntNo>*********</acntNo>

<acntName>H5 现金宝测试 02</acntName>

<acntToNo>****************</acntToNo>

<acntToName>花无缺 1</acntToName>

<localFlag></localFlag>

<bankCode></bankCode>

<bankName>中国工商银行股份有限公司北京通州支行</bankName>

<amount>12.31</amount>

<explain>平台付款</explain>

<certNo>********</certNo>

<actDate>2008-03-28</actDate>

</xDataBody>

136 / 149

</CMBC>

响应报文：

民生银行银企直联

<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="SingleXfer" security="none" lang="chs" header="100"

version="100" >

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2020-03-13 17:49:58</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<transfer>

<trnId>********</trnId>

<svrId>20200313178157397500</svrId>

<insId>20200313aiqiyi0019</insId>

<bankName>中国工商银行</bankName>

<bankCode>************</bankCode>

<localFlag>5</localFlag>

</transfer>

</xDataBody>

</CMBC>

137 / 149

22.银企直联公钥查询(queryPublicKey)

民生银行银企直联

本部分更新日期:2022-06-02

本接口用于查询目前可用的银行公钥

22.1.请求(queryPublicKey)

标记

说明

<xDataBody>

是否

长度

必返

<trnId>

客户端产生的交易唯一标志（必输，但无作用）

<insId>

指令 ID，一条转账指令在客户端的唯一标识

Y

64

64

<cltcookie> 可选，客户端 cookie，响应时原值返回

</xDataBody>

22.2.响应(queryPublicKey)

标记

说明

<xDataBody>

<trnId>

<insId>

客户端产生的交易标志，原值返回

原值返回

<cltcookie>

可选，客户端 cookie，响应时原值返回

<pubKeyVersion> 公钥索引（版本）

<pubKey>

公钥

是否

长度

必返

64

64

2

138 / 149

  
 
 
 
  
  
 
 
 
 
 
民生银行银企直联

是否

长度

必返

标记

说明

</xDataBody>

22.3.例子

请求报文：

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="queryPublicKey">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>20**********</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>2015120800015505</trnId>

<insId>201512070ffdvv7</insId>

<cltcookie></cltcookie>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

139 / 149

  
  
<CMBC trnCode="queryPublicKey" security="none" lang="chs"

header="100" version="100" >

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2022-05-31 20:44:22</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>2015120800015505</trnId>

<insId>201512070ffdvv7</insId>

<cltcookie></cltcookie>

<pubKeyVersion>1</pubKeyVersion>

<pubKey>MIIDJjCCAsugAwIBAgIFMBeHSBgwDAYIKoEcz1UBg3UFADA

rMQswCQYDVQQGEwJDTjEcMBoGA1UECgwTQ0ZDQSBTTTIgVEVTVCBPQ0EyM

TAeFw0yMjA1MTYwNzU0MDFaFw0yNzA1MTYwNzU0MDFaMHIxCzAJBgNVBAYT

AkNOMREwDwYDVQQKDAhPQ0EyMVNNMjEQMA4GA1UECwwHVEVDVGVzdDE

ZMBcGA1UECwwQT3JnYW5pemF0aW9uYWwtMTEjMCEGA1UEAwwaT0NBMjFA

Z29uZ3ppZGFpZmFAWloxMTExQDEwWTATBgcqhkjOPQIBBggqgRzPVQGCLQN

CAATbWOxyWyvpejyzT9S/ZKFEXQJsWXvc/kZXMjh/nu/m/0VZHTDQ4ypgRg2Q

CqPk2pJ5JRicWtwAwYtz2g7E8G/do4IBkTCCAY0wHwYDVR0jBBgwFoAU4n62EL

uU6xXmrtEVCv/o16BXOZ0wSAYDVR0gBEEwPzA9BghggRyG7yoCAjAxMC8GC

CsGAQUFBwIBFiNodHRwOi8vd3d3LmNmY2EuY29tLmNuL3VzL3VzLTEzLmh0b

TCB1AYDVR0fBIHMMIHJMC+gLaArhilodHRwOi8vMjEwLjc0LjQyLjMvT0NBMjEvU

00yL2NybDIzMzQwLmNybDCBlaCBkqCBj4aBjGxkYXA6Ly8yMTAuNzQuNDIuMT

A6Mzg5L0NOPWNybDIzMzQwLE9VPVNNMixPVT1DUkwsTz1DRkNBIFNNMiBUR

140 / 149

民生银行银企直联

VNUIE9DQTIxLEM9Q04/Y2VydGlmaWNhdGVSZXZvY2F0aW9uTGlzdD9iYXNlP2

9iamVjdGNsYXNzPWNSTERpc3RyaWJ1dGlvblBvaW50MAsGA1UdDwQEAwID+

DAdBgNVHQ4EFgQUEcQMOKsVaq3YNWVjqngSqnav9SUwHQYDVR0lBBYwFAYI

KwYBBQUHAwIGCCsGAQUFBwMEMAwGCCqBHM9VAYN1BQADRwAwRAIgOntA

aT4CrK6cKuSPGEaL4Guq6u17o2gw9vyuEH419xsCIGHd6S7RdqQCft/6iKEazI2

baEdAL1C7xxv+Azuiyozi</pubKey>

</xDataBody>

</CMBC>

141 / 149

