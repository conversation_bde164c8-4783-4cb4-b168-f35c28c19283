银企直联接口文档

（财富管理-流动利）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 7

目录

民生银行银企直联

目录 .............................................................................................2

1. 流动利 C 明细余额查询(ENTFLOWQRYB2E) ......................................... 3

1.1. 请求（ENTFLOWQRYB2E） .......................................................... 3

1.2. 响应（ENTFLOWQRYB2E） .......................................................... 4

1.3. 例子 ...................................................................................... 4

2 / 7

1.流动利 C 明细余额查询(EntFlowQryB2e)

民生银行银企直联

本部分更新日期:2021-04-02

根据查询日期和查询标志查询流动利 C 的历史明细和余额。

*流动利 B/C 目前已暂停新签约

该功能用于查询流动利 C 增值余额信息。即，查询账户的流动利增值汇总金额、增值

余额明细。

流动利 C 提供 人民币 1 天和 7 天、3 个月、半年、一年增值服务。

【业务规则】币种人民币，最低起存金额 1 万，最低留存金额 0 万，我行挂牌利率，

按季付息。

【收益计算规则】

1.小于 7 天，按 1 天通知存款计息；

2.大于等于七天，小于三个月，分段计息：7 天通知存款+1 天通知存款利息计息；

3.大于等于三个月，小于六个月的，分段计息：3 个月定期+7 天通知存款+1 天通知

存款利息计息；

4.大于等于六个月，小于一年的，分段计息：6 个月定期+7 天通知存款+1 天通知存

款利息计息；

5.满一年，则按 1 年定期存款计息。

1.1.请求（EntFlowQryB2e）

  标记

说明

<xDataBody>

 <acNo>

账号

 <qryDate>

查询日期（yyyy-MM-dd）

 <qryFlag>

查询标志非必输，默认 3:查询购买明细以及历史余额信息

</xDataBody>

长度

3 / 7

民生银行银企直联

长度

1.2.响应（EntFlowQryB2e）

  标记

说明

<xDataBody>

 <AcNo>

账号

 <TotalRow>

总条数

 <AcName>

账户名称

 <Currency>

币种

 <Type>

签约产品类型

 <ActBalance>

账户余额

 <AddPrcpSum> 增值总本金

 <AvlBalance>

可用余额

 <OutEtdData> 扩展字段

 <List>

  <DateValue> 日期

  <AddPrcp>

增值本金

 </List>

</xDataBody>

1.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

4 / 7

trnCode="EntFlowQryB2e">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

民生银行银企直联

<clientId>2000072311</clientId>

<userId>200007231102</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>805312241sdf</trnId>

<insId>1146531612</insId>

<acNo>*********</acNo>

<qryDate>2015-03-25</qryDate>

<qryFlag>3</qryFlag>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="EntFlowQryB2e" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2015-03-30 09:18:01</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<AcNo>*********</AcNo>

<TotalRow>1</TotalRow>

<AcName>司法查控 UAT 流动利 B 外币账户</AcName>

5 / 7

民生银行银企直联

<Currency>USD</Currency>

<Type>流动利 B</Type>

<ActBalance>80000.00</ActBalance>

<AddPrcpSum>520077.24</AddPrcpSum>

<AvlBalance>600077.24</AvlBalance>

<OutEtdData></OutEtdData>

<List>

<Map>

<DateValue>2015-03-23</DateValue>

<AddPrcp>520077.24</AddPrcp>

</Map>

</List>

</xDataBody>

</CMBC>

6 / 7

