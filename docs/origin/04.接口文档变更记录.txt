民生银行银企直联

银企直联接口文档

（接口文档变更记录）

邮箱：<EMAIL>

1 / 24

【2021-05-10】 V5.0

民生银行银企直联

在原接口文档《银企直联接口格式建议 4.0.8》的基础上升级，将接口按场景划分整理，

完善转账类接口说明。形成 5.0 版本。

【2021-07-20】 V5.1

1．查询票据信息(QryDraftInfo)新增查询条件 billNo，添加说明 billNo 和 rgctId

不可同时为空；

2．查询可背书的票据(QryEndorseableDraft)返回列表新增出参 protEndors 背面

转让标志、curStatusName 票据状态；

3．他行账户代发(otherBankAgentPay) 新增用途代码及含义；

4．调整境内外币支付(B2ECnForeExchPay)、被银行退回的申请删除

(B2eForByExchDecionDel)、根据币种查询结算账户信息

(B2eOpenCertAcInfoByCurrency)；

5．银企直联智能账簿间调账 (B2EVirtualAcctAdjust)增加交易日期字段；

6．客户账单回单下载(b2eElectNoteDownLoadNew)增加示例报文；

7．增加行名行号查询及转账可达汇路查询相关接口，包括：

8．通过行名查询行号(单笔）(B2eBankNoSingleQry)

9．转账可达汇路查询(单笔)(B2eAbleRouteSingleQry)

10．通过行名查询行号(批量)(B2eBatchBankNo)

11．获取行名查询行号结果(批量）(B2eQryBatchBankNo)

12．转账可达汇路查询(批量)(B2eBatchAbleRoute)

13．获取转账可达汇路查询结果(批量)(B2eQryBatchRoute)

【2021-10-09】V5.2

1.交易明细查询(qryTrsDtl) 接口记账流水号字段长度修改为 32 位。

2 / 24

民生银行银企直联

2.新增交易明细对账单 PDF 下载(ElectNotePrint)接口。

3.完善汇出汇款（境内外币）相关接口 B2ECnForeExchPay、

B2eGetAccountType、B2eForByExchDecion、

B2eQryCnForeExchPayState、B2eTerritoryForeignQry、

B2ePeBankCodeQuery、B2eSingleActBalQry、

B2eOpenCertAcInfoByExpensePayAct、B2eOpenCertAcInfoByCurrency、

B2eGetCifForeignInfo。

4.余额查询交易（新）(qryBalNew)，交易明细查询(qryTrsDtl) 接口要查询的账号修改

为可以是非人民币账号。

5.转账汇款-基金垫资转账、转账汇款-代理支付、转账汇款-委托代扣的接口删除。

【2021-10-27】V5.3

1.电子票据-新增票据信息(新增出票)(IssueDraftAdd)接口新增入参 isReturnRgctId

（非必输）及描述，新增出参 rgctId 及描述。

2.电子票据-票据交易状态查询(qryDraftStatus)接口新增入参 statusType（非必输）

及描述，当且仅当 statusType=1 时，返回的交易状态 operStatus 为票交所码

值。

3.电子票据-可质押票据查询(QryPledgeDraft)接口删除。

4.完善大额存单相关接口大额存单实时购买(B2eDepositRealtimeBuy)、大额存单提前

支取(B2eDepositRealtimeSell)、银企直连产品说明书查询

(B2eQueryCdPrdManual)、大额存单产品列表查询(B2eQueryDepositList)。

5.现金管理-集团账户服务 增加 授权关系总览(QryAuthRelation)、集团对账授权关系总

览(QryBlocAuthRelation) 、客户授权业务信息查询(QryAuthCifRelationInfo) 。

6.财富管理-银证银期--证券资金账户余额查询(QryFundAccBalInfo)响应字段 availBal

的说明改为 可用余额。

7.财富管理-通知存款--定期存款列表查询(queryRegularList)响应字段 openDate 和

dueDate 的长度都改为 8。

3 / 24

【2022-01-12】V5.4

民生银行银企直联

1.现金管理-智能账簿-智能账簿批量产品账号明细查询(B2EVirtualBatchDtlQry)接口修

改接口用例。

2.转账汇款-转账基础服务-单笔费用报销(CostReimb)请求字段 explain 的说明修改为

常用用途代码|备注，备注可不输入，用途代码中文转义+备注长度不得超过 50，例

如：“311” 或者“311|测试”（★），长度修改为 50。

3.转账汇款-转账基础服务--操作子公司账户单笔费用报销(CostReimbGroup)请求字段

explain 的说明修改为 常用用途代码|备注，备注可不输入，用途代码中文转义+备

注长度不得超过 50，例如：“311” 或者“311|测试”（★），长度修改为 50。

4.账户管理-余额明细-定期账户明细查询交易(depositAccountDetail) 响应字段

CurrencyNo 货币代号增加 50-新西兰元。

5.现金管理-现金池增加币种对应货币代号。

6.财富管理-定期存款-定期存款列表查询(queryRegularList)增加 可以查询外币定期账

户。

7.财富管理-定期存款-定期转活期交易(QueryRegularB2D)增加 支持外币定期转活期。

8.转账汇款-转账基础服务- 批量费用报销 (batchCostReimb)接口修改接口用例。

9.财富管理-通知存款- 新通知存款通知(DepositNoticeNew)增加 暂不支持外币。

10. 财富管理-大额存单-持有大额存单查询(QryFincDtl)接口响应字段 IncomeRate 浮

动盈亏 删除。

11. 转账汇款-转账基础服务-单笔转账交易(Xfer)请求字段 bankName 的说明 ：收款人

开户行名称（★）改为 收款人开户行名称（★）（大/小额、网银互联汇路此项必

填）。

12. 转账汇款-转账基础服务-批量付款（batchXfer）的支付文件数据格式 fileContent

中收款方手机号和收款方 email 地址的说明：手机号必须是 134——139 改为 手机

号必须是 11 位。

4 / 24

13. 转账汇款-转账基础服务-批量付款对账（qryBatchXfer）、批量费用报销查询

（qryBatchCostReimb）、批量费用报销、代发工资查询

（qryBatchCostReimbNew）响应字段中的 statusCode 说明：0 受理成功 改为

民生银行银企直联

0-受理成功，2-审批未通过，4-等待审批中。

14. 转账汇款-转账基础服务-总公司以子公司名义付款交易(topXfer)请求字段

bankName 的说明：收款人开户行名称（★）改为 收款人开户行名称。

15. 现金管理-智能账簿批量转账(B2EVirtualBatchXfer)的支付文件数据格式

fileContent 中汇路的说明：删除 3 网银互联。

16. 汇出汇款 新增 汇出汇款 -跨境外币、汇出汇款 -跨境人民币 接口。

【2022-02-16】V5.5

新增薪福通接口，包括：

1.签约账号列表查询(B2eQryCustomerSignInfoList)

2.项目子账簿与特殊子账簿列表查询(B2eQryAllVirtualAcNoBySignNo)

3.项目子账簿列表查询(B2eGetVirtualAcNoInfoBySignNo)

4.子账簿余额查询(B2eQrySalaryVacctBalance)

5.子账簿新增(B2eQrySalaryVacctBalance)

6.子账簿销户(B2eVirtualAccountCancel)

7.子账簿修改(B2eVirtualAccountModify)

8.子账簿批量出金(B2eSalaryMgnBatchTransfer)

9.子账簿单笔出金(B2eSalaryMgnTransfer)

10. 子账簿间转账(B2eVirtualAccountEachTransfer)

11. 子账簿交易明细查询(B2eVirtualAccountTransferDetail)

12. 子账簿批量出金结果查询(B2eQrySalaryMgnBatchList)

13. 子账簿批量转账明细(B2eQrySalaryMgnBatchTransferDetail)

5 / 24

14. 待清分账簿入账明细(B2eQryClarifyVacctNoTransferDetail)

15. 子账簿调账(B2eVirtualAccountReconciliation)

民生银行银企直联

【2022-03-18】V5.6

1.票据管家-票据签约信息查询(QrySignBillMassage)接口，新增 partner、

isSbuCompany、signId、parentCompanyCustNo、

parentCompanyName、marginAccount、billDepositAccount、

discountProportion 等字段返回

2.票据管家-额度查询（QueryBillLimitAmount）接口，新增 useLimitAmount 字段返

回

3.票据管家-额度维护分配(BillGroupLimitDistribute)接口，集团限额数值与集团限额

百分比不可同时存在，但必输其中一个 注意：这些字段默认不返回，需要单独向银

行申请添加白名单。

4.新增理财持仓查询接口，包括：

持有理财查询(B2eQueryMyFinance)

理财产品历史收益查询(B2eQryFinanceIncomeRateHis)

理财产品基本信息查询(B2eQueryPrdBuyInfo)

理财历史收益明细查询(B2eQueryMyFinanceHis)

理财持有产品详细信息查询(B2eQueryPrdDetailInfo)

理财文件下载(B2eQryEntPrdProtocol)

【2022-05-16】V5.7

6 / 24

转账汇款-转账基础服务-总公司以子公司名义付款交易(topXfer)接口，使用场景不支持

民生银行银企直联

“本行对私”

【2022-08-11】V5.8

1.QryDraftInfo 支持查询授权

2.账户增加持有票据查询接口（支持授权账户查询）

【2022-09-13】V5.9

1.薪福通-子账簿批量出金(B2eSalaryMgnBatchTransfer)接口，增加出参字段

ApplyId，该字段为发起批量服务的后台请求流水; 入参字段去除 FileName 字段；

FileNameContent 的上送格式有加密保文改为明文格式。

2.08.账户管理-余额查询银企直联明细下载(新)(trsDtlDownLoad) 接口，响应报文示例

修改，dataStream 字段中增加“记账流水号”示例。

3.07.转账汇款-他行账户代发 他行账户代发(otherBankAgentPay) 接口，响应报文字

段 otherBankBatchAgent 修正为 otherBankAgentPay。

【2022-09-16】V5.9.1

1.05.转账汇款-转账基础服务-批量费用报销 (batchCostReimb)接口。增加

secKeyEnc、secKeyIndex、fileContentType 字段。

2.05.转账汇款-转账基础服务-批量费用报销 (batchCostReimbNew)接口。增加

secKeyEnc、secKeyIndex、fileContentType 字段。增加加密流程说明。

3.07.转账汇款-他行账户代发-他行账户代发(otherBankAgentPay) 接口。增加

secKeyEnc、secKeyIndex、fileContentType 字段。

7 / 24

4.05.转账汇款-转账基础服务-批量费用报销查询(qryBatchCostReimb)接口。增加

secKeyEnc、secKeyIndex、fileContentType 字段。增加加解密流程说明。

民生银行银企直联

5.05.转账汇款-转账基础服务-批量费用报销查询(qryBatchCostReimbNew)接口。增

加 secKeyEnc、secKeyIndex、fileContentType 字段。增加加解密流程说明。

6.14.电子票据-查询票据信息(QryDraftInfo)接口。增加 fromAcc 背书人账号（白名单

客户返回）字段、toBankNo 被背书人行号（白名单客户返回）字段、toAcct 被背

书人账号（白名单客户返回）字段、被背书人行号（白名单客户返回）字段。

7.07.转账汇款-他行账户代发-他工资查询(qryOthBankAgentPayRes) 接口。

fileContent 字段修改；竖线“|”分割数据元素，以尖号“^”为数据行分割符，具体格

式定义与付款类型有关 该字段根据客户上送的 fileContentType 而定，如客户在

fileContentType 中要求银行以加密的方式返回，同时上送了 secKeyEnc 和

secKeyIndex，则银行将先解密出客户的一次性会话密钥，并将明细内容加密并转

换为 base64 编码后返回给客户。加密算法：SM4，加密模式：ECB。增加加解密

流程说明。

8.05.转账汇款-转账基础服务，新增银企直联公钥查询(queryPublicKey)接口；本接口

用于查询目前可用的银行公钥。

9.05.转账汇款-转账基础服务 ，新增定期转活期交易(QueryRegularB2D)接口；业务逻

辑： 本功能可以将定期存款账户里的定期存款转回活期账户。

10. 05.转账汇款-转账基础服务-单笔转账交易(Xfer)接口，localFlag 字段汇路修改；汇

路（★） 本行转账：0<externBank>需输 0、跨行转账-小额支付系统：

2<externBank>需输 1、跨行转账-大额支付系统：3<externBank>需输 1、跨

行转账-网银互联：5<externBank>需输 1、跨行转账-自动计算汇路：

9<externBank>需输 1。需注意：必须提供完整的行名行号、汇路 2、5 最多支持

100 万元、汇路 2、5 最多支持 100 万元，7 乘 24 小时运行、汇路 3 金额无上

限，工作时间：工作日的前一日 20:30 至工作日当日的 17:15、当选择汇路 9，如

bankCode 字段上送收款行总行行号，则无法使用大额、小额汇路，仅会尝试使用

网银互联汇路；如 bankCode 字段上送收款行支行行号，则会在大额、小额、网银

互联三个汇路中选中当前时间、金额可达的最优汇路。bankCode 字段收款人开户

行行号（是否跨行标志<externBank>选择 1 时必填）。bankName 字段收款人

8 / 24

开户行名称（★）。 响应报文，增加 route 字段；小额支付系统：2 、大额支付系

统：3、网银互联：5。注意：本字段仅当<localFlag>上送 9 且交易正常时返回，

民生银行银企直联

否则不返回本字段。

【2022-09-16】V5.9.2

05.转账汇款-转账基础服务-单笔转账交易(Xfer)接口。bankName 字段改回收款人开户

行名称（★）(大/小额、网银互联汇路此项必填)

【2022-09-20】V5.9.3

28.汇出汇款-境内外币-根据币种查询结算账户信息(B2eOpenCertAcInfoByCurrency)

接口。请求报文增加(ProductId)产品 id（非必输）、(AcFieleName)字段名（非必输）

字段。 查询结算账户信息(B2eOpenCertAcInfoByExpensePayAct)接口。请求报文增

加(ProductId)产品 id（非必输）、(AcFieleName)字段名（非必输） 字段。

29.汇出汇款-跨境外币-根据币种查询结算账户信息(B2eOpenCertAcInfoByCurrency)

接口。请求报文增加(ProductId)产品 id（非必输）、(AcFieleName)字段名（非必输）

字段。 查询结算账户信息(B2eOpenCertAcInfoByExpensePayAct)接口。请求报文增

加(ProductId)产品 id（非必输）、(AcFieleName)字段名（非必输） 字段。

30.汇出汇款-跨境人民币-根据币种查询结算账户信息

(B2eOpenCertAcInfoByCurrency)接口。请求报文增加(ProductId)产品 id（非必

输）、(AcFieleName)字段名（非必输） 字段。 查询结算账户信息

(B2eOpenCertAcInfoByExpensePayAct)接口。请求报文增加(ProductId)产品 id

（非必输）、(AcFieleName)字段名（非必输） 字段。

【2022-09-23】V5.9.4

1.现金管理-智能账簿-银企直联智能账簿管理(B2EVirtualAcctMngt)。请求报文增加

（AsEnableFlag：账簿状态(0：启用,1：禁用），非必输）字段。

9 / 24

民生银行银企直联

2.现金管理-智能账簿-新增银企直连智能账簿子账簿权限维护

(B2EOperatorAuthVirtual)接口

3.银企直联智能账簿转账(B2EVirtualAcctTransToEntAcct)接口，请求报文

（PayeeAcType）字段（非必输），收款方类型 将 状态 1：对私删除。

【2022-09-29】V5.9.5

新增银企直连现金池-现金池上存下拨交易状态查询-

B2ECashPoolQueryCollectAllocateStat。通过交易流水查询交易的状态。

【2022-10-09】V5.9.6

1.现金管理-现金池-现金池上存下拨交易状态查询

(B2ECashPoolQueryCollectAllocateStat)接口，返回报文增加状态枚举值。交易

状态:A-交易成功、B-等待柜面审批、C-柜面审批拒绝、D-结果未知，待查询、E-交

易失败。

2.财富管理-定期存款-定期转活期交易(QueryRegularB2D)接口 。在转账汇款目录下删

除该接口。在定期存款目录下将该接口更新，通知存款目录下应增加该接口。

3.电子票据- 持有票据查询(QueryGroupHoldingBills) ，“持有票据查询接口（支持授

权账户查询） ”的描述改为”支持查询客户持有的票据”。

4.转账汇款-转账基础服务银企直联公钥查询(queryPublicKey) ，文档发送请求标题和

结果返回标题修正。改为“queryPublicKey”。

【2022-10-10】V5.9.7

账户管理-回单-客户账单回单下载（支持最多 3 个同时下载）

(b2eElectNoteDownLoadNew)接口中，返回字段“FileContent”说明改为：经

Base64 加密之后的字符串，在使用时应先解码为字节数组并写入文件可使用：

10 / 24

org.apache.commons.codec.binary.Base64;java.util.Base64 等类似功能的库进

民生银行银企直联

行处理。

【2022-10-13】V5.9.8

电子票据-提示承兑的服务(AcceptDraft)接口，接口说明“收票人提示承兑人进行到期承

兑的服务” 改为 “出票人提示承兑人进行到期承兑的服务”。

【2022-10-20】V5.9.9

1.05.转账汇款-转账基础服务-1.8. 单笔转账交易结果查询(qryXfer)。在报文示例中，返

回报文增加了已退汇交易的示例。

2.电子票据-贴现申请(DiscountableBillApply)接口。支持上送实付金额字段，实付金额

（白名单客户支持输入）：1. 该金额不输入，视为卖方付息。2. 该金额等于票面金

额时，视为买方付息。3. 该金额位于[0,票面金额)时，视为卖方付息或协议付息。

3.电子票据-贴现申请(DiscountableBillApply)接口。实付金额字段(discMoney)去除长

度格式限制。

4.新增接口：法人账户透支—额度信息查询(CorpCreditLimitQry)、借据信息查询

(CorpCreditInfoQry)、利率信息查询(CorpInterestInfoQry)、透支还款

(CorpRepaySubmit)、透支转账汇款(CorpCreditTransfer)接口。

【2022-11-9】V5.9.10

新增薪福通接口：

1）实账户模式单笔出金(B2eSalaryMgnRealTransfer)-支持客户通过自有平台 实现通

过实账户进行对外单笔转账。

2）实账户模式批量出金(B2eSalaryMgnRealBatchTransfer)-客户通过自有平台实现

通过实账户实现批量代发的功能。响应报文中“applyId”批次请求流水号为备用字段，暂

不返回。

3）实账户批量出金结果查询(B2eQrySalaryMgnRealBatchList)-实账户批量出金的交

易结果列表查询。

11 / 24

民生银行银企直联
4）实账户批量转账明细(B2eQrySalaryMgnBatchRealDetail)-查询实账户模式批量转

账后的交易明细。调用该交易前需要先查询 B2eQrySalaryMgnRealBatchList 服务，

获取交易参数信息。

5）实账户批量退汇查询(B2eQrySalaryMgnBatchRealReexList)-实账户批量退汇的

交易结果列表查询。 |

6）批量代发批次结果详情 pdf 文件下载(B2eQryCommonSalaryMgnBatchByFile)-

在调用该交易前需要先查询 B2eQrySalaryMgnRealBatchList 服务获取到 applyId 与

transStatus 的值，通过该交易实现批量出金的交易结果详情 pdf 文件下载。

【2022-11-14】V5.9.11

账户管理-网上对账：删除 1.8 对账单回执复核确认(VerifyAccRecApprConf)接口、

1.5 可复核对账单回执列表(QryVerifyAccRecApprList)接口。

【2022-11-16】V5.9.12

实账户模式批量出金(B2eSalaryMgnRealBatchTransfer)-客户通过自有平台实现通过

实账户实现批量代发的功能。响应报文中“applyId”批次请求流水号为备用字段现已返

回，报文示例更新。

【2022-12-30】V5.10.0

1.更新 账户管理-回单客户回单查询（支持返回记账流水号）(ElectnoteListQry)，新增

支持查询 T 日回单 。

2.账户管理-回单-客户账单回单下载（支持最多 3 个同时下载）

(b2eElectNoteDownLoadNew)，新增：是否添加可信签章字段-(NeedSign)。

3.删除 账户管理-回单-客户回单查询（支持 T 日回单）(b2eElectNoteQryNew)接口。

4.转账汇款-他行账户代发-他行代发账户资产金额查询(qryOtherBankAgentActBal) 接

口，增加订单状态（orderStat）字段描述：可用账号-1，在途-2，已完成：3

5.电子票据-查询票据信息(QryDraftInfo)接口：

12 / 24

民生银行银企直联
6.1）背书类型 ( endoType )字段新增值类型：12:提示收票(白名单客户返回）13:提示

承兑（白名单客户返回）。

7.2）背书日期 ( endoDate )字段新增值类型： 背书类型为提示收票时为出票日期、背

书类型为提示承兑时为承兑申请日期 。

8.3）签收日期( signDate ) 字段新增值类型： 背书类型为提示收票时为签收日期、背

书类型为提示承兑时为承兑日期 。

【2023-02-24】V6.0.0

1.银企直连智能账簿查询 (B2EVirtualAcctQry)接口，新增“ShowState”字段（是否返

回子账簿状态(未上送或者上送 0 时不返回子账簿状态)，上送 1 时，返回子 账簿状

态。）

【2023-03-01】V6.0.1

1.账户管理-回单 ElectnoteListQry 回单查询接口新增描述：“如需查询当日回单，需申

请开通白名单”

【2023-05-10】V6.0.2

1.智能账簿-交易明细查询 virtualDetailLoad 接口新增“vittualCapacity”字段（账簿号

位数（仅支持参数为 6））

【2023-05-26】V6.0.3

法人账户透支-透支转账汇款(CorpCreditTransfer)接口“payChannel” 汇路字段，增加

具体汇路码值： 0002：本行 1225：大额 1238：小额 1216：网银互联 。

【2023-05-31】V6.1.0

13 / 24

05.转账基础服务-批量费用报销 (batchCostReimb)接口已支持代发工资到数字人民币

民生银行银企直联

账户。

05.转账基础服务-批量费用报销、代发工资 (batchCostReimbNew)接口已支持代发工

资到数字人民币账户。

【2023-06-01】V6.1.1

34.法人账户透支 透支转账汇款(CorpCreditTransfer)接口请求报文（payChannel）

汇路字段新增入参说明。

【2023-06-06】V6.1.2

34.法人账户透支 透支转账汇款(CorpCreditTransfer)接口请求报文新增

（payeeBankNo）开户行行号字段。

05.转账基础服务批量费用报销、代发工资 (batchCostReimbNew)，请求报文，支付

文件数据格式(fileContent)字段补充 “收款行行名”字段。

【2023-06-07】V6.1.3

34.法人账户透支 透支转账汇款(CorpCreditTransfer)接口请求报文（purpose）用途

字段新增入参说明。

【2023-06-07】V6.1.4

34.法人账户透支 透支还款(CorpRepaySubmit)接口请求报文补充（insId）客户端流

水号唯一字段。

【2023-06-13】V6.1.5

14 / 24

新增 35.银企直联交易资金监管 模块，包含两个新增接口：资金划转

（B2eFundSupTransfer）、划转状态查询(B2eFundSupTransResQry)。

民生银行银企直联

【2023-07-03】V6.2.1

05.转账基础服务 xfer 接口，现已支持同行对私交易。

【2023-07-17】V6.2.2

去除 16.结售汇及外汇买卖、28.汇出汇款-境内外币、29.汇出汇款-跨境外币、30.汇出

汇款-跨境人民币 相关接口，共计 60 个接口。

现金管理-智能账簿批量转账(B2EVirtualBatchXfer)，请求报文示例修改。

【2023-07-27】V6.2.3

08.账户管理-余额明细，更新 qryTrsDtl、trsDtlDownLoad 接口。

【2023-08-11】V6.3.1

05.转账汇款-转账基础服务，删除 costReimb、CostGroupReimb 接口，更新 xfer 接

口。

调整常用转账场景说明，1.1.2 公转私-费用报销，去掉单笔费用报销 CostReimb，操作

子公司账户单笔费用报销 CostGroupReimb；1.1.3 公转私-代发工资，去掉说明：（说

明：目前暂无单笔代发工资（本行）的接口，向本行发放工资，请使用批量代发工资接

口。）

15 / 24

【2023-11-07】V6.4.1

新增 银企直联新一代电子票据接口。

民生银行银企直联

【2023-11-13】V6.4.3

新增 《 14. 银企直联新一代电子票据接口》，去除原《14.电子票据》。

【2023-12-08】V6.5.0

1.银企直联新一代电子票据，票据交易状态查询(B2eNbsQryDraftTransStatus)新增出

参交易流水号（transId）。

2.可同意清偿申请列表查询（B2eNbsAgreeDischargeSignUpPre)出参接收人账号

（transToAcctNo）改为非必输，接收人名称（transToName）改为非必输，接收

人统一社会信用代码（transToSocCode）改为非必输，接收人开户行行号

（transToBankNo）改为非必输，接收人开户行名称（transToBankName）改为

非必输。

3.薪福通，实账户模式批量出金(B2eSalaryMgnRealBatchTransfer)入参 企业自制凭

证 “CertNo“修改说明；最大位数 8 位，非必输。

4.07.转账汇款-他行账户代发 文档排版格式微调。

【2023-12-28】V6.5.1

1.14.银企直联新一代电子票据，1)背书申请(B2eNbsEndorsement) 新增入参申请备

注 （applyRemark）

2.2)票据详细信息查询(B2eNbsDraftDetail) 新增入参 展示票面备注

（showBillRemark ）0：展示，1：不展示；不输不展示

3.新增出参票面备注 （billRemark）

16 / 24

民生银行银企直联
4.3)贴现申请(B2eNbsDraftDiscount) 修改入参：aoAcctName 入账（收款）账户名

称改为 Y 必输。aoBankNo：“入账（收款）行号，与贴入行行号一致” 改为 “入账

（收款）行号，与申请人开户行行号一致”。

5.新增 32.银企直连数字人民币接口共 9 个。

【2024-01-05】V7.1.1

19.现金管理-全球跨境现金池, 1.7. 全球跨境现金池他行交易明细查询

(CBOtherBankTransDetailQry)返回字段去除 总条数（tatalNum）字段

【2024-01-08】V7.1.2

1.薪福通 实账户批量出金 B2eSalaryMgnRealBatchTransfer 增加代发说明字段

issuingInstructions。

2.薪福通 实账户模式单笔出金 B2eSalaryMgnRealTransfer 实账户批量出金

B2eSalaryMgnRealBatchTransfer 服务增加 客户端产生的交易唯一标识 trnId

（非必输）、业务流水号 insId（必输）。

【2024-01-22】V7.1.3

1.05 转账基础服务，topxfer 总公司以子公司名义付款交易，行名行号改为必输。

2.05 转账基础服务，transferXfer 制单转账交易，返回报文删除“balance”字段。

3.14 新一代电子票据，4.2 背书申请(B2eNbsEndorsement)，请求字段

applyRemark 描述改为"背书备注"。

4.14 新一代电子票据，4.2 背书申请(B2eNbsEndorsement)，请求字段

custAccount 签约账号改为必输。

17 / 24

5.14 新一代电子票据，6.1 可贴现票据列表查询(B2eNbsElectronicDisCountPre)，

民生银行银企直联

请求字段 oppName 描述改为“交易前手客户名称”。

6.14 新一代电子票据，6.3 贴现申请(B2eNbsDraftDiscount)，请求字段 sttlmMk 结

算方式描述结算方式：ST01：票款对付（线上清算）（DVP）改为暂不支持。

7.14 新一代电子票据，6.4 贴现线上协议查询(B2eNbsQueryDiscountTreaty)，返回

字段 treatyNo 协议编号改为必输。

8.14 新一代电子票据，可通用撤销票据列表查询(B2eNbsRevocableDraftsQry)，接

口描述 4 改为：”对于提示承兑且为我行承兑的票据，查询前需联系我行客户经理先

进行批次撤销，再进行查询。“

【2024-02-20】V7.2.1

1.14 新一代电子票据，户行信息查询 (B2eNbsQueryBankInfo)新增入参 是否返回大

额支付系统机构名称，非比输，0 展示 1 不展示，默认不展示

（showBankName）.删除入参省份代码（provinceCode）.增加出参：大额支付

系统机构名称（bankName），是否可用 0 不可用 1 可用（isAvailable）

2.18 现金管理 募管通 1.9. 募管通产品账号对外转账(prdXfer) 入参备注字段长度改为

50。

【2024-03-21】V7.3.1

14 新一代电子票据，6.2. 贴现金额试算（B2eNbsCalEleDiscountIntrst）,rate 利率

字段更改说明：贴现利率（例：如利率为 10%，输入 10）。

【2024-04-08】V7.4.1

新增 33.银企直联-汇出汇款（境内外币）

【2024-04-22】V7.4.2

18 / 24

1.30.薪福通 ，新增新增交易薪福通本行卡校验 B2eSalaryCheckBankCard、银企薪

福通工资卡校验结果查询 B2eDownLoadSalaryCheckBankCardFile 接口。

民生银行银企直联

【2024-06-28】V7.6.1

1.08.账户管理-余额明细查询，余额查询交易(qryBalNew)接口新增是否查询账户状

态：

“qryAcctState”字段。

2.新增新一代电子票据线上开票接口，34.银企直联线上开立银行承兑汇票

3.新增 35.银企直联税费通 接口

4.薪福通 19.实账户批量转账明细查询（B2eQrySalaryMgnBatchRealDetail）接口

<pageSize>更改字段说明（查询条数最大支持 50 条）；

【2024-07-26】V7.7.1

1.05.转账基础服务，9.批量费用报销、代发工资 (batchCostReimbNew)支持一借多

贷。<payModel>0：一借一贷，1：一借多贷

2.34.银企直联线上开立银行承兑汇票更新部分字段描述。

【2024-08-14】V7.8.1

1.16.现金管理-智能账簿 智能账簿单笔费用报销（B2EVirtualCostReimb），支持客户

上送企业自制凭证号并优化用途逻辑：

1）入参增加<CertNo>非必输字段，长度为 8。

2）入参<Explain>字段长度调整需输入常用用途代码|备注，备注可不输入，对应

转义的中文+备注总长度不得大于 50，例如：“311” 或者“311|测试”；

19 / 24

 
2.新一代电子票据，追索通知(B2eNbsRecourseRequest)入参 被追索人统一社会信用代码

民生银行银企直联

<rcvgOrgCode>改为非必输。

【2024-09-26】V7.9.1

1.接口文档 05.转账基础服务，批量费用报销、代发工资 (batchCostReimbNew) 调整

Usage 用途逻辑，更新说明。

2.接口文档 25.财富管理-银证银期， 银转证，证转银，余额查询，交易明细 查询接口增

加入参 pdGhNo 字段。

3.新增接口，集团客户总公司查询子公司信息(QryBlocAuthRelShpAllShow)

【2024-11-20】V7.11.1

1.接口文档：11.账户管理-回单，客户回单查询(ElectnoteListQry)接口、客户回单下载

(b2eElectNoteDownLoadNew) 接口支持贴现回单、OFD 文件格式回单查询下

载。客户回单回下载(b2eElectNoteDownLoadNew) 新增<pdfStyle>文件打印类

型字段。

2.接口文档：14.新一代电子票据，持有票据查询（B2eNbsDraftHoldingBillsQry）、

票据详细信息查询 （B2eNbsDraftDetail）、票据交易状态查询 （B2eNbsQryDr

aftTransStatus） 支持授权账户查询。票据交易状态查询 （B2eNbsQryDraftTra

nsStatus）返回字段删除<acptDt> 承兑日期。

3.接口文档：05.转账基础服务,batchCostReimb、batchCostReimbNew、BatchTra

nsferCostReimb 用途更新，用途代码与企业网银保持一制。

4.接口文档：05.转账基础服务，Xfer、ReserveTrasnsfer，修改说明；Xfer:如果向备

付金机构转账，请使用 ReserveTransfer 接口。ReserveTrasnsfer:支付机构的备

付金账户的开户行号，一般为 991 开头。

5.接口文档：05.转账基础服务，删除批量费用报销（batchCostReimb）接口，请使用

批量费用报销、代发工资 (batchCostReimbNew)接口代替支持相关业务功能。

6.接口文档：34.银企直联线上开立银行承兑汇票，融资申请结果查询(B2eQueryBasic

DraftResult)新增入参 channel-渠道标识，issueAccountNo-出票账户、返回列表

元素新增出参 channel-渠道标识；

20 / 24

民生银行银企直联
7.接口文档：34.银企直联线上开立银行承兑汇票，新增接口：融资申请信息查询(B2eQ

ueryBasicDraftDetails)，支持授权账户查询。

8.新增文档：36.银企直联授权主要模块支持授权账户接口索引。

9.接口文档：36.银企直联-集团授权业务接口合并至接口文档 20.现金管理-集团账户服

务。

【2024-12-09】V7.12.1

1. 新增接口文档：37.银企直联即期结售汇。

2. 更新接口 22.财富管理-定期存款，qryFixAcct(总公司定期存款列表查询) 接口更新：

新增字段 qryDeptTypeAndCifInfo（是否查询定期类型信息）。

3. 更新接口 12.账户管理-保证金 QryGuarActTransList（保证金账户交易明细查询）

入参增加 qryOpBankName（是否查询交易对手开户行行名）字段，若上送 0，则出参

中返回 opBankName（交易对手开户行行名）字段。

4. 新增接口文档：38.电子票据-银企直联票据管家

5. 更新接口 14.电子票据-新一代电子票据：出票登记、提示承兑、通用签收（提示承兑

签收）新增商票承兑限额校验说明。

6. 更新接口文档说明：16.现金管理-智能账簿，智能账簿批量转账入参汇路增加 3-网银

互联

7. 更新接口 21.财富管理-大额存单，自有账户/授权账户持有大额存单查询

(QryGroupFincDtl)支持授权账户及本企业大额存单查询

【2025-03-14】V8.2.1

1.接口文档：33.国际结算-外币-跨境人民币汇款，外币及跨境人民币汇款汇路信息查询

接口 (B2eIntlQryRtnAndRemitStatus)删除<senderCode>发报行字段。

2. 接口文档：34.电子票据-银企直联线上开立银行承兑汇票：

1）修改生产环境文件上传、文件下载域名地址：openapi.cmbc.com.cn: 2443。

21 / 24

2）修改获取文件 Tokne、文件上传、文件下载接口说明。

民生银行银企直联

3.接口文档：37.银企直联即期结售汇，修改 7.结售汇文件上传(b2eMultiUploads)接口

生产、测试环境调用地址。

4. 接口文档：05.转账基础服务：调整接口文档展示格式。对私付款用途修改调整与企网

一致

5.接口文档：08.账户管理-余额明细 trsDtlDownLoad 修改说明、qryTrsDtl、trsDtlD

ownLoad 接口新增 redFlag、redFlagShow 字段。trsDtlDownLoad 接口删除<ty

peCode>借贷标识字段。接口文档格式修改回单查询新增新增 redFlag、redFlagSh

ow 字段

6.接口文档：11.账户管理-回单 ，回单查询新增新增 redFlag、redFlagShow。增加<

FileName>字段上送说明。

7.接口文档：16.现金管理-智能账簿

1) 银企直联智能账簿批量开立(B2EBatchVirtualAcctMngt)、银企直联智能账簿

管理(B2EVirtualAcctMngt)接口新增授权账户的子账簿开立及管理功能；

2) 下载智能账簿交易凭证和回单功能、单笔费用报销、批量费用报销、智能账簿转

账银企直连智能账簿查询 (B2EVirtualAcctQry) 、智能账簿新交易明细查询(B

2EVirtualTransDetailQryNew) 、银企直连智能账簿间转账 (B2EVirtualAcc

tTrans) 、银企直连智能账簿转账(B2EVirtualAcctTransToEntAcct) 、对账

查询交易(qryXfer) 、交易明细下载（智能账簿专用）(virtualDetailLoad) 支

持授权账户使用；

3) 单笔、批量费用报销接口增加汇路和他行行号入参；

4) 银企直联智能账簿批量开立(B2EBatchVirtualAcctMngt)子账簿数量上限增加

到 1000 个；

8.33.国际结算-外币-跨境人民币汇款修改：

1) 外币/跨境人民币汇款申请-新增/退汇交易(IntlRemitApply)接口：五、申报信

息部分增加字段：<decDate>预计报关日期、<decPromise>报关承诺字段

2) 各接口增加企业银企网银操作员权限说明

22 / 24

民生银行银企直联
3) 外币及跨境人民币汇款汇路信息查询 (B2eIntlQryRtnAndRemitStatus)删除<

senderCode>发报行字段。

4) 外币/跨境人民币汇款申请-新增/退汇交易(IntlRemitApply)接口：去掉入参<a

uditFileContent>和<fileName>字段，新增入参<tradeNo>字段

5) 新增 查询上传文件令牌 id(ItsGetToken)接口

6) 新增 跨境外币、跨境人民币汇款申请文件上传(b2eMultiUploads)接口

9.接口文档 34.电子票据-银企直联线上开立银行承兑汇票：

1) 修改生产环境文件上传、文件下载域名地址：openapi.cmbc.com.cn: 2443

2) 修改获取文件 Tokne、文件上传、文件下载接口说明。

3) 融资预请(B2eBasicDraftApply)接口，<tradeContractAmount>贸易合同

金额字段增加非必输说明。

4) 融资预申请(B2eBasicDraftApply)接口新增保证金账户说明。

5) 融资预申请(B2eBasicDraftApply)接口新增字段：

6) <hashAlg> 签名算法（云证书仅支持 sm3）、< x509Cert> 公钥证书（sm

3 算法必输）。

10. 接口文档 37.银企直联即期结售汇，修改调用结售汇文件上传(b2eMultiUploads)接

口生产环境地址。

【2025-04-15】V8.3.1

1.接口文档 05.转账汇款-转账基础服务：接口 6.批量付款(batchXfer)接口汇路字段调整

说明。

2.接口文档 05.转账汇款-转账基础服务：接口 4.备付金转账 (ReserveTransfer)增加 交

易结果查询说明，qryXfer 接口支持 备付金转账交易结果查询。

3.接口文档 09.账户管理-网上对账：1.网上对账开通信息维护(DebtMaintainB2e)增加

说明，如需成员单位授权总部集中对账，需单独开通"集团客户集中对账"业务。

4.接口文档 34.电子票据银企直联线上开立承兑汇票：

23 / 24

民生银行银企直联

1）修改生产环境文件上传、文件下载域名地址：

生产环境：https://openapi.cmbc.com.cn: 2443

测试环境：https://obpgateway.cmbc.com.cn:10284

2）文件下载接口修改返回文件流说明。

3）将原有签名说明“1.1.4 签名请求”标题改为“1.2 签名请求”

4）增加文件上传、下载代码 Demo。

5.接口文档 11.账户管理-回单：增加贴现、OFD 白名单逻辑字段说明。

6.接口文档：08.账户管理-余额明细：5.交易明细对账单 PDF 下载(ElectNotePrint)增

加冲正表示 、交易区分字段。qryBalNew、qryHistoryBal 接口新增是否为保证金账

户标识 depositAccFlag 字段。

7.接口文档：37.银企直联即期结售汇：修改 7.结售汇文件上传(b2eMultiUploads)调用

地址，将”http”改为”https”。在云盘中删除该文档，若需要请联系总行国际结算交易

银行获取。

8.接口文档：33.国际结算-外币-跨境人民币汇款，

1). 新增-外币/跨境人民币汇款申请- 新增/退汇交易(IntlOutRemitApply) 接口。

存量客户使用 （IntlRemitApply）接口，新增客 户使用（IntlOutRemitAppl

y）接口

2). IntlOutRemitApply 接口支持境内外币现汇+购汇/外汇买卖汇 款，支持 FT

账户

3). 跨境外币、跨境人民币汇款文件 上传(b2eMultiUploads)接口修改调用 UR

L；将 http 改为 https。

在云盘中删除该文档，若需要请联系总行国际结算交易银行业务老师获取。

9.增加接口文档 38.票据管家目录，若需要请联系总行交易银行部票据交易中心业务老师

获取。

10.去除接口文档 07.转账汇款-他行账户代发。仅支持存量业务客户，（按行内要求，挡

板户代能已下线，预计 4 月底 5 月初发布内部户代发接口文档）。

24 / 24

