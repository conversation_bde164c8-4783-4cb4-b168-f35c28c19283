银企直联接口文档

（现金管理-智能账簿）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0

2021-03-18

定义接口文档

V2.0.0

2024-08-14

智能账簿单笔费用报销

（B2EVirtualCostReimb），支持

客户上送企业自制凭证号并优化用途

逻辑。

V2.1.0

2024-12-19

智能账簿批量转账

（B2EVirtualBatchXfer），汇路增

加：3-网银互联

V2.2.0

2025-02-25

1.银企直联智能账簿批量开立(B2EB

atchVirtualAcctMngt)、银企直

联智能账簿管理(B2EVirtualAcct

Mngt)接口新增授权账户的子账簿

开立及管理功能；

2.下载智能账簿交易凭证和回单功能

、单笔费用报销、批量费用报销、

智能账簿转账银企直连智能账簿查

询 (B2EVirtualAcctQry) 、智能

账簿新交易明细查询(B2EVirtual

TransDetailQryNew) 、银企直

连智能账簿间转账 (B2EVirtualA

cctTrans) 、银企直连智能账簿转

账(B2EVirtualAcctTransToEntA

cct) 、对账查询交易(qryXfer)

、交易明细下载（智能账簿专用）

(virtualDetailLoad) 支持授权账

户

1 / 110

民生银行银企直联

3.支持授权账户使用；

4.单笔、批量费用报销接口增加汇路

和他行行号入参；

5.银企直联智能账簿批量开立(B2EB

atchVirtualAcctMngt)子账簿数

量上限增加到 1000 个；

2 / 110

文档说明：（必读）

1.请 求 报 文 头 需 上 送 客 户 号 （ clientId ） 、 操 作 员

（userId）、银企直联密码（userPswd）（开通银企

直联后邮件或短信下发的银企交易密码，非 Ukey 密

码，如有疑问请联系客户经理）。其他 XML 报文说明

请参照接口文档：01.银企接口文档公共说明

2.XML 格式字段：接口字段名称。

3.字段说明：字段中文释义

4.是否必输：是否为必输字段：Y（是） / N（不是）

5.长度：

汉字长度：使用字符数（例如“最多小于等于 20 个汉

字”，即：“20”）来限制。

数字长度：使用位数描述整数部分和小数部分的长度限

制（例如“最多 10 位数字，其中 2 位小

数”）即：10,2。

目录

目录 .............................................................................................0

文档说明：（必读） ............................................................................. 0

1. 银企直联智能账簿管理(B2EVIRTUALACCTMNGT) .................................4

1.1. 请求(B2EVIRTUALACCTMNGT) ..................................................... 4

1.2. 响应(B2EVIRTUALACCTMNGT) ..................................................... 7

1.3. 例子 ...................................................................................... 7

2. 银企直联智能账簿批量开立(B2EBATCHVIRTUALACCTMNGT) ............... 10

2.1. 请求(B2EBATCHVIRTUALACCTMNGT) ...........................................10

2.2. 响应(B2EBATCHVIRTUALACCTMNGT) ...........................................14

2.3. 例子 .................................................................................... 14

3. 银企直联智能账簿查询 (B2EVIRTUALACCTQRY) ................................ 15

3.1. 请求(B2EVIRTUALACCTQRY) ..................................................... 16

3.2. 响应(B2EVIRTUALACCTQRY) ..................................................... 16

3.3. 例子 .................................................................................... 19

4. 银企直联智能账簿关联账户设置(B2EVIRTUALACCTRULESET) ................21

4.1. 请求(B2EVIRTUALACCTRULESET) ............................................... 22

4.2. 响应(B2EVIRTUALACCTRULESET) ............................................... 23

4.3. 例子 .................................................................................... 23

5. 银企直联智能账簿关联账户查询(B2EVIRTUALACCTRULEQRY) ............... 25

5.1. 请求(B2EVIRTUALACCTRULEQRY) ...............................................25

5.2. 响应(B2EVIRTUALACCTRULEQRY)) ............................................. 26

5.3. 例子 .................................................................................... 27

6. 银企直联智能账簿间转账 (B2EVIRTUALACCTTRANS) ..........................28

民生银行银企直联
6.1. 请求(B2EVIRTUALACCTTRANS) .................................................. 29

6.2. 响应(B2EVIRTUALACCTTRANS) .................................................. 29

6.3. 例子 .................................................................................... 30

7. 银企直联智能账簿交易明细查询(B2EVIRTUALACCTTRANSDETAILQRY) .. 31

7.1. 请求(B2EVIRTUALACCTTRANSDETAILQRY) .................................... 32

7.2. 响应(B2EVIRTUALACCTTRANSDETAILQRY) .................................... 32

7.3. 例子 .................................................................................... 33

8. 智能账簿新交易明细查询(B2EVIRTUALTRANSDETAILQRYNEW) ........... 35

8.1. 请求(B2EVIRTUALTRANSDETAILQRYNEW) .....................................35

8.2. 响应(B2EVIRTUALTRANSDETAILQRYNEW) .....................................36

8.3. 例子 .................................................................................... 37

9. 查询子公司智能账簿交易明细查询(B2EGROUPVIRTUALTRANSDETAILQRY)40

9.1. 请求(B2EGROUPVIRTUALTRANSDETAILQRY) ..................................40

9.2. 响应(B2EGROUPVIRTUALTRANSDETAILQRY) ..................................41

9.3. 例子 .................................................................................... 42

10. 单笔转账交易结果查询(QRYXFER) .................................................. 44

10.1. 请求(QRYXFER) .................................................................... 44

10.2. 响应(QRYXFER) .................................................................... 44

10.3. 例子 .................................................................................. 46

11. 交易明细下载（智能账簿专用）(VIRTUALDETAILLOAD) ...................... 49

11.1. 请求(VIRTUALDETAILLOAD) ......................................................50

11.2. 响应(VIRTUALDETAILLOAD) ......................................................51

11.3. 例子 .................................................................................. 52

12. 银企直联智能账簿转账(B2EVIRTUALACCTTRANSTOENTACCT) .......... 55

12.1. 请求(B2EVIRTUALACCTTRANSTOENTACCT) ................................. 55

1 / 110

民生银行银企直联
12.2. 响应(B2EVIRTUALACCTTRANSTOENTACCT) ................................. 57

12.3. 例子 .................................................................................. 57

13. 银企直联智能账簿单笔费用报销(B2EVIRTUALCOSTREIMB) ................. 59

13.1. 请求(B2EVIRTUALCOSTREIMB) .................................................59

13.2. 响应(B2EVIRTUALCOSTREIMB) .................................................62

13.3. 例子 .................................................................................. 62

14. 智能账簿批量费用报销(VIRTUALBATCHCOSTREIMB) ........................63

14.1. 请求(VIRTUALBATCHCOSTREIMB) .............................................. 64

14.1.1. fileContent 支付数据格式： ............................................................. 65

14.2. 响应(VIRTUALBATCHCOSTREIMB) .............................................. 66

14.3. 例子 .................................................................................. 66

15. 智能账簿批量转账(B2EVIRTUALBATCHXFER) ................................. 68

15.1. 请求(B2EVIRTUALBATCHXFER) ................................................ 68

15.2. 响应(B2EVIRTUALBATCHXFER) ................................................ 71

15.3. 例子 .................................................................................. 71

16. 智能账簿批量转账查询(QRYB2EVIRTUALBATCHXFER) ......................74

16.1. 请求(QRYB2EVIRTUALBATCHXFER) ........................................... 74

16.2. 响应(QRYB2EVIRTUALBATCHXFER) ........................................... 74

16.3. 例子 .................................................................................. 76

17. 智能账簿批量费用报销查询(QRYVIRTUALBATCHCOSTREIMB) ............ 78

17.1. 请求(QRYVIRTUALBATCHCOSTREIMB) ......................................... 79

17.2. 响应(QRYVIRTUALBATCHCOSTREIMB) ......................................... 79

17.3. 例子 .................................................................................. 80

18. 银企直联智能账簿间调账 (B2EVIRTUALACCTADJUST) .......................81

18.1. 请求(B2EVIRTUALACCTADJUST) ................................................81

2 / 110

民生银行银企直联
18.2. 响应(B2EVIRTUALACCTADJUST) ................................................82

18.3. 例子 .................................................................................. 83

19. 智能账簿利息清单查询(B2EVIRTUALINTERESTQRY) ......................... 84

19.1. 请求(B2EVIRTUALINTERESTQRY) .............................................. 84

19.2. 响应(B2EVIRTUALINTERESTQRY) .............................................. 85

19.3. 例子 .................................................................................. 86

20. 智能账簿利息清单下载(B2EVIRTUALINTERESTDOWNLOAD) ............. 88

20.1. 请求(B2EVIRTUALINTERESTDOWNLOAD) .....................................88

20.2. 响应(B2EVIRTUALINTERESTDOWNLOAD) .....................................88

20.3. 例子 .................................................................................. 90

21. 智能账簿批量产品账号明细查询(B2EVIRTUALBATCHDTLQRY) ............. 92

21.1. 请求(B2EVIRTUALBATCHDTLQRY) .............................................92

21.2. 响应(B2EVIRTUALBATCHDTLQRY) .............................................92

21.3. 例子 .................................................................................. 94

22. 银企直连智能账簿子账簿权限维护(B2EOPERATORAUTHVIRTUAL) ...... 100

22.1. 请求(B2EOPERATORAUTHVIRTUAL) ......................................... 100

22.2. 响应(B2EOPERATORAUTHVIRTUAL) ......................................... 101

22.3. 例子 ................................................................................ 101

23. 智能账簿交易凭证和回单下载(B2EVIRTUALACCTTRANSDOWNLOAD) 102

23.1. 请求(B2EVIRTUALACCTTRANSDOWNLOAD) ................................ 103

23.2. 响应(B2EVIRTUALACCTTRANSDOWNLOAD) ................................ 103

23.3. 例子 .................................................................................. 104

3 / 110

1.银企直联智能账簿管理(B2EVirtualAcctMngt)

民生银行银企直联

本部分更新日期:2025-02-25

银企直联智能账簿管理交易,管理一个实体账号的智能账簿，包括智能账簿的开立、修

改、删除。 交易要求：

1：实体账号必须是该公司自己的账号且加挂网银；

2：操作员必须对该实体账号具备查询并转账权限；

3：支持授权账户开立智能账簿。（如涉及使用授权账户请联系分行客户经理）

1.1.请求(B2EVirtualAcctMngt)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志

Y

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识 Y

64

 <OperFlag>

操作标识

0—新增，

1—删除，

2—修改

 <AcNo>

实体账号

 <VirtualAcNo>

智能账簿

 <VirtualAcName> 智能账簿名称

 <InitAmount>

初始金额

Y

1

Y

Y

Y

N

32

6

60

15,2

4 / 110

  标记

说明

 <ExceedLimit>

透支金额

 <Inherit>

是否遵从所属实账户计息规则

1-是

0-否

民生银行银企直联

是否

长度

必输

N

Y

15,2

1

 <AccrualFlag>

存款是否计息

N

1

1-是

0-否 (当 Inherit 为 0 时必输)

 <CreditRateType> 存款计息方式

N

1

0-固定

1-分段 （当 AccrualFlag 为 1 时必输）

 <CreditRate>

存款固定利率 （当 CreditRateType 为 0

N

2,5

时必输）

 <CreNum>

存款分段数（当 CreditRateType 为 1 时必

N

1

输，范围是 2 到 5）

 <CreditAmount1> 存款分段金额 1

 <CreditRate1>

存款分段利率 1

 <CreditAmount2> 存款分段金额 2

 <CreditRate2>

存款分段利率 2

 <CreditAmount3> 存款分段金额 3

 <CreditRate3>

存款分段利率 3

 <CreditAmount4> 存款分段金额 4

N

N

N

N

N

N

N

15,2

2,5

15,2

2,5

15,2

2,5

15,2

5 / 110

  标记

说明

 <CreditRate4>

存款分段利率 4

 <CreditAmount5> 存款分段金额 5

 <CreditRate5>

存款分段利率 5

 <AccrualMode>

透支是否计息

1-是

0-否(当 Inherit 为 0 时必输)

民生银行银企直联

是否

长度

必输

N

N

N

N

2,5

15,2

2,5

1

 <DebitRateType> 透支计息方式

N

1

0-固定

1-分段(当 AccrualMode 为 1 时必输)

 <DebitRate>

透支固定利率(当 DebitRateType 为 0 时

N

2,5

必输)

 <DebNum>

透支分段数(当 DebitRateType 为 1 时必

N

1

输)

 <DebitAmount1> 透支分段金额 1

 <DebitRate1>

透支分段利率 1

 <DebitAmount2> 透支分段金额 2

 <DebitRate2>

透支分段利率 2

 <DebitAmount3> 透支分段金额 3

 <DebitRate3>

透支分段利率 3

 <DebitAmount4> 透支分段金额 4

 <DebitRate4>

透支分段利率 4

N

N

N

N

N

N

N

N

15,2

2,5

15,2

2,5

15,2

2,5

15,2

2,5

6 / 110

  标记

说明

 <DebitAmount5> 透支分段金额 5

 <DebitRate5>

透支分段利率 5

 <AsEnableFlag>

账簿状态(0：启用,1：禁用）

</xDataBody>

1.2.响应(B2EVirtualAcctMngt)

  标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端交易的唯一标志

 <svrId>

指令 ID，一条转账指令在客户端的唯一标识

 <insId>

服务器该笔交易的标识

</xDataBody>

民生银行银企直联

是否

长度

必输

N

N

N

15,2

2,5

1

是否

长度

必返

Y

Y

32

32

64

1.3.例子

请求报文：

<?xmlversion="1.0"encoding="GB2312"?>

<CMBCheader = version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctMngt">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

7 / 110

民生银行银企直联

<clientId>2000351239</clientId>

<userId>2000351239002</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd013</insId>

<OperFlag>1</OperFlag> <!-- 操作 -->

<AcNo>87373022210201717604</AcNo> <!-- 签约账号 -->

<VirtualAcNo>000014</VirtualAcNo><!-- 智能账簿 -->

<VirtualAcName>000014</VirtualAcName><!-- 智能账簿名称 -->

<InitAmount>1000</InitAmount><!-- 初始金额 -->

<ExceedLimit>1000</ExceedLimit><!-- 透支金额 -->

<Inherit>0</Inherit><!-- 是否遵从所属实账户计息规则 -->

<AccrualFlag>1</AccrualFlag><!-- 存款是否计息 -->

<CreditRateType>0</CreditRateType><!-- 存款计息方式 -->

<CreditRate>11</CreditRate><!-- 存款固定利率 -->

<CreNum></CreNum><!-- 存款分段数 -->

<CreditAmount1></CreditAmount1><!-- 存款分段金额 1 -->

<CreditRate1></CreditRate1><!-- 存款分段利率 1 -->

<CreditAmount2></CreditAmount2><!-- 存款分段金额 2 -->

<CreditRate2></CreditRate2><!-- 存款分段利率 2 -->

<CreditAmount3></CreditAmount3><!-- 存款分段金额 3 -->

<CreditRate3></CreditRate3><!-- 存款分段利率 3 -->

<CreditAmount4></CreditAmount4><!-- 存款分段金额 4 -->

<CreditRate4></CreditRate4><!-- 存款分段利率 4 -->

<CreditAmount5></CreditAmount5><!-- 存款分段金额 5 -->

<CreditRate5></CreditRate5><!-- 存款分段利率 5 -->

<AccrualMode>0</AccrualMode><!-- 透支是否计息 -->

8 / 110

民生银行银企直联

<DebitRateType></DebitRateType><!-- 透支计息方式 -->

<DebitRate></DebitRate><!-- 透支固定利率 -->

<DebNum></DebNum><!-- 透支分段数 -->

<DebitAmount1></DebitAmount1><!-- 透支分段金额 1 -->

<DebitRate1></DebitRate1><!-- 透支分段利率 1 -->

<DebitAmount2></DebitAmount2><!-- 透支分段金额 1 -->

<DebitRate2></DebitRate2><!-- 透支分段利率 1 -->

<DebitAmount3></DebitAmount3><!-- 透支分段金额 1 -->

<DebitRate3></DebitRate3><!-- 透支分段利率 1 -->

<DebitAmount4></DebitAmount4><!-- 透支分段金额 1 -->

<DebitRate4></DebitRate4><!-- 透支分段利率 1 -->

<DebitAmount5></DebitAmount5><!-- 透支分段金额 1 -->

<DebitRate5></DebitRate5><!-- 透支分段利率 1 -->

<AsEnableFlag>1</AsEnableFlag><!-- 账簿状态-->

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2EVirtualAcctMngt"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

9 / 110

<trnId>84412option05dzd001015</trnId>

<insId>option05dzd013</insId>

</xDataBody>

</CMBC>

民生银行银企直联

2.银企直联智能账簿批量开立(B2EBatchVirtualAcct

Mngt)

本部分更新日期:2025-02-25

银企直联智能账簿批量开立交易,管理一个实体账号的多个智能账簿开立。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限；

3：支持授权账户开立智能账簿。（如涉及使用授权账户请联系分行客户经理）

4：开立子账簿数量上限增加到 1000。

2.1.请求(B2EBatchVirtualAcctMngt)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 < insId>

指令 ID，一条转账指令在客户端的唯一标识

 <AcNo>

实体账号

 <fileContent> 竖线“|”分割数据元素，以尖号“^”为数据行分割

符，具体格式定义

是否

长度

必输

Y

Y

Y

64

64

32

10 / 110

  标记

说明

</xDataBody>

数据格式：<fileContent>：

民生银行银企直联

是否

长度

必输

OperFlag|VirtualAcNo|VirtualAcName|InitAmount|ExceedLimit|Inherit|Accrual

Flag|CreditRateType|CreditRate|CreNum|CreditAmount1|CreditRate1|CreditA

mount2|CreditRate2|CreditAmount3|CreditRate3|CreditAmount4|CreditRate4|

CreditAmount5|CreditRate5|AccrualMode|DebitRateType|DebitRate|DebNum|

DebitAmount1|DebitRate1|DebitAmount2|DebitRate2|DebitAmount3|DebitRa

te3|DebitAmount4|DebitRate4|DebitAmount5|DebitRate5

  域名称

说明

 <OperFlag>

操作标识

0—新增

 <VirtualAcNo>

智能账簿

 <VirtualAcName> 智能账簿名称)

 <InitAmount>

初始金额

 <ExceedLimit>

透支金额

 <Inherit>

是否遵从所属实账户计息规：

1-是

0-否

是否

长度

必输

Y

Y

Y

N

N

Y

1

6

60

15,2

15,2

1

 <AccrualFlag>

存款是否计息：

N

1

1-是

0-否(当 Inherit 为 0 时必输)

 <CreditRateType> 存款计息方式：

N

1

11 / 110

  域名称

说明

民生银行银企直联

是否

长度

必输

0-固定

1-分段 ；

当 AccrualFlag 为 1 时必输

 <CreditRate>

存款固定利率，当 CreditRateType 为 0 时

N

2,5

必输

 <CreNum>

存款分段数，当 CreditRateType 为 1 时必

N

1

输，范围是 2 到 5

 <CreditAmount1> 存款分段金额 1

 <CreditRate1>

存款分段利率 1

 <CreditAmount2> 存款分段金额 2

 <CreditRate2>

存款分段利率 2

 <CreditAmount3> 存款分段金额 3

 <CreditRate3>

存款分段利率 3

 <CreditAmount4> 存款分段金额 4

 <CreditRate4>

存款分段利率 4

 <CreditAmount5> 存款分段金额 5

 <CreditRate5>

存款分段利率 5

15,2

2,5

15,2

2,5

15,2

2,5

15,2

2,5

15,2

2,5

 <AccrualMode>

透支是否计息：

N

1

1-是

0-否；

当 Inherit 为 0 时必输

12 / 110

  域名称

说明

 <DebitRateType> 透支计息方式：

0-固定

1-分段：

当 AccrualMode 为 1 时必输

民生银行银企直联

是否

长度

必输

N

1

 <DebitRate>

透支固定利率：当 DebitRateType 为 0 时

N

2,5

必输

 <DebNum>

透支分段数：当 DebitRateType 为 1 时必

N

1

输

 <DebitAmount1> 透支分段金额 1

N

15,2

 <DebitRate1>

透支分段利率 1

 <DebitAmount2> 透支分段金额 2

 <DebitRate2>

透支分段利率 2

 <DebitAmount3> 透支分段金额 3

 <DebitRate3>

透支分段利率 3

 <DebitAmount3> 透支分段金额 3

 <DebitRate3>

透支分段利率 3

 <DebitAmount4> 透支分段金额 4

 <DebitRate4>

透支分段利率 4

 <DebitAmount5> 透支分段金额 5

 <DebitRate5>

透支分段利率 5

2,5

15,2

2,5

15,2

2,5

15,2

2,5

15,2

2,5

15,2

2,5

13 / 110

2.2.响应(B2EBatchVirtualAcctMngt)

民生银行银企直联

  标记

说明

是否必返 长度

<xDataBody>

 <trnId>

客户端产生的交易标志，原值返回

 <svrId>

服务器该笔交易的标识

 <insId>

客户端产生的交易标志，原值返回

64

Y

N

Y

</xDataBody>

2.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EBatchVirtualAcctMngt">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>2200008969</clientId>

<userId>2200008969001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd013</insId>

<AcNo>*********</AcNo> <!-- 签约账号 -->

<fileContent>0|000001|虚拟账户

1|20000|0|1||||||||||||||||||||||||||||</fileContent>

14 / 110

民生银行银企直联

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2EBatchVirtualAcctMngt"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2013-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<insId>option05dzd013</insId>

</xDataBody>

</CMBC>

3.银企直联智能账簿查询 (B2EVirtualAcctQry)

本部分更新日期:2025-02-26

银企直联智能账簿查询交易,查询一个实体账号下智能账簿的信息。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银；

2：操作员必须对该实体账号具备查询并转账权限。

3：支持实体授权账户查询子账簿

15 / 110

3.1.请求(B2EVirtualAcctQry)

  标记

说明

民生银行银企直联

是否

长度

必输

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <AcNo>

实体账号

 <ShowState>

是否返回子账簿状态(未上送或者上送 0 时不返

回子账簿状态)，上送 1 时，返回子账簿状态

 <VirtualAcNo> 虚拟账号 如果不输则分页查询实体账号下所有

子账户信息

 <currentIndex> 起始位置 如果 VirtualAcNo 不输入，则此字段

必输

 <pageSize>

查询笔数 如果 VirtualAcNo 不输入，则此字段

Y

N

Y

Y

N

64

64

32

1

6

必输

</xDataBody>

3.2.响应(B2EVirtualAcctQry)

  标记

说明

<xDataBody>

服务消息集

是否

长度

必返

16 / 110

  标记

说明

 <recordNumber>

总数

 <List>

  <Map>

   <VirtualAcNo>

智能账簿

   <VirtualAcName> 智能账簿名称

   <Currency>

币种

   <SelfBalance>

自身余额

   <ExceedLimit>

透支额度

   <Inherit>

是否遵从所属实账户计息规则

   <CreditRate>

贷方利率

   <CreditAmount1> 贷方分段金额 1

   <CreditRate1>

贷方分段利率 1

   <CreditAmount2> 贷方分段金额 2

   <CreditRate2>

贷方分段利率 2

   <CreditAmount3> 贷方分段金额 3

   <CreditRate3>

贷方分段利率 3

   <CreditAmount4> 贷方分段金额 4

   <CreditRate4>

贷方分段利率 4

民生银行银企直联

是否

长度

必返

32

32

6

3

15,2

15,2

0-否

1-是

8,5

15,2

8,5

15,2

8,5

15,2

8,5

15,2

8,5

17 / 110

Y

Y

Y

Y

Y

Y

Y

N

N

N

N

N

N

N

N

  标记

说明

   <CreditAmount5> 贷方分段金额 5

   <CreditRate5>

贷方分段利率 4

   <DebitRate>

借方利率

   <DebitAmount1> 借方分段金额 1

   <DebitRate1>

借方分段利率 1

   <DebitAmount2> 借方分段金额 2

   <DebitRate2>

借方分段利率 2

   <DebitAmount3> 借方分段金额 3

   <DebitRate3>

借方分段利率 3

   <DebitAmount4> 借方分段金额 4

   <DebitRate4>

借方分段利率 4

   <DebitAmount5> 借方分段金额 5

   <DebitRate5>

借方分段利率 5

   <AsEnableFlag>

账簿状态（当入参中 ShowState 为 1

时才返回）

0：启用,

1：禁用

  <Map>

 <List>

</xDataBody>

民生银行银企直联

是否

长度

必返

N

N

Y

N

N

N

N

N

N

N

N

N

N

N

15,2

8,5

8,5

15,2

8,5

15,2

8,5

15,2

8,5

15,2

8,5

15,2

8,5

2

18 / 110

民生银行银企直联

3.3.例子

请求报文：

<?xml version="1.0"encoding="GB2312"?>

<CMBC header ="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>2000351239</clientId>

<userId>2000351239002</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd013</insId>

<OperFlag>1</OperFlag> <!-- 操作 -->

<AcNo>87373022210201717604</AcNo> <!-- 签约账号 -->

<VirtualAcNo>000014</VirtualAcNo><!-- 智能账簿 -->

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualAcctQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-10-16 14:44:44</dtServer>

19 / 110

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<recordNumber>12</recordNumber>

<List>

<Map>

<VirtualAcNo>87373022210201717604-000026</VirtualAcNo>

<VirtualAcName>000026</VirtualAcName>

<Currency>CNY</Currency>

<SelfBalance>26.00</SelfBalance>

<ExceedLimit>0.00</ExceedLimit>

<Inherit>1</Inherit>

<CreditRate>0.00000</CreditRate>

<CreditAmount1>0.00</CreditAmount1>

<CreditRate1>0.00000</CreditRate1>

<CreditAmount2>0.00</CreditAmount2>

<CreditRate2>0.00000</CreditRate2>

<CreditAmount3>0.00</CreditAmount3>

<CreditRate3>0.00000</CreditRate3>

<CreditAmount4>0.00</CreditAmount4>

<CreditRate4>0.00000</CreditRate4>

<CreditAmount5>0.00</CreditAmount5>

<CreditRate5>0.00000</CreditRate5>

<DebitRate>0.00000</DebitRate>

<DebitAmount1>0.00</DebitAmount1>

<DebitRate1>0.00000</DebitRate1>

<DebitAmount2>0.00</DebitAmount2>

<DebitRate2>0.00000</DebitRate2>

<DebitAmount3>0.00</DebitAmount3>

<DebitRate3>0.00000</DebitRate3>

<DebitAmount4>0.00</DebitAmount4>

<DebitRate4>0.00000</DebitRate4>

<DebitAmount5>0.00</DebitAmount5>

<DebitRate5>0.00000</DebitRate5>

</Map>

<Map>

<VirtualAcNo>87373022210201717604-000027</VirtualAcNo>

20 / 110

<VirtualAcName>000027</VirtualAcName>

民生银行银企直联

<Currency>CNY</Currency>

<SelfBalance>27.00</SelfBalance>

<ExceedLimit>0.00</ExceedLimit>

<Inherit>1</Inherit>

<CreditRate>0.00000</CreditRate>

<CreditAmount1>0.00</CreditAmount1>

<CreditRate1>0.00000</CreditRate1>

<CreditAmount2>0.00</CreditAmount2>

<CreditRate2>0.00000</CreditRate2>

<CreditAmount3>0.00</CreditAmount3>

<CreditRate3>0.00000</CreditRate3>

<CreditAmount4>0.00</CreditAmount4>

<CreditRate4>0.00000</CreditRate4>

<CreditAmount5>0.00</CreditAmount5>

<CreditRate5>0.00000</CreditRate5>

<DebitRate>0.00000</DebitRate>

<DebitAmount1>0.00</DebitAmount1>

<DebitRate1>0.00000</DebitRate1>

<DebitAmount2>0.00</DebitAmount2>

<DebitRate2>0.00000</DebitRate2>

<DebitAmount3>0.00</DebitAmount3>

<DebitRate3>0.00000</DebitRate3>

<DebitAmount4>0.00</DebitAmount4>

<DebitRate4>0.00000</DebitRate4>

<DebitAmount5>0.00</DebitAmount5>

<DebitRate5>0.00000</DebitRate5>

</Map>

</List>

</xDataBody>

</CMBC>

4.银企直联智能账簿关联账户设置(B2EVirtualAcctRu

leSet)

本部分更新日期:2021-04-02

银企直联智能账簿关联账户设置,设置一个实体账号下多个智能账簿的关联账户。

21 / 110

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限；

4.1.请求(B2EVirtualAcctRuleSet)

  标记

说明

<xDataBody>

民生银行银企直联

是否

长度

必返

 <trnId>

客户端产生的交易唯一标志

Y

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识 Y

 <AcNo>

实体账号

 < VirtualAcNo>

智能账簿

 <List>

  <Map>

   <AcSeq>

序号

   <OperFlag>

操作方式

0-新增

1-删除

2-修改

Y

Y

N

N

64

32

6

39

60

39

1

   <RelType>

关联方式

N

1

1：关联账号；

2：关联户名。

   <RelInfo>

关联信息 如果关联方式为：

N

22

22 / 110

  标记

说明

民生银行银企直联

是否

长度

必返

1：关联账号则该字段表示被关联的账号

2：关联户名则该字段表示被关联的户名

   <NewRelInfo> 修改后的关联信息 如果交易操作标志为 2-修

N

改是必输表示被修改后的关联信息

  <Map>

 <List>

</xDataBody>

4.2.响应(B2EVirtualAcctRuleSet)

  标记

说明

是否必返 长度

<xDataBody>

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

客户端交易的唯一标志

32

32

Y

Y

Y

</xDataBody>

4.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctRuleSet">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

23 / 110

民生银行银企直联

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd015</insId>

<AcNo>*********</AcNo> <!--实体账号 -->

<VirtualAcNo>110002</VirtualAcNo><!-- 虚拟账号 -->

<List>

<Map>

<AcSeq>1</AcSeq>

<OperFlag>0</OperFlag>

<RelType>1</RelType>

<RelInfo>123456</RelInfo>

<NewRelInfo></NewRelInfo>

</Map>

</List>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualAcctRuleSet" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2015-10-09 10:10:04</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

24 / 110

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<insId>option05dzd015</insId>

</xDataBody>

</CMBC>

5.银企直联智能账簿关联账户查询(B2EVirtualAcctRu

leQry)

本部分更新日期:2021-04-02

银企直联智能账簿关联账户查询,查询一个实体账号的智能账簿的关联账户

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限；

5.1.请求(B2EVirtualAcctRuleQry)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志

Y

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <AcNo>

实体账号

 <VirtualAcNo> 智能账簿

</xDataBody>

Y

Y

Y

64

32

6

25 / 110

5.2.响应(B2EVirtualAcctRuleQry))

  标记

说明

<xDataBody>

服务消息集

民生银行银企直联

是否

长度

必返

 <trnId>

 <svrId>

 <insId>

 <List>

  <Map>

   <Status>

   <AcNo >

客户端交易的唯一标志

服务器该笔交易的标识

客户端交易的唯一标志

状态：

0-正常；

1-注销

账号

   <VirtualAcNo>

智能账簿

   <VirtualAcName>

智能账簿名称

   <Currency>

币种

   <RelInfo>

关联信息 如果关联方式为：

1：关联账号则该字段表示被关

联的账号

2：关联户名则该字段表示被关

联的户名

关联方式

1：关联账号；

   <RelType>

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

64

32

26 / 110

  标记

说明

2：关联户名。

民生银行银企直联

是否

长度

必返

  <Map>

 <List>

</xDataBody>

5.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctRuleQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd016</insId>

<AcNo>*********</AcNo> <!-- 实体账号 -->

<VirtualAcNo>110002</VirtualAcNo><!-- 智能账簿 -->

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualAcctRuleQry" security="none" lang="chs"

27 / 110

民生银行银企直联

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2015-10-09 10:12:27</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<Status>0</Status>

<AcNo>*********</AcNo>

<Currency>CNY</Currency>

<VirtualAcNo>110002</VirtualAcNo>

<VirtualAcName>110002 测试</VirtualAcName>

<RelType>1</RelType>

<RelInfo>123456</RelInfo>

</Map>

</List>

</xDataBody>

</CMBC>

6.银企直联智能账簿间转账 (B2EVirtualAcctTrans)

本部分更新日期:2025-02-26

银企直联智能账簿间转账,一个实体账号的智能账簿向该实体账号的另外一个智能账簿转
账。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限；

3：支持授权账户子账簿间转账

28 / 110

6.1.请求(B2EVirtualAcctTrans)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 < insId>

指令 ID，一条转账指令在客户端的唯一标

识

 <AcNo>

实体账号

 <AcName>

实体账户名

 <PayerAcNo>

付方智能账簿(格式是：实账号-智能账簿)

 <PayerAcName> 付方智能账簿

 <PayeeAcNo>

收方智能账簿(格式是：实账号-智能账簿)

 <PayeeAcName> 收方智能账簿名

 <Amount>

转账金额

 <Usage>

用途

</xDataBody>

6.2.响应(B2EVirtualAcctTrans)

  标记

说明

民生银行银企直联

是否

长度

必输

Y

N

Y

Y

Y

Y

Y

Y

Y

Y

Y

64

64

32

60

39

60

39

60

15,2

42

是否

长度

必返

29 / 110

民生银行银企直联

是否

长度

必返

32

32

Y

Y

Y

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

客户端交易的唯一标志

</xDataBody>

6.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctTrans">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>2000351239</clientId>

<userId>2000351239002</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd018</insId>

<AcNo>87373022210201717604</AcNo>

<AcName>测试 2000351239</AcName>

<PayerAcNo>87373022210201717604-000004</PayerAcNo>

<PayerAcName>000004</PayerAcName>

<PayeeAcNo>87373022210201717604-000025</PayeeAcNo>

<PayeeAcName>000025</PayeeAcName>

30 / 110

民生银行银企直联

<Amount>10.00</Amount>

<Usage>签约账号</Usage>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2EVirtualAcctTrans"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<insId>option05dzd018</insId>

</xDataBody>

</CMBC>

7.银企直联智能账簿交易明细查询(B2EVirtualAcctTr

ansDetailQry)

本部分更新日期:2021-04-02

查询实体账号下智能账簿的交易明细。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限；

31 / 110

7.1.请求(B2EVirtualAcctTransDetailQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <AcNo>

实体账号

 <VirtualAcNo> 智能账簿

 <BeginDate>

起始日期(yyyy-MM-dd)

 <EndDate>

结束日期(yyyy-MM-dd)

 <currentIndex> 起始位置

 <pageSize>

查询笔数

</xDataBody>

7.2.响应(B2EVirtualAcctTransDetailQry)

  标记

说明

<xDataBody>

服务消息集

 <recordNumber>

总数

 <List>

民生银行银企直联

长度

是否

必输

Y

Y

Y

Y

Y

Y

64

64

32

6

8

8

是否

长度

必返

32

32

32 / 110

民生银行银企直联

是否

长度

必返

Y

Y

N

N

Y

Y

Y

Y

N

Y

8

15,2

15,2

15,2

8

39

180

60

60

  标记

说明

  <Map>

   <TransDate>

交易日期(yyyy-MM-dd)

   <TransTime>

交易时间

   <LoanAmount> 借方金额

   <LendAmount> 贷方金额

   <SelfBalance> 自身余额

   <CertNo>

凭证号

   <OppAcNo>

对方账户

   <OppAcName> 对方户名

   <Remark>

备注

   <TransName>

交易类型

  <Map>

 <List>

</xDataBody>

7.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctTransDetailQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>2000351239</clientId>

33 / 110

民生银行银企直联

<userId>2000351239002</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd013</insId>

<BeginDate>2014-10-16</BeginDate>

<EndDate>2014-10-17</EndDate>

<AcNo>87373022210201717604</AcNo>

<VirtualAcNo>000004</VirtualAcNo>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualAcctTransDetailQry" security="none"

lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2014-10-17 17:02:01</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<recordNumber>1</recordNumber>

<List>

34 / 110

民生银行银企直联

<Map>

<TransDate>2014-10-16</TransDate>

<TransTime>16:09:32</TransTime>

<LoanAmount>10.00</LoanAmount>

<SelfBalance>999990.00</SelfBalance>

<SelfBalance>999990.00</SelfBalance>

<CertNo></CertNo>

<OppAcNo>87373022210201717604-000025</OppAcNo>

<OppAcName>测试 2000351239 000025</OppAcName>

<Remark>签约账号</Remark>

<TransName>虚拟子账户手工划拨(接测试 2000351239</TransName>

</Map>

</List>

</xDataBody>

</CMBC>

8.智能账簿新交易明细查询(B2EVirtualTransDetail

QryNew)

本部分更新日期:2021-04-02

1.新明细查询增加显示交易流水号、交易子流水号、对方开户行名称。

2.支持授权账户查询子账簿

8.1.请求(B2EVirtualTransDetailQryNew)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <AcNo>

实体账号

是否

长度

必输

Y

Y

64

32

35 / 110

  标记

说明

 <VirtualAcNo> 智能账簿

 <BeginDate>

起始日期(yyyy-MM-dd)

 <EndDate>

结束日期(yyyy-MM-dd)

 <currentIndex> 起始位置

 <pageSize>

查询笔数

</xDataBody>

民生银行银企直联

是否

长度

必输

Y

Y

Y

N

N

6

8

8

8.2.响应(B2EVirtualTransDetailQryNew)

  标记

说明

是否

长度

必返

<xDataBody>

服务消息集

 <trnId>

客户端产生的交易唯一标志

 <recordNumber>

总数

 <List>

  <Map>

   <TransDate>

交易日期(yyyy-MM-dd)

   <SerialNo>

交易流水号

   <SubSerialNo>

交易子流水号

   <TransTime>

交易时间

Y

Y

Y

Y

Y

Y

32

32

8

36 / 110

民生银行银企直联

是否

长度

必返

N

N

Y

Y

N

N

Y

N

Y

15,2

15,2

15,2

13

39

180

60

60

  标记

说明

   <LoanAmount>

借方金额

   <LendAmount>

贷方金额

   <SelfBalance>

自身余额

   <CertNo>

凭证号

   <OppAcNo>

对方账户

   <OppAcName>

对方户名

   <OppBankName> 对方开户行名称

   <Remark>

备注

   <TransName>

交易类型

  <Map>

 <List>

</xDataBody>

8.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualTransDetailQryNew">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

37 / 110

民生银行银企直联

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>option05dzd013</insId>

<BeginDate>2016-12-01</BeginDate>

<EndDate>2016-12-26</EndDate>

<AcNo>*********</AcNo>

<VirtualAcNo>000001</VirtualAcNo>

<currentIndex>1</currentIndex>

<pageSize>2</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualTransDetailQryNew" security="none"

lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-12-29 09:55:13</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<recordNumber>57</recordNumber>

<List>

<Map>

<TransDate>2016-12-14</TransDate>

38 / 110

<SerialNo>********</SerialNo>

民生银行银企直联

<SubSerialNo>50301201612140025583049503021677</SubSerialNo>

<TransTime>11:12:28</TransTime>

<LoanAmount>123.00</LoanAmount>

<LendAmount></LendAmount>

<SelfBalance>999877.00</SelfBalance>

<CertNo>*************</CertNo>

<OppAcNo>*********</OppAcNo>

<OppAcName>UAT 票据测试 001</OppAcName>

<OppBankName></OppBankName>

<Remark></Remark>

<TransName>对外转账(接口)</TransName>

</Map>

<Map>

<TransDate>2016-12-14</TransDate>

<SerialNo>********</SerialNo>

<SubSerialNo>50301201612140025585573503024131</SubSerialNo>

<TransTime>14:17:10</TransTime>

<LoanAmount>1.10</LoanAmount>

<LendAmount></LendAmount>

<SelfBalance>999875.90</SelfBalance>

<CertNo></CertNo>

<OppAcNo>*********-000000</OppAcNo>

<OppAcName>UAT 票据测试 002 总账簿</OppAcName>

<OppBankName></OppBankName>

<Remark>智能账簿间转账</Remark>

<TransName>虚拟子账户手工划拨(接 UAT 票据测试 002</TransName>

</Map>

</List>

</xDataBody>

</CMBC>

39 / 110

民生银行银企直联
9.查询子公司智能账簿交易明细查询(B2EGroupVirtua

lTransDetailQry)

本部分更新日期:2021-04-02

查询子公司授权给总公司的实账户下的智能账簿。

该操作员须对实账户有查询权限

9.1.请求(B2EGroupVirtualTransDetailQry)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志

Y

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <AcNo>

子公司实体账号

 <VirtualAcNo> 智能账簿

 <BeginDate>

起始日期(yyyy-MM-dd)

 <EndDate>

结束日期(yyyy-MM-dd)

 <currentIndex> 起始位置

 <pageSize>

查询笔数

</xDataBody>

Y

Y

Y

Y

N

N

32

6

8

8

40 / 110

9.2.响应(B2EGroupVirtualTransDetailQry)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端产生的交易唯一标志

 <recordNumber>

总数

 <List>

  <Map>

   <TransDate>

交易日期(yyyy-MM-dd)

   <SerialNo>

交易流水号

   <SubSerialNo>

交易子流水号

   <TransTime>

交易时间

   <LoanAmount>

借方金额

   <LendAmount>

贷方金额

   <SelfBalance>

自身余额

   <CertNo>

凭证号

   <OppAcNo>

对方账户

   <OppAcName>

对方户名

   <OppBankName> 对方开户行名称

   <Remark>

备注

民生银行银企直联

是否

长度

必返

Y

Y

Y

Y

Y

Y

N

N

N

N

N

32

32

8

15,2

15,2

15,2

8

39

180

60

41 / 110

  标记

说明

民生银行银企直联

是否

长度

必返

   <TransName>

交易类型

60

  <Map>

 <List>

</xDataBody>

9.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EGroupVirtualTransDetailQry">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<BeginDate>2017-12-01</BeginDate>

<EndDate>2017-12-08</EndDate>

<AcNo>*********</AcNo>

<VirtualAcNo>000000</VirtualAcNo>

<currentIndex>1</currentIndex>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

42 / 110

响应报文

民生银行银企直联

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EGroupVirtualTransDetailQry" security="none"

lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-12-08 14:23:04</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<recordNumber>1</recordNumber>

<List>

<Map>

<TransDate>2017-12-05</TransDate>

<SerialNo>********</SerialNo>

<SubSerialNo>503012017120500********503721870</SubSerialNo>

<TransTime>15:41:01</TransTime>

<LoanAmount>9.00</LoanAmount>

<LendAmount></LendAmount>

<SelfBalance>*********.86</SelfBalance>

<CertNo></CertNo>

<OppAcNo></OppAcNo>

<OppAcName>其它应付款</OppAcName>

<OppBankName></OppBankName>

<Remark>工资</Remark>

<TransName></TransName>

</Map>

</List>

43 / 110

</xDataBody>

</CMBC>

民生银行银企直联

10.单笔转账交易结果查询(qryXfer)

本部分更新日期:2022-10-19

1. 根据流水号（insId）查询一笔转账交易是否成功

2. 请注意，单笔对账交易，若在报文头 code 字段返回 E1602，则表示该笔交易银行未受

理，可以视为交易失败 。

3. 支持授权账户查询

10.1.请求(qryXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

 <cltcookie> 可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <svrId>

服务器返回转账消息的标识（非必输）

</xDataBody>

10.2.响应(qryXfer)

  标记

说明

<xDataBody>

服务消息集

是否

长度

必输

Y

N

Y

N

64

64

32

是否

长度

必返

44 / 110

  标记

说明

 <trnId>

客户端交易的唯一标志

 <insId>

指令 ID，一条转账指令在客户端的唯一标

识

 <svrId>

服务器对转账消息的标识

 <statusId>

 <statusCode>

状态码：

民生银行银企直联

是否

长度

必返

Y

Y

Y

Y

64

64

32

1

0: 原交易成功；

2: 原交易失败；

3: 对账因为网络原因失败，请过一会再

试，原转账交易状态未知；

4: 原交易处理中

5: 交易成功（已退汇）（仅白名单客户返

回）

 <statusSeverity> 当状态码为 0 时，返回 ok；

Y

16

当状态码为 2 时，返回 W6191；

当状态码为 3 时，返回 F

当状态码为 4 时 所有汇路

10 —— 等待银行审批

12 —— 银行审批通过，等待发送核心

当汇路是大、小额时

1 —— 已受理

5 —— 冲账

当汇路是网银互联时

PR00 —— 已转发

45 / 110

  标记

说明

民生银行银企直联

是否

长度

必返

PR02 —— 已付款

PR06 —— 待处理

PR07 —— 已处理

PR08 —— 已撤销

PR10 —— 已确认

当状态码为 5 时，返回 PS15

 <statusErrMsg>

 </statusId>

描述信息（

Y

</xDataBody>

说明：对账响应描述

信息在 xFerStatus 中

10.3.例子

请求报文

<?xml version = "1.0" encoding = "utf-8"?>

<CMBC header="100" version="100" security="none" lang="utf-8"

trnCode="qryXfer">

<requestHeader>

<dtClient>20020615 10:20:45</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

46 / 110

民生银行银企直联

<trnId>1001</trnId>

<insId>00sa01ds01006861</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="chs" security="none" trnCode="qryXfer"

version="100">

<responseHeader>

<status>

<code>E1602</code>

<severity>Error</severity>

<message>此流水号不存在,请查证</message>

</status>

<dtServer>2008-09-02 14:05:58</dtServer>

<dtDead></dtDead>

<lanaguge>utf-8</lanaguge>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>46tr456</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

<statusId>

<statusCode></statusCode>

<statusSeverity></statusSeverity>

<statusErrMsg></statusErrMsg>

</statusId>

</xDataBody>

</CMBC>

请求的流水号存在且转账成功的交易

<<?xml version="1.0" encoding="utf-8"?>

<CMBC header="100" lang="utf-8" security="none" trnCode="qryXfer"

47 / 110

民生银行银企直联

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-02 14:11:48</dtServer>

<dtDead></dtDead>

<lanaguge>utf-8</lanaguge>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>8053qwe</insId>

<svrId>jVv2Hc3ZHAzX</svrId>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>转账交易已成功！</statusErrMsg>

</statusId>

</xDataBody>

</CMBC>

请求的流水号已退汇

<<?xml version="1.0" encoding="utf-8"?>

<CMBC trnCode="qryXfer" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2022-07-14 14:44:28</dtServer>

<userKey>N</userKey>

48 / 110

民生银行银企直联

<dtDead />

<language>UTF-8</language>

</responseHeader>

<xDataBody>

<trnId>1001</trnId>

<insId>22071410264600000000000203428461</insId>

<svrId>31301202207146127589058313000000</svrId>

<statusId>

<statusCode>5</statusCode>

<statusSeverity>PS15</statusSeverity>

<statusErrMsg>交易成功（已退汇）</statusErrMsg>

</statusId>

</xDataBody>

</CMBC>

报文头的<code>节点返回错误码说明

1、E1602 —— 则表示该笔交易银行未受理，可以视为交易失败

2、EYQ13 —— 此汇路目前不支持状态未知交易的对账查询，原交易的状态为未知

3、E6031 —— 转账状态未知

该交易存在的问题：

1、目前上海同城汇路交易如果转账时候接口明确返回成功或者失败，此处可以返回处理
结果，如果转账当时返回超时（WEC02）异常，此处无法调核心接口进行查询。

2：对于原交易状态未知的情况，大、小额、网银互联和行内转账能够从核心获得原交易
的状态，其他汇路，目前不能获得原交易的终态。

11.交易明细下载（智能账簿专用）(virtualDetailLoa

d)

本部分更新日期:2023-05-10

根据客户号、账号、起始笔数、查询笔数、起始日期、结束日期、借贷标记、中英文标志

下载此账号的明细。

49 / 110

1、此客户号下所有账号是否有一个或多个对所查账号具有查询权限，如果均没有对所查

询账号的查询权限则报错“没有该账户的现金池查询权限，不允许查询”。

民生银行银企直联

2、下载明细时起始笔数必须大于 0 且不能超过最大查询记录数，最大查询记录不能超过

10000 且不能小于 0，查询之间间隔不能大于 3 个月。

3、有查询权限则调用明细下载接口获取账号明细。

4、同一个账号不能在设定的时间内连续进行起始记录条数是 1 的查询，其他的起始条数

被认为是翻页查询，不受时间间隔限制，目前时间限制设置是 15 分钟；

5、实账号的明细的“银行附言”会返回子账号

6、支持授权账户查询

11.1.请求(virtualDetailLoad)

  标记

说明

<xDataBody>

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志（必输，但无作

N

64

用）

 <acntNo>

查询账号

 <dateFrom>

起始时间（yyyy-MM-dd）

 <dateTo>

结束日期（yyyy-MM-dd）

 <startNo>

起始笔数

 <queryRows>

查询笔数

 <typeCode>

借贷标记(暂不支持)

 <vittualCapacity> 账簿号位数（仅支持参数为 6）

Y

Y

Y

Y

Y

N

N

32

10

10

10

10

1

6

50 / 110

民生银行银企直联

是否

长度

必输

是否

长度

必返

64

Y

Y

Y

  标记

说明

</xDataBody>

11.2.响应(virtualDetailLoad)

  标记

说明

<xDataBody>

 <trnId>

原值返回

 <totalNum>

总条数

 <dataStream> 竖线“|”分割数据元素，以“0x0A”为数据行分割

符

</xDataBody>

*dataStream 说明（具体请参考报文示例）：

1、当未配置子账簿解析白名单时：

交易日期|主机交易流水号|借方发生额|贷方发生额|账户余额|凭证种类|凭证号|摘要|银行附

言|交易地点|对方账号|对方账号名称|对方开户行|交易时间戳

2、当配置了子账簿解析白名单并且 vittualCapacity 参数为 6 时：

交易日期|主机交易流水号|借方发生额|贷方发生额|账户余额|凭证种类|凭证号|摘要|银行附

言|交易地点|对方账号|对方账号名称|对方开户行|交易时间戳|子账簿号

请注意：子账簿号字段的生成规则为获取明细的摘要列，长度如果超过 6 的，获取最后 7

个字符，且 7 个字符的第一个为 :（半角冒号），且后 6 位为纯数字的，则在子账簿号位

置填写后 6 位数字。以上有一个条件不满足的，子账簿号处留空。

51 / 110

民生银行银企直联

11.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="virtualDetailLoad">

<requestHeader>

<dtClient>2012-05-11 11:08:33</dtClient>

<clientId>2200009314</clientId>

<userId>2200009314001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>liukaixuan</trnId>

<acntNo>*********</acntNo>

<dateFrom>2020-06-16</dateFrom>

<dateTo>2020-09-16</dateTo>

<startNo>1</startNo>

<queryRows>20</queryRows>

<typeCode></typeCode>

<vittualCapacity>6</vittualCapacity>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="virtualDetailLoad" security="none" lang="chs"

header="100" version="100" >

<responseHeader>

<status>

<code>

0

</code>

<severity>

info

52 / 110

民生银行银企直联

</severity>

<message>

ok

</message>

</status>

<dtServer>

2020-09-17 10:05:38

</dtServer>

<userKey>

N

</userKey>

<dtDead>

</dtDead>

<language>

chs

</language>

</responseHeader>

<xDataBody>

<trnId>

liukaixuan

</trnId>

<totalNum>

25

</totalNum>

<dataStream>

交易日期|主机交易流水号|借方发生额|贷方发生额|账户余额|凭证种类|凭证号|摘

要|银行附言|交易地点|对方账号|对方账号名称|对方开户行|交易时间戳|子账簿号

|0x0A20200620|00000000000000000000000000000000|0.00|2283.20|1316

145.29|||结息

||||||20200621024218.1642371|0x0A20200710|31301202007097966984087

544404D8B|0.00|12.00|1316157.29|||往来结算款|往来结算款||609896095|网银

互联测试|中国民生银行股份有限公司长春民丰大街支行

|20200709223606.6532001|0x0A20200908|5980120200908SP10000123544

3EA664|161.00|0.00|1315996.29|4098000|07150201|取款|||612335718|奎屯

琼海捷拓紫荆休闲娱乐公司|中国民生银行股份有限公司长春民丰大街支行

|20200908145151.5362221|0x0A20200908|5980120200908SP10000124544

412496|160.00|0.00|1315836.29|4098000|07150201|取款|||612335718|奎屯

53 / 110

琼海捷拓紫荆休闲娱乐公司|中国民生银行股份有限公司长春民丰大街支行

|20200908145152.5888141|0x0A20200908|5980120200908SP10000127544

3EA8B2|166.50|0.00|1315669.79|4098000|00125486|取款

|||60979784564834333|皇冠集团公司|中国工商银行股份有限公司四川省绵阳剑门支

民生银行银企直联

行

|20200908150152.6265221|0x0A20200908|5980120200908SP10000127544

3EA8B2|2.00|0.00|1315667.79|||手续费|||||中国民生银行股份有限公司苏州分行

|20200908150152.7135681|0x0A20200908|5980120200908SP10000128544

3EA8B6|166.50|0.00|1315501.29|4098000|07150201|取款

|||67645621545897222|奎沧桑集团公司|中国工商银行股份有限公司四川省绵阳剑门

支行

|20200908150154.8385761|0x0A20200908|5980120200908SP10000128544

3EA8B6|2.00|0.00|1315499.29|||手续费|||||中国民生银行股份有限公司苏州分行

|20200908150154.9314391|0x0A20200909|31301202009097967856049544

3F0DA7|400.00|0.00|1315099.29|4098000|09094834|4111|汇兑

||676456897335857|琼海|中国农业银行股份有限公司

|20200909095048.5770791|0x0A20200909|31301202009097967861594544

3F2FE6|1451.00|0.00|1313648.29|4098000|09145150|跨行对公

|||6225875154|琼海集团公司测试|中国工商银行股份有限公司四川省绵阳剑门支行

|20200909145511.9144991|0x0A20200909|31301202009097967861594544

3F2FE6|2.00|0.00|1313646.29|||手续费|||||中国民生银行股份有限公司苏州分行

|20200909145511.9930851|0x0A20200909|31301202009097967861968544

40D1A8|1504.00|0.00|1312142.29|4098000|09150716|本行对公|本行对公

||649597313|理财 uat 荟速网络检测仪器有限公司|中国民生银行股份有限公司长春民

丰大街支行

|20200909150950.6180471|0x0A20200909|31301202009097967862251544

3F3228|51504.00|0.00|1260638.29|4098000|09151608|取款|||6225875154|琼

海集团|中国工商银行股份有限公司四川省绵阳剑门支行

|20200909151824.3655221|0x0A20200909|31301202009097967862251544

3F3228|8.40|0.00|1260629.89|||手续费|||||中国民生银行股份有限公司苏州分行

|20200909151824.4659761|0x0A20200909|31301202009097967862576544

40DC43|55000.00|0.00|1205629.89|4098000|09152937|取款|||6225875154|

琼海集团|中国工商银行股份有限公司四川省绵阳剑门支行

54 / 110

民生银行银企直联

|20200909153155.1412811|0x0A20200909|31301202009097967862576544

40DC43|8.40|0.00|1205621.49|||手续费|||||中国民生银行股份有限公司苏州分行

|20200909153155.2410901|0x0A20200909|31301202009097967862622544

3F3808|55000.00|0.00|1150621.49|4098000|09153147|取款|||6225875154|琼

海集团|中国工商银行股份有限公司四川省绵阳剑门支行

|20200909153342.0097811|0x0A20200909|31301202009097967862622544

3F3808|8.40|0.00|1150613.09|||手续费|||||中国民生银行股份有限公司苏州分行

|20200909153342.0960301|0x0A20200909|31301202009097967863007544

3F385D|55000.00|0.00|1095613.09|4098000|09153736|取款|||6225875154|

琼海集团|中国工商银行股份有限公司四川省绵阳剑门支行

|20200909153937.6824711|0x0A20200909|31301202009097967863007544

3F385D|8.40|0.00|1095604.69|||手续费|||||中国民生银行股份有限公司苏州分行

|20200909153937.7799271|0x0A

</dataStream>

</xDataBody>

</CMBC>

12.银企直联智能账簿转账(B2EVirtualAcctTransTo

EntAcct)

本部分更新日期:2022-09-23

银企直联智能账簿转账，一个账户的是智能账簿对另一个实体账户进行转账，同时支持行

内转账和跨行转账。

交易要求：

1：实体账号必须是该公司自己的账号且加挂网银

2：操作员必须对该实体账号具备查询并转账权限

3：支持授权账户转账

12.1.请求(B2EVirtualAcctTransToEntAcct)

  标记

说明

是否

长度

必输

55 / 110

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <insId>

指令 ID，一条转账指令在客户端的唯一标

识

 <PayerAcNo>

付款方账号(格式是：实账号-智能账簿)

 <PayerAcName>

付款方智能账簿账户名

 <PayeeAcNo>

收款方账号

 <PayeeAcName>

收款方户名

 <externBank>

是否跨行(0:同行，1：跨行)

 <PayChannel>

汇路

民生银行银企直联

是否

长度

必输

Y

Y

Y

Y

Y

Y

Y

N

64

64

32

60

32

60

1

1

行内转账输入空值；

跨行转账：

1-大额；

2-小额；

3-网银互联；

4-上海同城。

 <PayeeAcType>

收款方类型

Y

1

0：对公

1：对私

 <BankCode>

收款方行号，跨行

 <PayeeDeptName> 收款行行名

 <PayerAcAddr>

付款方地址

12

80

N

N

N

56 / 110

民生银行银企直联

是否

长度

必输

Y

N

N

15,2

22

8

是否

长度

必返

32

32

Y

Y

Y

  标记

说明

 <Amount>

转账金额

 <Usage>

摘要/用途

 <CertNo>

凭证号(8 位以内的数字)

</xDataBody>

12.2.响应(B2EVirtualAcctTransToEntAcct)

  标记

说明

<xDataBody>

 <cash>

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

客户端交易的唯一标志

 <cash>

</xDataBody>

12.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctTransToEntAcct">

<requestHeader>

<dtClient>2015-05-08 15:18:40</dtClient>

57 / 110

民生银行银企直联

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>14d3263f15d1</trnId>

<insId>1212121212121212121</insId>

<PayerAcNo>*********-011001</PayerAcNo>

<PayerAcName>011001</PayerAcName>

<PayeeAcNo>10086</PayeeAcNo>

<PayeeAcName>中国移动通信</PayeeAcName>

<externBank>1</externBank>

<PayChannel>2</PayChannel>

<PayeeAcType>1</PayeeAcType>

<BankCode>************|</BankCode>

<PayeeDeptName>中国工商银行股份有限公司上海市盈港路支行

</PayeeDeptName>

<PayerAcAddr>北京</PayerAcAddr>

<Amount>10000.00</Amount>

<Usage>ss</Usage>

<CertNo>ss</CertNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualAcctTransToEntAcct" security="none"

lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

58 / 110

<dtServer>2015-10-28 10:46:00</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<cash>

<trnId>14d3263f15d1</trnId>

<svrId></svrId>

<insId>12121212121212121211211111aaaa</insId>

</cash>

</xDataBody>

</CMBC>

13.银企直联智能账簿单笔费用报销(B2EVirtualCostR

eimb)

本部分更新日期:2021-04-02

业务逻辑：
1：对于单笔金额大于等于五万元的，其款项用途的选择须符合中国人民银行《人民币银

行结算账户管理办法》的规定。中国人民银行规定的用途有：个人投资本金和收益；个人

债券产权转让收益；个人贷款转存；证券结算金期货保证金；个人继承、赠予款项；保险

理赔、保费退还；个人纳税退还；农副矿产品销售收入；其它合法收入-律师费；其它合

法收入-柜台销售结算款；其它合法收入-网上交易款；其它合法收入-资金退还；其它合

法收入-拆迁补偿款；其它合法收入-垫付资金和其它合法收入-小件商品销售收入。

2：其他限额见企业网银相应交易

3：支持授权账户

13.1.请求(B2EVirtualCostReimb)

  标记

说明

是否

长度

必输

59 / 110

  标记

说明

<xDataBody>

民生银行银企直联

是否

长度

必输

 <trnId>

客户端产生的交易唯一标志（必输，但无作

Y

64

用）

 <insId>

指令 ID，一条转账指令在客户端的唯一标识 Y

 <PayerAcNo>

付款账号

 <PayeeAcNo>

收款账号

 <PayeeAcName> 收款人名称

 <payeeAcctType> 收款账户类型：

1--本行卡

2--本行活折

3--他行卡折

Y

Y

Y

N

64

32

32

60

1

<PayChannel>

汇路（payeeAcctType 为 3--他行卡折时

N

32

必输）

行内转账：

输空值

跨行转账：

1-大额

2-小额

3-网银互联

<BankCode>

收款方行号，当 payeeAcctType 为 3--他

N

12

行卡折时必输）

 <Amount>

转账金额

Y

15,2

60 / 110

  标记

说明

民生银行银企直联

是否

长度

必输

 <Explain>

摘要代码/备注

Y

50

备注可不输入，对应转义的中文+备注总长

度不得大于 50，例如：“311” 或者

“311|测试”

 <CertNo>

企业自制凭证（非必输）

8

</xDataBody>

摘要字段根据下面的列表输入摘要代码：

摘要代码 用途描述

311 报销费用-差旅费

312 报销费用-办公费

313 报销费用-水电费

314 报销费用-通讯费

315 报销费用-交通费

316 报销费用-报刊费

317 报销费用-餐费

318 报销费用-医药费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

371 其它合法收入-租赁费

372 其它合法收入-运费

373 其它合法收入-工程款

374 其它合法收入-律师费

375 其它合法收入-资金退还

376 其它合法收入-拆迁补偿款

377 其它合法收入-垫付资金

61 / 110

378 其它合法收入-柜台销售结算款

379 其它合法收入-网上交易款

380 其它合法收入-小件商品销售收入

13.2.响应(B2EVirtualCostReimb)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

民生银行银企直联

是否

长度

必输

Y

Y

64

64

</xDataBody>

13.3.例子

请求报文

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualCostReimb">

<requestHeader>

<dtClient>20131125 15:47:11</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>100</appVer>

</requestHeader>

<xDataBody>

<trnId>251547230024</trnId>

<insId>3cmbc251547230024012</insId>

<PayerAcNo>*********-000001</PayerAcNo>

<PayeeAcNo>6226222900823084</PayeeAcNo>

62 / 110

民生银行银企直联

<PayeeAcName>厦门同城一</PayeeAcName>

<payeeAcctType>1</payeeAcctType>

<Amount>1.00</Amount>

<Explain>311</Explain>

<CertNo>12345678</CertNo>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2EVirtualCostReimb"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2010-02-22 10:16:13</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>251547230024</trnId>

<insId>3cmbc251547230024012</insId>

</xDataBody>

</CMBC>

14.智能账簿批量费用报销(VirtualBatchCostReimb)

本部分更新日期:2025-02-25

业务逻辑：

1：对于单笔金额大于等于五万元的，其款项用途的选择须符合中国人民银行《人民币银

行结算账户管理办法》的规定。中国人民银行规定的用途有：个人投资本金和收益；个人

63 / 110

债券产权转让收益；个人贷款转存；证券结算金期货保证金；个人继承、赠予款项；保险

理赔、保费退还；个人纳税退还；农副矿产品销售收入；其它合法收入-律师费；其它合

法收入-柜台销售结算款；其它合法收入-网上交易款；其它合法收入-资金退还；其它合

法收入-拆迁补偿款；其它合法收入-垫付资金和其它合法收入-小件商品销售收入。

民生银行银企直联

2：分行控制限额与企网对应交易一致。

3：此交易，一个批次最大笔数为 200

4：支持授权账户

14.1.请求(VirtualBatchCostReimb)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <insId>

客户端交易的唯一标志

 <PayerAcNo> 付款账号账号

 <totalRow>

总笔数

 <totalAmt>

总金额

 <Usage>

用途

 <fileContent> 竖线“|”分割数据元素，以尖号“^”为数据行分割

符

</xDataBody>

用途字段 / 摘要：

311 报销费用-差旅费

312 报销费用-办公费

是否

长度

必输

64

50

Y

Y

Y

Y

Y

N

Y

64 / 110

民生银行银企直联

313 报销费用-水电费

314 报销费用-通讯费

315 报销费用-交通费

316 报销费用-报刊费

317 报销费用-餐费

318 报销费用-医药费

341 个人投资本金和收益

342 个人债券产权转让收益

343 个人贷款转存

344 证券结算金期货保证金

345 个人继承、赠予款项

346 保险理赔、保费退还

347 个人纳税退还

348 农副矿产品销售收入

371 其它合法收入-租赁费

372 其它合法收入-运费

373 其它合法收入-工程款

374 其它合法收入-律师费

375 其它合法收入-资金退还

376 其它合法收入-拆迁补偿款

377 其它合法收入-垫付资金

378 其它合法收入-柜台销售结算款

379 其它合法收入-网上交易款

380 其它合法收入-小件商品销售收入

14.1.1.fileContent 支付数据格式：

收款账号|收款账号名称|备注|金额|凭证号|汇路|行号

  域名称

 <收款账号>

说明

必输

 <收款账号名称> 必输

 <备注>

可不输入(暂不支持)

长度

32

60

65 / 110

民生银行银企直联

长度

15,2

8

2

  域名称

 <金额>

说明

必输

 <凭证号>

企业自制凭证号(非必输)

<汇路>

非必输

行内转账输入 0 或不输

跨行交易汇路：

1-大额实时支付

2-小额实时支付

3-网银互联

<行号>

当汇路有值且不为 0 时必输

12

14.2.响应(VirtualBatchCostReimb)

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

返回的总条数（全部数据量）

</xDataBody>

14.3.例子

请求报文

是否

长度

必返

64

64

Y

N

Y

66 / 110

<?xml version="1.0" encoding="GB2312" ?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="VirtualBatchCostReimb">

<requestHeader>

<dtClient>2015-09-15 20:05:29</dtClient>

<clientId>**********</clientId>

<userPswd>123123</userPswd>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>1442318729741320094</trnId>

<insId>201611020011111</insId>

<cltcookie></cltcookie>

<PayerAcNo>*********-111111</PayerAcNo>

<totalAmt>3</totalAmt>

<totalRow>2</totalRow>

<fileContent>6226223300313064|王晓成||13424|1.0^6226223300313163|

纪兵成||15688|2.0</fileContent>

<Usage>379</Usage>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="VirtualBatchCostReimb" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-22 10:34:54</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

67 / 110

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<batch>

<trnId>1442318729741320094</trnId>

<insId>201611020011111</insId>

</batch>

</xDataBody>

</CMBC>

15.智能账簿批量转账(B2EVirtualBatchXfer)

本部分更新日期:2023-07-17

1.该交易支持智能账簿批量对外转账和批量智能账簿间的转账

2.智能账簿对外转账时不支持行内公转私业务

3.一个批次最大笔数为 200

4.本接口返回的成功标记只代表申请已接受，具体每笔交易状态需通过智能账簿批量转账

查询接口查询。

5.该接口暂不支持授权账户。

15.1.请求(B2EVirtualBatchXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

 <PayerAcNo>

付方虚拟账户（格式：实账号-虚账号）

 <PayerAcName> 付方虚拟账户名称

 <PayType>

转账类型：

是否

长度

必输

Y

Y

Y

Y

Y

64

68 / 110

  标记

说明

0:智能账簿对外转账

1:智能账簿间转账

 <TotalRow>

总笔数

 <TotalAmt>

总金额

民生银行银企直联

是否

长度

必输

Y

Y

Y

15,2

 <fileContent>

竖线“|”分割数据元素，以尖号“^”为数据行分

割符，具体格式定义与转账类型有关

</xDataBody>

支付文件数据格式<fileContent>：

1、智能账簿对外转账

汇路|凭证号|收款账户类型|收款账号|收款户名|同城异地标识|收款行联行号|收款行名|金

额|备注

  域名称

 <汇路>

长度

2

说明

必输，

行内交易输入 0

跨行交易汇路为:

1 大额实时支付

2 小额实时支付

3 网银互联汇路

4 上海同城

 <凭证号：输入企业自制凭证号>

非必输， 用于企业 ERP 系统更新

8

流水状态的标识，目前支持 8 位数

字

69 / 110

  域名称

 <收款账户类型>

 <收款账号>

 <收款户名>

说明

必输

0.企业

1.个人卡

2.个人折

必输

必输

民生银行银企直联

长度

1

32

60

 <同城异地标识>

小额汇路必输：

0-同城；

1-异地

 <收款行联行号>

行内转账不用输入； 跨行转账必

12

输

 <收款行名>

行内转账非必输 跨行转账必须输

40

 <金额>

 <备注>

入。

必输

非必输

15，

2

30

2、智能账簿间的转账

收方智能账簿|收方智能账簿名称|交易类型|交易金额|备注

  域名称

说明

长度

 <收方智能账簿>

必输(格式：实账号-智能账簿)

 <收方智能账簿名称> 必输

70 / 110

民生银行银企直联

长度

1

15，2

30

是否必返 长度

Y

N

64

32

  域名称

说明

 <交易类型>

0-智能账簿间转账

 <交易金额>

必输

 <备注>

非必输

15.2.响应(B2EVirtualBatchXfer)

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志

 <insId>

服务器该笔交易的标识

</xDataBody>

15.3.例子

请求报文

<?xml version="1.0" encoding="GB2312" standalone="no"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualBatchXfer">

<requestHeader>

<dtClient>2023-07-17 14:44:55</dtClient>

<clientId>2200763112</clientId>

<userId>2200763112001</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>client123456</trnId>

71 / 110

民生银行银企直联

<cltcookie></cltcookie>

<insId>123456</insId>

<PayerAcNo>*********-000001</PayerAcNo>

<PayerAcName>测试 1</PayerAcName>

<PayType>1</PayType>

<TotalAmt>1.1</TotalAmt>

<TotalRow>2</TotalRow>

<fileContent>*********-000002|测试 2|0|1.0|测试数据库^*********-

000002|测试 2|0|0.1|测试数据库</fileContent>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualBatchXfer" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-22 10:04:28</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<batchTransfers>

<trnId>client20152245500001</trnId>

<insId>2016110916290000s1</insId>

</batchTransfers>

</xDataBody>

</CMBC>

2、批量智能账簿间转账

请求报文：

72 / 110

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="B2EVirtualBatchXfer">

<requestHeader>

<dtClient>2015-02-03 14:44:55</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>client20152245500001</trnId>

<cltcookie></cltcookie>

<insId>201611141dd629000g08</insId>

<PayerAcNo>*********-111111</PayerAcNo>

<PayerAcName>计息测试账号</PayerAcName>

<PayType>1</PayType>

<TotalAmt>1.1</TotalAmt>

<TotalRow>1</TotalRow>

<fileContent>*********-123000|虚拟测试账户|0|智能账簿间转账

|1.1</fileContent>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualBatchXfer" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-22 10:11:46</dtServer>

<userKey>N</userKey>

73 / 110

民生银行银企直联

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<batchTransfers>

<trnId>client20152245500001</trnId>

<insId>201611141dd629000g08</insId>

</batchTransfers>

</xDataBody>

</CMBC>

16.智能账簿批量转账查询(QryB2EVirtualBatchXfe

r)

本部分更新日期:2021-04-02

16.1.请求(QryB2EVirtualBatchXfer)

  标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

客户端交易的唯一标志

</xDataBody>

16.2.响应(QryB2EVirtualBatchXfer)

  标记

说明

是否

长度

必输

Y

Y

64

32

64

是否

长度

必返

74 / 110

民生银行银企直联

是否

长度

必返

64

32

64

Y

Y

Y

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

客户端交易的唯一标志

 <fileContent> 竖线“|”分割数据元素，以尖号“^”为数据行分

割符，具体格式定义与转账类型有关

</xDataBody>

fileContent 定义：

1.智能账簿批量对转账：

凭证号|付款账号|付款账号名称|收款账号|收款账号名称|收款账号类型|收款账户开户行||收

款账户开户行名称|交易金额|备注|交易状态码|错误信息

交易状态码说明：

0-交易处理中

1-成功

2-失败

3-交易状态未知

2.批量智能账簿间转账：

付款账号|付款账号名称|收款账号|收款账号名称|交易类型|交易金额|备注|交易状态码|错误

信息

交易状态码说明：

0-交易处理中

1-成功

75 / 110

2-失败，

3-交易状态未知

16.3.例子

民生银行银企直联

1、智能账簿批量对外转账查询 请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryB2EVirtualBatchXfer">

<requestHeader>

<dtClient>2015-02-03 14:44:55</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>client20152245500001</trnId>

<cltcookie></cltcookie>

<insId>2016110916290000s1</insId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryB2EVirtualBatchXfer" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-22 10:08:56</dtServer>

<userKey>N</userKey>

76 / 110

民生银行银企直联

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>批量支付受理成功！</statusErrMsg>

</statusId>

<batchTransfers>

<trnId>client20152245500001</trnId>

<insId>2016110916290000s1</insId>

<fileContent>*************|*********|厦门测试

**********|6226220000006666|门家祥|1|301561000020|交通银行股份有限公

司益阳资阳支行|123.00||1|success|^</fileContent>

</batchTransfers>

</xDataBody>

</CMBC>

批量智能账簿间转账查询

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryB2EVirtualBatchXfer">

<requestHeader>

<dtClient>2015-02-03 14:44:55</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>client20152245500001</trnId>

<cltcookie></cltcookie>

<insId>201611141dd629000g08</insId>

77 / 110

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryB2EVirtualBatchXfer" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-22 10:14:16</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>批量支付受理成功！</statusErrMsg>

</statusId>

<batchTransfers>

<trnId>client20152245500001</trnId>

<insId>201611141dd629000g08</insId>

<fileContent>*********-111111|计息测试账号|*********-123000|虚拟

测试账户|0|1.10|智能账簿间转账|1|success|^</fileContent>

</batchTransfers>

</xDataBody>

</CMBC>

17.智能账簿批量费用报销查询(qryVirtualBatchCos

tReimb)

本部分更新日期:2025-02-25

78 / 110

17.1.请求(qryVirtualBatchCostReimb)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <insId>

指令 ID，一条转账指令在客户端的唯一标识

</xDataBody>

17.2.响应(qryVirtualBatchCostReimb)

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志

 <insId>

客户端交易的唯一标志

 <fileContent> 竖线“|”分割数据元素，以尖号“^”为数据行分

民生银行银企直联

是否

长度

必返

Y

Y

64

64

是否

长度

必返

64

64

Y

Y

Y

割符

</xDataBody>

fileContent 格式说明：

凭证号|收款账号|收款账号名|备注(预留，暂不支持)|交易金额交易状态码|错误信息

交易状态码说明：

79 / 110

民生银行银企直联

0-成功

1-失败

17.3.例子

请求报文

<?xml version="1.0" encoding="GB2312" ?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="qryVirtualBatchCostReimb">

<requestHeader>

<dtClient>2015-09-15 20:05:29</dtClient>

<clientId>**********</clientId>

<userPswd>123123</userPswd>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>1442318729741320094</trnId>

<insId>201611020011111</insId>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="qryVirtualBatchCostReimb" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-22 10:39:05</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

80 / 110

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<statusId>

<statusCode>0</statusCode>

<statusSeverity>ok</statusSeverity>

<statusErrMsg>批量支付受理成功！</statusErrMsg>

</statusId>

<batchTransfers>

<trnId>1442318729741320094</trnId>

<insId>201611020011111</insId>

<fileContent>13424|6226223300313064|王晓成||1.00|0|成功

|^15688|6226223300313163|纪兵成||2.00|0|成功|^

</fileContent>

</batchTransfers>

</xDataBody>

</CMBC>

18.银企直联智能账簿间调账 (B2EVirtualAcctAdjus

t)

本部分更新日期:2021-07-05

对结息明细或已调账的明细不能再进行调账

18.1.请求(B2EVirtualAcctAdjust)

  标记

说明

<xDataBody>

是否

长度

必返

 <trnId>

客户端产生的交易唯一标志

Y

64

 <cltcookie>

可选，客户端 cookie，响应时原值返回 N

81 / 110

  标记

说明

民生银行银企直联

是否

长度

必返

 <insId>

指令 ID，一条转账指令在客户端的唯一

Y

64

标识

 <PayerAcNo>

付款账号(格式：实账户-虚账户)

 <PayerAcName>

付款账户名

 <PayeeAcNo>

收款账号(格式是：实账号-智能账簿)

 <PayeeAcName>

收款账户名称

 <SerialNo>

交易流水号

 <SubSerialNo>

交易子流水号

 <Amount>

转账金额

 <TransDate>

交易日期，如 2019-01-18

 <Usage>

用途

</xDataBody>

18.2.响应(B2EVirtualAcctAdjust)

  标记

说明

<xDataBody>

 <trnId>

客户端交易的唯一标志

 <svrId>

服务器该笔交易的标识

 <insId>

客户端交易的唯一标志

Y

Y

N

N

Y

Y

N

N

N

32

60

39

60

39

60

15,2

10

22

是否

长度

必返

32

32

Y

Y

Y

82 / 110

民生银行银企直联

是否

长度

必返

  标记

说明

</xDataBody>

18.3.例子

请求报文

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualAcctAdjust">

<requestHeader>

<dtClient>2013-01-08 11:01:33</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<cltcookie></cltcookie>

<insId>20170104000001</insId>

<PayerAcNo>*********-000000</PayerAcNo>

<PayerAcName>总账簿 </PayerAcName>

<PayeeAcNo>*********-000002</PayeeAcNo>

<PayeeAcName>101</PayeeAcName>

<SerialNo>39432131</SerialNo>

<SubSerialNo>50301201901180039432131503375556</SubSerialNo>

<TransDate>2019-01-18</TransDate>

<Usage>银企直联虚账户间调账</Usage>

</xDataBody>

</CMBC>

响应报文

83 / 110

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="B2EVirtualAcctAdjust" header="100"

民生银行银企直联

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>ok</severity>

<message>info</message>

</status>

<dtServer>2017-01-14 10:10:58</dtServer>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>option05dzd001</trnId>

<insId>20170104000001</insId>

</xDataBody>

</CMBC>

19.智能账簿利息清单查询(B2EVirtualInterestQry)

本部分更新日期:2021-04-02

此功能可查询两年以内的利息清单明细

查询区间最大为 1 年

19.1.请求(B2EVirtualInterestQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <AcNo>

实体账号

长度

是否

必输

64

Y

Y

84 / 110

  标记

说明

 <VirtualAcNo>

智能账簿

 <VirtualAcName> 智能账簿名称

 <StartDate>

起始日期(yyyy-MM-dd)

 <EndDate>

结束日期(yyyy-MM-dd)

</xDataBody>

19.2.响应(B2EVirtualInterestQry)

民生银行银企直联

长度

是否

必输

Y

Y

Y

Y

  标记

说明

是否

长度

必返

<xDataBody>

 <trnId>

客户端交易的唯一标志

Y

64

 <totalNum>

当前笔数

 <List>

  <Map>

   <AccrualDate>

计/结息时间

   <VirtualAcNo>

智能账簿

   <VirtualAcName>

智能账簿名称

   <AccrualBeginDate> 计息开始时间

   <AccrualEndDate>

计息结束时间

   <LoanFlag>

借贷标识

N

85 / 110

民生银行银企直联

是否

长度

必返

  标记

说明

   <Accrual>

实际利息

   <AssignFlag>

分配标识：

0-不处理

1-正在处理

2-已分配

3-分配失败。

  <Map>

 <List>

</xDataBody>

19.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualInterestQry">

<requestHeader>

<dtClient>2015-01-08 10:46:20</dtClient>

<clientId>2200006457</clientId>

<userId>2200006457001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>BAT07201trnID</trnId>

<AcNo>*********</AcNo>

<VirtualAcNo>000004</VirtualAcNo>

<VirtualAcName>测试 05</VirtualAcName>

86 / 110

民生银行银企直联

<StartDate>2016-10-01</StartDate>

<EndDate>2016-11-28</EndDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualInterestQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-11-28 15:31:26</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>BAT07201trnID</trnId>

<totalNum>4</totalNum>

<List>

<Map>

<AccrualDate>2016-11-22</AccrualDate>

<VirtualAcNo>000004</VirtualAcNo>

<VirtualAcName>测试 05</VirtualAcName>

<AccrualBeginDate>2016-11-08</AccrualBeginDate>

<AccrualEndDate>2016-11-21</AccrualEndDate>

<LoanFlag>C</LoanFlag>

<Accrual>793.13</Accrual>

<AssignFlag>0</AssignFlag>

</Map>

</List>

</xDataBody>

</CMBC>

87 / 110

民生银行银企直联
20.智能账簿利息清单下载(B2EVirtualInterestDow

nLoad)

本部分更新日期:2021-04-02

1、此功能可查询两年以内的利息清单明细

2、查询区间最大为 3 个月

3、该接口查询实体账户下所有虚拟子账户的利息清单

20.1.请求(B2EVirtualInterestDownLoad)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <AcNo>

实体账号

 <StartDate>

起始日期(yyyy-MM-dd)

 <EndDate>

结束日期(yyyy-MM-dd)

 <pageSize>

查询笔数

 <currentIndex> 起始笔数

</xDataBody>

20.2.响应(B2EVirtualInterestDownLoad)

  标记

说明

<xDataBody>

是否

长度

必输

64

Y

Y

Y

Y

N

N

是否

长度

必返

88 / 110

  标记

说明

 <trnId>

客户端交易的唯一标志（★）

 <totalNum>

当前笔数

 <List>

  <Map>

   <AccrualDate>

计/结息时间

   <VirtualAcNo>

智能账簿

   <VirtualAcName>

智能账簿名称

   <AccrualBeginDate> 计息开始时间

   <AccrualEndDate>

计息结束时间

   <LoanFlag>

借贷标识

   <Accrual>

实际利息

分配标识：

0-不处理

1-正在处理

2-已分配

3-分配失败。

   <AssignFlag>

  <Map>

 <List>

</xDataBody>

民生银行银企直联

是否

长度

64

必返

Y

Y

N

89 / 110

民生银行银企直联

20.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualInterestDownLoad">

<requestHeader>

<dtClient>2015-01-08 10:46:20</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>BAT07201trnID</trnId>

<AcNo>*********</AcNo>

<currentIndex>2</currentIndex>

<pageSize>10</pageSize>

<StartDate>2017-06-01</StartDate>

<EndDate>2017-06-21</EndDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualInterestDownLoad" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2017-06-22 10:46:20</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

90 / 110

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>BAT07201trnID</trnId>

<allNum>4</allNum>

<totalNum>3</totalNum>

<List>

<Map>

<AccrualDate>2017-06-08</AccrualDate>

<VirtualAcNo>000002</VirtualAcNo>

<VirtualAcName>101</VirtualAcName>

<AccrualBeginDate>2017-05-31</AccrualBeginDate>

<AccrualEndDate>2017-06-07</AccrualEndDate>

<LoanFlag>D</LoanFlag>

<Accrual>-0.90</Accrual>

<AssignFlag>2</AssignFlag>

</Map>

<Map>

<AccrualDate>2017-06-12</AccrualDate>

<VirtualAcNo>000001</VirtualAcNo>

<VirtualAcName>100</VirtualAcName>

<AccrualBeginDate>2017-06-08</AccrualBeginDate>

<AccrualEndDate>2017-06-11</AccrualEndDate>

<LoanFlag>C</LoanFlag>

<Accrual>4641.60</Accrual>

<AssignFlag>2</AssignFlag>

</Map>

<Map>

<AccrualDate>2017-06-12</AccrualDate>

<VirtualAcNo>000002</VirtualAcNo>

<VirtualAcName>101</VirtualAcName>

<AccrualBeginDate>2017-06-08</AccrualBeginDate>

<AccrualEndDate>2017-06-11</AccrualEndDate>

<LoanFlag>D</LoanFlag>

<Accrual>-0.52</Accrual>

<AssignFlag>2</AssignFlag>

</Map>

</List>

91 / 110

</xDataBody>

</CMBC>

民生银行银企直联

21.智能账簿批量产品账号明细查询(B2EVirtualBatch

DtlQry)

本部分更新日期:2021-11-01

21.1.请求(B2EVirtualBatchDtlQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标识

 <acNo>

实体账户

 <qryStartDate> 起始日期(格式：yyyy-MM-dd)

 <qryEndDate>

结束日期(格式：yyyy-MM-dd)

 <reserveField1> 备用字段

</xDataBody>

21.2.响应(B2EVirtualBatchDtlQry)

  标记

说明

<xDataBody>

服务消息集

是否

长度

必输

Y

Y

Y

Y

64

32

10

10

是否

长度

必返

92 / 110

  标记

说明

 <trnId>

客户端产生的交易唯一标志

 <recordNumber>

总笔数

 <reserveField1>

备用字段（未启用）

 <reserveField2>

备用字段（未启用）

 <List>

  <Map>

   <transDate>

交易日期(yyyy-MM-dd)

   <serialNo>

交易流水号

   <subSerialNo>

交易子流水号

   <virtualAcNo>

产品账号

   <transTime>

交易时间

   <loanAmount>

借方金额

   <lendAmount>

贷方金额

   <slfBalance>

自身余额

   <sertNo>

凭证号

   <oppAcNo>

对方账户

   <oppAcName>

对方户名

   <oppBankName> 对方开户行名称

   <postscript>

附言

民生银行银企直联

是否

长度

必返

Y

N

N

64

32

10

32

64

13

8

15,2

15,2

15,2

13

39

180

255

255

93 / 110

  标记

说明

   <purpose>

摘要

   <merId>

商户编码

   <reserveField3> 备用字段（未启用）

   <reserveField4> 备用字段（未启用）

民生银行银企直联

是否

长度

必返

255

40

  <Map>

 <List>

</xDataBody>

21.3.例子

请求字段：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EVirtualBatchDtlQry">

<requestHeader>

<dtClient>2021-11-01 14:23:13</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2021************</trnId>

<cltcookie></cltcookie>

<qryStartDate>2021-10-29</qryStartDate>

<qryEndDate>2021-10-29</qryEndDate>

<acNo>*********</acNo>

94 / 110

民生银行银企直联

<reserveField1></reserveField1>

</xDataBody>

</CMBC>

返回字段：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="B2EVirtualBatchDtlQry" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2021-11-01 14:24:42</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>CMBCTRN2021************</trnId>

<recordNumber>8</recordNumber>

<reserveField1></reserveField1>

<reserveField2></reserveField2>

<List>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>174532</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>400.56</lendAmount>

<slfBalance>**********.31</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>厦门测试 **********</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

95 / 110

民生银行银企直联

<postscript>TEST 民生 40056</postscript>

<purpose>TEST 民生 40056</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>155502</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>111.00</lendAmount>

<slfBalance>**********.75</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>版本票据测试 9</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

<postscript>1</postscript>

<purpose>1</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>154702</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>1542.00</lendAmount>

<slfBalance>**********.75</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>厦门测试 **********</oppAcName>

96 / 110

民生银行银企直联
<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

<postscript>测试同行</postscript>

<purpose>测试同行</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>154332</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>11.00</lendAmount>

<slfBalance>**********.75</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>厦门测试 **********</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

<postscript>同行对公</postscript>

<purpose>同行对公</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>153732</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>103.33</lendAmount>

<slfBalance>**********.75</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

97 / 110

<oppAcName>厦门测试 **********</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

民生银行银企直联

<postscript>SWHY 民生测试 10333</postscript>

<purpose>SWHY 民生测试 10333</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>121831</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>22.00</lendAmount>

<slfBalance>**********.42</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>厦门测试 **********</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

<postscript>无-CC3JLXPT2PT</postscript>

<purpose>无-CC3JLXPT2PT</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>111232</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>1.00</lendAmount>

<slfBalance>**********.42</slfBalance>

<sertNo>*************</sertNo>

98 / 110

<oppAcNo>*********</oppAcNo>

<oppAcName>厦门测试 **********</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

民生银行银企直联

<postscript>无-CC00000007</postscript>

<purpose>无-CC00000007</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

<Map>

<transDate>2021-10-29</transDate>

<serialNo>********</serialNo>

<subSerialNo>DM</subSerialNo>

<transTime>111102</transTime>

<virtualAcNo>999999</virtualAcNo>

<loanAmount></loanAmount>

<lendAmount>1.00</lendAmount>

<slfBalance>**********.42</slfBalance>

<sertNo>*************</sertNo>

<oppAcNo>*********</oppAcNo>

<oppAcName>厦门测试 **********</oppAcName>

<oppBankName>中国民生银行股份有限公司长春民丰大街支行</oppBankName>

<postscript>无-CC00000007</postscript>

<purpose>无-CC00000007</purpose>

<merId></merId>

<reserveField3></reserveField3>

<reserveField4></reserveField4>

</Map>

</List>

</xDataBody>

</CMBC>

99 / 110

民生银行银企直联
22.银企直连智能账簿子账簿权限维护(B2EOperatorA

uthVirtual)

本部分更新日期:2022-6-24

银企直连客户可以通过该接口对智能账簿子账簿进行分配权限，权限包括查询、查询并转
账

交易要求：

1：子账簿必须是该公司自己的账号；

2：银企直连客户必须为管理员用户。

22.1.请求(B2EOperatorAuthVirtual)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志

 <cltcookie>

可选，客户端 cookie，响应时原值返回

 <insId>

指令 ID，一条转帐指令在客户端的唯一标识

 <Authority>

操作权限

0—查询

2—修改并转账

 <AcNO>

签约帐号

 <VirtualAcNo> 子账簿号

 <Operator>

操作员号

</xDataBody>

是否

长度

必输

Y

N

Y

Y

Y

Y

Y

64

64

1

32

6

32

100 / 110

22.2.响应(B2EOperatorAuthVirtual)

民生银行银企直联

  标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端交易的唯一标志

 <svrId>

指令 ID，一条转帐指令在客户端的唯一标识

 <insId>

服务器该笔交易的标识

是否

长度

必返

Y

Y

32

32

64

</xDataBody>

22.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2EOperatorAuthVirtual">

<requestHeader>

<dtClient>2022-06-23 19:37:53.3</dtClient>

<clientId>2200398998</clientId>

<userId>2200398998001</userId>

<userPswd>896cev</userPswd>

<language>utf-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>389835099989</insId>

<trnId>38988999956</trnId>

<AcNO>*********</AcNO>

<VirtualAcNo>000000</VirtualAcNo>

<Operator>2200398998002</Operator>

<Authority>2</Authority>

101 / 110

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2EOperatorAuthVirtual"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>84412option05dzd001015</trnId>

<insId>option05dzd013</insId>

</xDataBody>

</CMBC>

23.智能账簿交易凭证和回单下载(B2EVirtualAcctTra

nsDownload)

本部分更新日期:2025-2-26

通过智能账簿银企直连接口下载智能账簿交易凭证和回单，且功能支持授权账户使用，授

权账户的操作不受智能账簿授权账户白名单管理功能的限制。

102 / 110

23.1.请求(B2EVirtualAcctTransDownload)

标记

说明

<xDataBody>

<trnId>

客户端产生的交易唯一标志

<insId>

指令 ID，一条转帐指令在客户端的唯一

标识

<acNo>

实账号

<fileType>

凭证类型：

0-交易回单；

1-交易凭证

<portalSerialNo> 原交易流水号

<transDate>

交易日期(yyyy-MM-dd)

</xDataBody>

23.2.响应(B2EVirtualAcctTransDownload)

标记

说明

<xDataBody>

民生银行银企直联

是否

长度

必输

Y

Y

Y

Y

Y

Y

64

64

32

1

32

10

是 否

长度

必返

<fileContent> 经 Base64 加密之后的字符串，在使用时应先

Y

解码为字节数组并写入文件

103 / 110

<insId>

指令 ID，一条转帐指令在客户端的唯一标识

Y

64

民生银行银企直联

</xDataBody>

23.3.例子

请求报文

<?xml version="1.0" encoding="GBK"?>

<CMBC header="100" version="100" security="none" lang="chs"
trnCode="B2EVirtualAcctTransDownload">

<requestHeader>

<dtClient>2024-12-11 08:43:40</dtClient>

<clientId>2300594346</clientId>

<userId>2300594346002</userId>

<userPswd>896cev</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2024121021044013</trnId>

<insId>CMBCINS2024121021044014</insId>

<acNo>*********-000000</acNo>

<portalSerialNo>31301202411257992389584713978182</portalSerialN

o>

<fileType>1</fileType>

<transDate>2024-11-25</transDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"
trnCode="B2EVirtualAcctTransDownload">

<responseHeader>

104 / 110

民生银行银企直联

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-12-11 08:44:46</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<insId>CMBCINS2024121021044014</insId>

<fileContent></fileContent>

</xDataBody>

</CMBC>

105 / 110

