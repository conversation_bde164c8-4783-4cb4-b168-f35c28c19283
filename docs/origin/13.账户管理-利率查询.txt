银企直联接口文档

（账户管理-利率查询）

邮箱：<EMAIL>

民生银行银企直联

文档修改记录

版本

日期

说明

编写者 审核者

V1.0.0 2021-03-18 定义接口文档

1 / 8

目录

民生银行银企直联

目录 .............................................................................................2

１ 利率查询(INTERESTSETTLEMENTQRYB2E) ....................................... 3

1.1. 请求(INTERESTSETTLEMENTQRYB2E) ............................................. 3

1.2. 响应(INTERESTSETTLEMENTQRYB2E) ............................................. 3

1.3. 例子 ...................................................................................... 4

2 / 8

１ 利率查询(InterestSettlementQryB2e)

民生银行银企直联

本部分更新日期:2021-04-02

业务逻辑：

查询本公司各账户（含授权账户）下活期定期保证金的利率。

1.1. 请求(InterestSettlementQryB2e)

  标记

说明

<xDataBody>

 <insId>

流水号

 <acNo>

账号

 <qryDate>

查询日期（形如：yyyy-MM-dd）

</xDataBody>

1.2. 响应(InterestSettlementQryB2e)

  标记

说明

<xDataBody>

 <List>

  <Map>

   <AcNo>

账号

   <Currency> 币种

   <CifName>

客户名称

3 / 8

民生银行银企直联

   <datePost>

结息日

   <dateValue> 起息日

   <calcFrom> 开始日期

   <calcTo>

结束日期

   <aAmount> 利息金额

   <Amount5> 含有 5 位小数的利息金额

   <base>

本金金额

   <rate>

计息利率

  </Map>

 </List>

</xDataBody>

1.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="InterestSettlementQryB2e">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>2200001876</clientId>

<userId>2200001876002</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

4 / 8

民生银行银企直联

<insId>1146asd72</insId>

<acNo>6********</acNo>

<currency>RMB</currency>

<qryDate>2015-04-01</qryDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="InterestSettlementQryB2e" security="none" lang="chs"

header="100" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2015-04-08 15:31:37</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<List>

<Map>

<bankKey>C001</bankKey>

<bankLand>CN</bankLand>

<bankName>中国民生银行</bankName>

<AcNo>6********</AcNo>

<Currency>RMB</Currency>

<CifName>CSAA 专用航协 2</CifName>

<datePost>********</datePost>

<dateValue>********</dateValue>

<calcFrom>********</calcFrom>

<calcFromTime>120000</calcFromTime>

<calcTo>********</calcTo>

<aAmount>0.11</aAmount>

<Amount5>0.10694</Amount5>

5 / 8

<base>10000.00</base>

<rate>0.**********</rate>

</Map>

<Map>

<bankKey>C001</bankKey>

<bankLand>CN</bankLand>

<bankName>中国民生银行</bankName>

<AcNo>6********</AcNo>

<Currency>RMB</Currency>

<CifName>CSAA 专用航协 2</CifName>

<datePost>********</datePost>

<dateValue>********</dateValue>

<calcFrom>********</calcFrom>

<calcFromTime>120000</calcFromTime>

<calcTo>********</calcTo>

<aAmount>0.22</aAmount>

<Amount5>0.22459</Amount5>

<base>21001.00</base>

<rate>0.**********</rate>

</Map>

<Map>

<bankKey>C001</bankKey>

<bankLand>CN</bankLand>

<bankName>中国民生银行</bankName>

<AcNo>6********</AcNo>

<Currency>RMB</Currency>

<CifName>CSAA 专用航协 2</CifName>

<datePost>********</datePost>

<dateValue>********</dateValue>

<calcFrom>********</calcFrom>

<calcFromTime>120000</calcFromTime>

<calcTo>********</calcTo>

<aAmount>32.76</aAmount>

<Amount5>32.75712</Amount5>

<base>1021001.00</base>

<rate>0.**********</rate>

</Map>

</List>

民生银行银企直联

6 / 8

</xDataBody>

</CMBC>

民生银行银企直联

7 / 8

