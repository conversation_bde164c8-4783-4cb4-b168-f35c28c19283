银企直联接口文档

（现金管理-全球现金池）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 33

目录

民生银行银企直联

目录 .............................................................................................2

1. 全球跨境现金池账户签约信息查询(CBACCSIGNINFOQRY) ........................ 4

1.1. 请求(CBACCSIGNINFOQRY) ......................................................... 4

1.2. 响应(CBACCSIGNINFOQRY) ......................................................... 4

1.3. 例子 ...................................................................................... 5

2. 全球跨境现金池余额查询(CBCASHPOOLACCBALANCEQRY) ................... 7

2.1. 请求(CBCASHPOOLACCBALANCEQRY) ............................................ 7

2.2. 响应(CBCASHPOOLACCBALANCEQRY) ............................................ 7

2.3. 例子 ...................................................................................... 9

3. 全球跨境现金池账户信息查询(CBCASHPOOLACCINFOQRY) ...................10

3.1. 请求(CBCASHPOOLACCINFOQRY) ................................................10

3.2. 响应(CBCASHPOOLACCINFOQRY) ................................................11

3.3. 例子 .................................................................................... 12

4. 全球跨境现金池汇总信息查询(CBCASHPOOLSUMINFOQRY) .................. 14

4.1. 请求(CBCASHPOOLSUMINFORMATIONQRY) .................................... 14

4.2. 响应(CBCASHPOOLSUMINFORMATIONQRY) .................................... 15

4.3. 例子 .................................................................................... 16

5. 全球跨境现金池客户关系树查询(CBCASHPOOLTREEQRY) ......................18

5.1. 请求(CBCASHPOOLTREEQRY) .................................................... 19

5.2. 响应(CBCASHPOOLTREEQRY) .................................................... 19

5.3. 例子 .................................................................................... 20

6. 银企直联全球跨境现金池本行明细下载(CBDETAILLOAD) ......................... 21

6.1. 请求(CBDETAILLOAD) ...............................................................22

6.2. 响应(CBDETAILLOAD) ...............................................................22

2 / 33

民生银行银企直联
6.3. 例子 .................................................................................... 23

7. 全球跨境现金池他行交易明细查询(CBOTHERBANKTRANSDETAILQRY) ....24

7.1. 请求(CBOTHERBANKTRANSDETAILQRY) ........................................25

7.2. 响应(CBOTHERBANKTRANSDETAILQRY) ........................................25

7.3. 例子 .................................................................................... 26

8. 全球跨境现金池本行交易明细查询(CBSELFBANKTRANSDETAILQRY) .......28

8.1. 请求(CBSELFBANKTRANSDETAILQRY) .......................................... 28

8.2. 响应(CBSELFBANKTRANSDETAILQRY) .......................................... 29

8.3. 请求报文 ............................................................................... 30

3 / 33

1.全球跨境现金池账户签约信息查询(CBAccSignInfoQ

民生银行银企直联

ry)

本部分更新日期:2021-04-02

账户签约查询

1.1.请求(CBAccSignInfoQry)

  标记

说明

<xDataBody>

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <accountId> 账号 ID（★）

12

 <extFields1> 备用字段（未启用）

 <extFields2> 备用字段（未启用）

</xDataBody>

1.2.响应(CBAccSignInfoQry)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <custName>

客户名称

 <regName>

注册地

 <account>

账户账号

长度

64

200

20

50

4 / 33

 <currencyCode> 币种代码

 <bankName>

开户行名称

 <countryName> 开户行所在国家/地区

 <accTypeName> 账户性质名称

民生银行银企直联

3

255

255

255

 <accType>

账户类型 1：境内分行账户，2：境外分行账户，

1

3：他行账户

 <accContact>

联系人

 <contactphone> 联系人手机号

 <upAccount>

主账户

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

1.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBAccSignInfoQry">

<requestHeader>

<dtClient>2019-01-23 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

50

50

50

5 / 33

民生银行银企直联

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<accountId>679</accountId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="CBAccSignInfoQry" header="100"

lang="chs"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-07-05 13:57:36</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<custName>新业务 08</custName>

<regName>中国</regName>

<account>*********</account>

<currencyCode>AUD</currencyCode>

<bankName>中国民生银行股份有限公司天津分行</bankName>

<countryName>中国</countryName>

<accTypeName>国内资金主账户</accTypeName>

<accType>1</accType>

<accContact>叶超</accContact>

<contactphone>***********</contactphone>

<upAccount></upAccount>

<asAccountNo></asAccountNo>

6 / 33

</xDataBody>

</CMBC>

民生银行银企直联

2.全球跨境现金池余额查询(CBCashPoolAccBalance

Qry)

本部分更新日期:2021-04-02

现金池余额查询

2.1.请求(CBCashPoolAccBalanceQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

 <accountId>

账号 ID（★）

 <account>

签约账号（★）

 <isCmbc>

是否民生银行标志 0-他行账户 1-民生银行账户（★）

 <accountType> 账户类型 1-境内分行账户；2-境外分行账户；3-他行账

户（★）

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

2.2.响应(CBCashPoolAccBalanceQry)

  标记

说明

长度

64

12

50

1

1

长度

7 / 33

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <allNum>

总记录数（★）

 <List>

  <Map>

   <custName>

客户名称

   <account>

账户账号

   <currencyCode> 币种代码

   <currencyName> 币种名称

   <balance>

账户余额

   <availBalance>

可用余额

民生银行银企直联

64

8

255

50

3

255

20

20

   <acState>

账户状态 0-新建，2-正常，3-注销（本行余额返

1

回）

   <bankName>

开户行

   <countryName>

开户行所在国家/地区（他行余额返回）

   <verifyDate>

对账日期（他行余额返回）

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

  </Map>

 </List>

</xDataBody>

255

100

10

8 / 33

民生银行银企直联

2.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBCashPoolAccBalanceQry">

<requestHeader>

<dtClient>2019-01-23 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<accountId>830</accountId>

<account>************</account>

<isCmbc>0</isCmbc>

<accountType>1</accountType>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="CBCashPoolAccBalanceQry"

header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-07-05 13:49:17</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

9 / 33

民生银行银企直联

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<allNum>1</allNum>

<List>

<Map>

<custName>Yunfeng Financial&#13;&#10;云锋金融</custName>

<account>************</account>

<currencyCode>AUD</currencyCode>

<currencyName>澳大利亚元</currencyName>

<balance></balance>

<availBalance>0.00</availBalance>

<acState></acState>

<bankName>CHINA MINSHENG BANKING CORP., LTD., HONG KONG

BRANCH Hong

Kong Branch</bankName>

<countryName>香港</countryName>

<verifyDate>2019-07-05</verifyDate>

</Map>

</List>

</xDataBody>

</CMBC>

3.全球跨境现金池账户信息查询(CBCashPoolAccInfo

Qry)

本部分更新日期:2021-04-02

查询境内分行账户,境外分行账户,他行账户类型的账户信息

3.1.请求(CBCashPoolAccInfoQry)

  标记

说明

长度

10 / 33

民生银行银企直联

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

(★)

 <customId>

客户编号(★)

 <isQryNextLevel> 查询标志位(★)( 0 - 不查下级账户， 1 - 查询下级

账户)

 <pageNo>

当前页数(★)

 <pageSize>

查询笔数(★)

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

3.2.响应(CBCashPoolAccInfoQry)

  标记

说明

<xDataBody>

 <trnId>

客户端的唯一标识（★）

 <allNum>

总笔数（★）

 <totalPage>

总页数（★）

 <List>

  <Map>

   <accountId>

账号 ID（★）

   <account>

账号（★）

64

12

1

4

4

长度

64

20

20

12

50

11 / 33

   <accName>

账号名称（★）

   <accUserBal>

可用余额（★）

   <accBal>

账户余额（★）

民生银行银企直联

255

16

16

   <isCmbc>

是否是民生银行标志（★） 0-他行账户 1-民生银

1

行账户

   <accountType>

账号类型（★）1-境内分行账户；2-境外分行账

1

户；3-他行账户

   <currencyName> 币种名称（★）

   <bankName>

开户行名称（★）

   <countryName>

开户行所在国家（★）

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

  </xDataBody>

3.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBCashPoolAccInfoQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

255

255

255

12 / 33

民生银行银企直联

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<isQryNextLevel>0</isQryNextLevel>

<customId>255</customId>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CBCashPoolAccInfoQry" security="none" lang="chs"

header="100" version="100" >

<responseHeader>

<status>

<code>0000</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2019-07-03 15:26:51</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<totalPage>4</totalPage>

<allNum>36</allNum>

<List>

<Map>

<accountId>572</accountId>

<account>*********</account>

<accName>新业务 08</accName>

<accUserBal>37893.13</accUserBal>

<isCmbc>1</isCmbc>

<accountType>1</accountType>

<accBal>37893.13</accBal>

13 / 33

<currencyName>美元</currencyName>

<bankName>中国民生银行</bankName>

<countryName>中国</countryName>

民生银行银企直联

</Map>

……

</List>

</xDataBody>

</CMBC>

4.全球跨境现金池汇总信息查询(CBCashPoolSumInf

oQry)

本部分更新日期:2021-04-02

按照币种，或者按照国家&币种，查询全球跨境现金池汇总信息

4.1.请求(CBCashPoolSumInformationQry)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

 <customId>

客户编号(★)

长度

64

12

 <isQryNextLevel> 查询标志位(★)( 0 - 不查下级账户， 1 - 查询下级账

1

户)

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

14 / 33

4.2.响应(CBCashPoolSumInformationQry)

民生银行银企直联

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）

64

(★)

 <currencyList>

按币种汇总

  <Map>

   <currencyCode> 币种代码(★)

   <currencyName> 币种名称(★)

   <balSum>

累计余额(★)

   <totalAccCount> 累计账户个数(★)

  <Map>

 <currencyList>

 <countryList>

按国家&币种汇总

  <Map>

   <countryCode>

开户行所在国家代码(★)

   <countryName>

开户行所在国家名称(★)

   <currencyCode> 币种代码(★)

   <currencyName> 币种名称(★)

   <balSum>

累计余额(★)

   <totalAccCount> 累计账户个数(★)

3

255

16

12

3

100

3

255

16

12

15 / 33

民生银行银企直联

  <Map>

 <countryList>

 <bankList>

按开户行&币种汇总

  <Map>

   <bankCode>

开户行代码(★)

   <countryName>

开户行名称(★)

   <currencyCode> 币种代码(★)

   <currencyName> 币种名称(★)

   <balSum>

累计余额(★)

   <totalAccCount> 累计账户个数(★)

  <Map>

 <bankList>

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

</xDataBody>

4.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBCashPoolSumInfoQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

11

255

3

255

16

12

16 / 33

民生银行银企直联

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<isQryNextLevel>0</isQryNextLevel>

<customId>255</customId>

</xDataBody>

</CMBC>

响应报文:

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CBCashPoolSumInfoQry" security="none" lang="chs"

header="100" version="100" >

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-07-03 14:54:56</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>

vvvvv555

</trnId>

<currencyList>

<Map>

<currencyCode>AUD</currencyCode>

<currencyName>澳大利亚元</currencyName>

<balSum>15033.79</balSum>

<totalAccCount>3</totalAccCount>

</Map>

……

17 / 33

民生银行银企直联

</currencyList>

<countryList>

<Map>

<countryCode>156</countryCode>

<countryName>中国</countryName>

<currencyCode>AUD</currencyCode>

<currencyName>澳大利亚元</currencyName>

<balSum>15033.79</balSum>

<totalAccCount>2</totalAccCount>

</Map>

……

</countryList>

<bankList>

<Map>

<bankCode>2100</bankCode>

<bankName>中国民生银行股份有限公司天津分行</bankName>

<currencyCode>AUD</currencyCode>

<currencyName>澳大利亚元</currencyName>

<balSum>15033.79</balSum>

<totalAccCount>2</totalAccCount>

</Map>

……

</bankList>

</xDataBody>

</CMBC>

5.全球跨境现金池客户关系树查询(CBCashPoolTreeQ

ry)

本部分更新日期:2021-04-02

全球跨境现金池客户关系树关系查询

18 / 33

5.1.请求(CBCashPoolTreeQry)

  标记

说明

<xDataBody>

民生银行银企直联

长度

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <extFields1> 备用字段（未启用）

 <extFields2> 备用字段（未启用）

</xDataBody>

5.2.响应(CBCashPoolTreeQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无

64

作用）(★)

 <totalNum>

描述当前笔数(★)

 <List>

  <Map>

   <customId>

客户编号(★)

   <treeNodeId>

客户关系树节点 ID(★)

   <treeParentNodeId> 客户关系树父节点 ID (★)

   <level>

客户在整棵树中的所处层级 (★)

   <customNameCn>

客户中文名称(★)

8

12

12

12

1

600

19 / 33

   <upCustomNameCn> 上级客户中文名称

600

民生银行银企直联

   <hasChild>

是否具有子节点(★) true-有子节点 false-

5

无子节点

  <Map>

 <List>

</xDataBody>

5.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBCashPoolTreeQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>2200032752</clientId>

<userId>2200032752001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="CBCashPoolTreeQry" security="none" lang="chs"

header="100" version="100" >

<responseHeader>

<status>

20 / 33

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-07-03 13:57:44</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<totalNum>1</totalNum>

<List>

<Map>

<customId>247</customId>

<treeNodeId>526</treeNodeId>

<treeParentNodeId>458</treeParentNodeId>

<level>2</level>

<customNameCn>云锋金融</customNameCn>

<upCustomNameCn>新业务 08</upCustomNameCn>

<hasChild>false</hasChild>

</Map>

</List>

</xDataBody>

</CMBC>

6.银企直联全球跨境现金池本行明细下载(CBDetailLoa

d)

本部分更新日期:2021-04-02

根据客户号、账号、起始笔数、查询笔数、起始日期、结束日期、借贷标记、中英文标志

下载此账号的明细;

21 / 33

民生银行银企直联
1、下载明细时起始笔数必须大于 0 且不能超过最大查询记录数，最大查询记录不能超过

10000 且不能小于 0，查询之间间隔不能大于 3 个月;

2、同一个账号不能在设定的时间内连续进行起始记录条数是 1 的查询，其他的起始条数

被认为是翻页查询，不受时间间隔限制，目前时间限制设置是 15 分钟.

6.1.请求(CBdetailLoad)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

 <acntNo>

查询账号（★）

 <accountId>

账号 ID（★）

 <dateFrom>

起始时间（★）格式为 YYYY-MM-DD

 <dateTo>

结束日期（★）格式为 YYYY-MM-DD

 <startNo>

起始笔数（★）

 <queryRows> 查询笔数（★）

 <typeCode>

借贷标记(暂不支持)

</xDataBody>

6.2.响应(CBdetailLoad)

  标记

说明

<xDataBody>

长度

64

32

12

10

10

8

8

1

22 / 33

民生银行银企直联

 <trnId>

原值返回

 <totalNum>

总条数

 <dataStream> 竖线“|”分割数据元素，以尖号“0x0A”为数据行分割符

</xDataBody>

6.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBDetailLoad">

<requestHeader>

<dtClient>2012-05-11 11:08:33</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>ceshi000</trnId>

<acntNo>****************</acntNo>

<accountId>867</accountId>

<dateFrom>2012-08-01</dateFrom>

<dateTo>2012-10-17</dateTo>

<startNo>1</startNo>

<queryRows>2</queryRows>

<typeCode>2</typeCode>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none" trnCode="CBDetailLoad"

23 / 33

民生银行银企直联

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2012-11-07 10:39:20</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>ceshi000</trnId>

<totalNum>26</totalNum>

<dataStream>交易日期|主机交易流水号|借方发生额|贷方发生额|账户余额|凭证

种类|企业自制凭证号|摘要|交易地点|对方账号|对方账号名称|对方开户行|交易时间戳

|0x0A20120814|********0067|0.00|********.00|********.70|||大额汇兑来

账|中国民生银行总行营业部|***************|北京普德金属集团有限公司|中国农业

银行股份有限公司北京市分行集中作业中心(不对外营业）

|**************.583|0x0A20120814|************|********.00|0.00|36

386.70|新版清分机支票|*************|转存保证金|中国民生银行总行营业部

||||**************.624|</dataStream>

</xDataBody>

</CMBC>

7.全球跨境现金池他行交易明细查询(CBOtherBankTr

ansDetailQry)

本部分更新日期:2024-01-05

全球跨境现金池他行交易明细查询.

24 / 33

7.1.请求(CBOtherBankTransDetailQry)

民生银行银企直联

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <accountId>

账号 ID（★）

 <account>

签约账号（★）

 <qryStartDate> 开始日期（含）格式为 YYYY-MM-DD（★）

 <qryEndDate> 截止日期（含）格式为 YYYY-MM-DD（★）

 <pageNo>

页码（★）

 <pageSize>

查询笔数（★）

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

7.2.响应(CBOtherBankTransDetailQry)

  标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <allNum>

总记录数

 <List>

  <Map>

12

50

10

10

8

8

长度

64

8

25 / 33

民生银行银企直联

10

1

255

3

20

50

50

50

50

200

   <valueDate>

起息日 YYYY-MM-DD

   <dcFlag>

借贷标志 0：借；1：贷

   <currencyName> 币种名称

   <currencyCode> 币种代码

   <amount>

金额

   <oppAccNo>

对方账户

   <busiTypeName> 业务类型

   <referenceNo>

开户行业务编号

   <accBizNo>

账户行业务编号

   <supContent>

备注

   <extFields1>

备用字段（未启用）

   <extFields2>

备用字段（未启用）

  </Map>

 </List>

</xDataBody>

7.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBOtherBankTransDetailQry">

<requestHeader>

<dtClient>2019-01-23 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

26 / 33

民生银行银企直联

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<accountId>867</accountId>

<account>708090</account>

<qryStartDate>2019-03-28</qryStartDate>

<qryEndDate>2019-06-28</qryEndDate>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="CBOtherBankTransDetailQry"

header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-07-05 13:55:52</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<allNum>1</allNum>

<List>

<Map>

<valueDate>2019-04-08</valueDate>

<dcFlag>0</dcFlag>

27 / 33

民生银行银企直联

<currencyCode>BZD</currencyCode>

<currencyName>伯利兹元</currencyName>

<amount>10000</amount>

<oppAccNo></oppAccNo>

<busiTypeName>NCHG-Charges

and Other

Expenses</busiTypeName>

<referenceNo>****************</referenceNo>

<accBizNo>**********</accBizNo>

<supContent>RTGS CHARGE</supContent>

</Map>

</List>

</xDataBody>

</CMBC>

8.全球跨境现金池本行交易明细查询(CBSelfBankTran

sDetailQry)

本部分更新日期:2021-04-02

民生银行账户明细最多支持查询的时间区间内不超过 500 条数据，如果查询时间区间内
超出 500 条数据请使用明细银企直联下载接口.

8.1.请求(CBSelfBankTransDetailQry)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

 <accountId>

账号 ID（★）

 <account>

签约账号（★）

 <qryStartDate> 开始日期（★）（含）格式为 YYYY-MM-DD

 <qryEndDate> 截止日期（★）（含）格式为 YYYY-MM-DD

12

50

10

10

28 / 33

 <currentIndex> 起始笔数（★）

 <pageSize>

查询笔数（★）

 <extFields1>

备用字段（未启用）

 <extFields2>

备用字段（未启用）

民生银行银企直联

8

8

8.2.响应(CBSelfBankTransDetailQry)

  标记

说明

长度

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志（★）

 <allNum>

总记录数（★）

 <List>

  <Map>

   <trsDt>

交易日期

   <tellerSeq>

交易流水号

   <dcFlag>

借贷标志 0：借，1：贷

   <trsAmt>

   <avaliBal>

金额

余额

   <certNo>

凭证号

   <abstractNo>

摘要

   <payeeAc>

对方账号

   <payeeNm>

对方单位名称

64

8

8

32

1

20

20

12

255

32

255

29 / 33

   <payeeOpBankNm> 对方开户行

   <timeTamp>

交易时间

   <extFields1>

备用字段 1（未启用）

   <extFields2>

备用字段 2（未启用）

民生银行银企直联

255

8

  </Map>

 </List>

</xDataBody>

8.3.请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="CBSelfBankTransDetailQry">

<requestHeader>

<dtClient>2019-01-23 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<accountId>867</accountId>

<account>*********</account>

<qryStartDate>2019-04-02</qryStartDate>

<qryEndDate>2019-07-02</qryEndDate>

<currentIndex>1</currentIndex>

<pageSize>20</pageSize>

</xDataBody>

</CMBC>

30 / 33

返回报文

民生银行银企直联

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="CBSelfBankTransDetailQry"

header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-07-05 13:53:17</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>1146assaxd7bc43v2</trnId>

<allNum>1</allNum>

<List>

<Map>

<trsDt>********</trsDt>

<tellerSeq></tellerSeq>

<dcFlag>1</dcFlag>

<trsAmt>15000.00</trsAmt>

<avaliBal>15000.00</avaliBal>

<certNo></certNo>

<abstractNo>存款</abstractNo>

<payeeAc></payeeAc>

<payeeNm></payeeNm>

<payeeOpBankNm></payeeOpBankNm>

<timeTamp>145738</timeTamp>

</Map>

</List>

</xDataBody>

</CMBC>

31 / 33

民生银行银企直联

32 / 33

