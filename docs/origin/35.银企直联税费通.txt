银企直联接口文档

（税费通）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2024-06-18 定义接口文档

V2.0.0 2024-08-15 添加银企直联文件上传、下载接口。

更新批量缴税信息上传核验

(B2eCheckbatTaxPayInfo)字段说明

1 / 39

目录

民生银行银企直联

目录 .............................................................................................2

1. 批量缴税信息上传核验(B2ECHECKBATTAXPAYINFO) ........................... 4

1.1. 请求(B2ECHECKBATTAXPAYINFO) ................................................. 4

1.2. 响应(B2ECHECKBATTAXPAYINFO) ................................................. 5

1.3. 报文示例 ................................................................................. 5

2. 批量缴税核验结果查询(B2EQRYBATTAXPAYCHECKRESULT) ................. 7

2.1. 请求(B2EQRYBATTAXPAYCHECKRESULT) ........................................ 7

2.2. 响应(B2EQRYBATTAXPAYCHECKRESULT) ........................................ 8

2.3. 例子 ...................................................................................... 9

3. 批量缴税申请提交(B2ELEVYBATTAXPAY) .........................................12

3.1. 请求(B2ELEVYBATTAXPAY) ....................................................... 13

3.2. 响应(B2ELEVYBATTAXPAY) ....................................................... 13

3.3. 例子 .................................................................................... 14

4. 批量缴税交易结果查询(B2EQRYBATTAXPAYRESULT) ......................... 16

4.1. 请求(B2EQRYBATTAXPAYRESULT) .............................................. 16

4.2. 响应(B2EQRYBATTAXPAYRESULT) .............................................. 16

4.3. 例子 .................................................................................... 18

5. 批量缴税交易结果查询(B2EQRYBATTAXPAYINFO) ............................. 20

5.1. 请求(B2EQRYBATTAXPAYINFO) .................................................. 20

5.2. 响应(B2EQRYBATTAXPAYINFO) .................................................. 21

5.3. 例子 .................................................................................... 23

6. 缴税交易明细查询(B2EQRYTAXPAYERINFOLIST) ............................... 26

6.1. 请求(B2EQRYTAXPAYERINFOLIST) .............................................. 27

2 / 39

民生银行银企直联
6.2. 响应(B2EQRYTAXPAYERINFOLIST) .............................................. 27

6.3. 例子 .................................................................................... 29

7. 获取文件 TOKEN(B2EGETTOKEN) .................................................. 30

7.1. 请求(B2EGETTOKEN) ............................................................... 30

7.2. 响应(B2EGETTOKEN) ............................................................... 30

7.3. 报文示例 ............................................................................... 31

8. 文件上传(B2EUPLOAD) ................................................................ 32

8.1. 参数定义 ............................................................................... 34

8.2. 报文示例 ............................................................................... 34

9. 文件下载(B2EDOWNLOAD) ........................................................... 36

9.1. 参数说明： ............................................................................ 37

9.2. 报文示例： ............................................................................ 37

3 / 39

1.批量缴税信息上传核验(B2eCheckbatTaxPayInfo)

民生银行银企直联

本部分更新日期:2024-06-18

1.1.请求(B2eCheckbatTaxPayInfo)

  标记

说明

长度 描述

<xDataBody>

标记为★的为必填元素

 <insId>

客户端产生的交易唯一标志（★）

32

必输，原值返回，数

字字母组成；可做为

客户端自己的交易标

识

 <trnid>

交易流水号

32

非必输

 <fileType>

录入方式（★）

1

1.录入批量缴费信息

2.影像文件 PDF

 <payAcct>

付款账户（★）

32

 <fileContent> 批次明细数据或影像 ID（★）

</xDataBody>

<fileContent>录入方式说明：

1：fileContent 送批次明细的数据

竖线“|”分割数据元素，以尖号“^”为数据行分割符，格式如下（无需输表头）：

银行端查询缴税凭证序号|纳税人识别号|税务机关代码|应缴金额|客户附言^

2：fileContent 送影像 ID，获取影像 ID（imageId）步骤：

1）调用 B2eGetToken 获取 tokenId（需要配置获取 token 权限），

2）调用 8.文件上传（b2eUpload）上传接口获取 imageId）

4 / 39

1.2.响应(B2eCheckbatTaxPayInfo)

民生银行银企直联

  标记

说明

长度

描述

<xDataBody>

 <batchId>

批次号 32

必输，原值返回，数字字母组成；可做为

客户端自己的交易标识

 <batchStatus>

批次处

2

此为技术请求结果，非业务处理结果。业

理状态

务处理结果请至批次查询接口获取。

0：批次已登记

E：批次处理失败

 <batchResult>

批量校

验结果

如果批次失败，会返回失败原因

</xDataBody>

1.3.报文示例

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC

header="100" version="100" security="none" lang="chs" trnCode="B2eChe

ckbatTaxPayInfo">

<requestHeader>

<dtClient>2023-11-21 11:00:49</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

5 / 39

民生银行银企直联

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023120614354720</trnId>

<insId>CMBCINS2023112018134910</insId>

<fileType>1</fileType>

<payAcct>*********</payAcct>

<fileContent>4220222018411131008|440201198411138608|*********

00|1566.22|11 月 21 号备注^</fileContent>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs" trnCo

de="B2eCheckbatTaxPayInfo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-21 11:00:59</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<insId>CMBCINS2023112018134910</insId>

<batchId>31300202311210734410565554B35001</batchId>

<batchStatus>0</batchStatus>

6 / 39

</xDataBody>

</CMBC>

民生银行银企直联

2.批量缴税核验结果查询(B2eQryBatTaxPayCheck

Result)

本部分更新日期:2022-11-16

2.1.请求(B2eQryBatTaxPayCheckResult)

  标记

说明

长度

描述

<xDataBody>

 <insId>

流水号

32

批次号未知的情况

下，输入批量缴税

信息导入核验时的

insId。唯一流水号

和批次号二选一必

输

 <batchId>

批次号

32

唯一流水号和批次

 <pageNo>

起始页码（★）

 <pageSize>

返回条数（★）

</xDataBody>

号二选一必输

单次最多查询 100

笔

7 / 39

2.2.响应(B2eQryBatTaxPayCheckResult)

民生银行银企直联

  标记

说明

长度

描述

<xDataBody>

 <batchId>

批次号

 <channelDate>

上传时间

14

格式：

 <batChkStatus>

批次校验状态

1

 <payAcct>

付款账户

32

 <custName>

缴税账户名称

600

 <batChkResult>

批次失败原因

200

 <succChkNum>

校验成功总笔数 int

 <succChkAmt>

校验成功总金额 18,2

 <failChkNum>

校验失败总笔数 int

 <failChkAmt>

校验失败总金额 18,2

 <total>

批次明细总数量 int

 <taxPayInfoList>

批次信息列表

List<Map>

<Map>

   <batDetailNo> 明细序号

32

yyyyMMddHHmmSS

0-批次已登记

1-批次校验中

2-批次校验完成

E-批次失败

8 / 39

  标记

说明

长度

描述

民生银行银企直联

   <taxPayCode> 纳税人识别号

   <taxOrgCode>

税务机关代码

20

12

   <outerLevyNo> 银行端查询缴税

20

凭证序号

   <traAmt>

应缴金额

18,2

   <payRemark>

缴税附言

   <checkStatus> 校验状态

60

2

0:未校验

S:校验成功

E:校验失败

   <checkResult> 校验结果

200

核验失败，展示失败原因

  </Map>

 </taxPayInfoList>

</xDataBody>

2.3.例子

请求报文

根据批次号：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayCheckResult">

<requestHeader>

<dtClient>2023-11-21 11:05:49</dtClient>

<userPswd>s5wg65</userPswd>

9 / 39

民生银行银企直联

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023112018134911</trnId>

<batchId>31300202311210734410565554B35001</batchId>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

根据 insId 查询：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayCheckResult">

<requestHeader>

<dtClient>2023-11-20 18:13:49</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

10 / 39

民生银行银企直联

<xDataBody>

<trnId>CMBCTRN2023112018134900</trnId>

<insId>CMBCINS2023112018134910</insId>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayCheckResult">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-21 11:03:22</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<batChkStatus>2</batChkStatus>

<succChkNum>1</succChkNum>

11 / 39

<succChkAmt>1566.22</succChkAmt>

民生银行银企直联

<taxPayInfoList>

<item>

<batDetailNo>31300202311210734410565554B35002</batDetailNo>

<taxPayCode>440201198411138608</taxPayCode>

<taxOrgCode>*********00</taxOrgCode>

<outerLevyNo>4220222018411131008</outerLevyNo>

<traAmt>1566.22</traAmt>

<payRemark>11 月 21 号备注</payRemark>

<checkStatus>S</checkStatus>

</item>

</taxPayInfoList>

<batchId>31300202311210734410565554B35001</batchId>

<custName>毕象语企业公司</custName>

<failChkAmt>0.0</failChkAmt>

<total>1</total>

<trnId>CMBCTRN2023112018134900</trnId>

<insId>CMBCINS2023112018134910</insId>

<payAcct>*********</payAcct>

<failChkNum>0</failChkNum>

<channelDate>20231121110056</channelDate>

</xDataBody>

</CMBC>

3.批量缴税申请提交(B2eLevyBatTaxPay)

本部分更新日期:2024-06-18

12 / 39

3.1.请求(B2eLevyBatTaxPay)

民生银行银企直联

  标记

说明

长度

描述

32

15,2

3

<xDataBody>

 <insId>

流水号（★）

<trnId>

交易识别号

 <batchId>

批次号（★）

 <amount>

总金额（★）

<TotalNum>

总笔数（★）

<payerAcct>

付款账号（★）

<payerName> 付款账户名称（★）

<AuthFlag>

是否需要网银审批（★）

</xDataBody>

3.2.响应(B2eLevyBatTaxPay)

Y：需要审批

N：无需审批

  标记

说明

长度 描述

<xDataBody>

 <svrId>

渠道流水号

32

非审批模式下返回此参数

 <CertNo>

凭证号

审批模式返回的凭证号，网银任

务中心可通过此凭证号查看详情

 <message>

返回信息

13 / 39

民生银行银企直联

</xDataBody>

3.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eLevyBatTaxPay">

<requestHeader>

<dtClient>2023-10-07 17:51:36</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>ent</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202310071523339</trnId>

<insId>CMBCINS2023100715233310</insId>

<payerAcct>*********</payerAcct>

<payerName>毕象语企业公司</payerName>

<batchId>31300202309260016040014554B60004</batchId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

14 / 39

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eLevyBatTaxPay">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-10-07 17:51:41</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<CertNo>9800000000853</CertNo>

<message>您已成功制单，等待下一级审批！</message>

</xDataBody>

</CMBC>

15 / 39

4.批量缴税交易结果查询(B2eQryBatTaxPayResult)

民生银行银企直联

本部分更新日期:2024-06-18

4.1.请求(B2eQryBatTaxPayResult)

  标记

说明

长度

描述

<xDataBody>

 <insId>

唯一流水号

32

批次号未知的情况下，输入批

量缴税申请提交的 insId。唯

一流水号和批次号二选一必输

<batchId>

批次号

32

唯一流水号和批次号二选一必

输

<pageNo>

<pageSize>

</xDataBody>

起始页码

（★）

返回条数

（★）

4.2.响应(B2eQryBatTaxPayResult)

  标记

说明

长度

描述

<xDataBody>

 <batchId>

批次号

32

16 / 39

  标记

说明

长度

描述

民生银行银企直联

<payAcct>

缴税账号

32

<succPayNum>

成功笔数

<succPayAmt>

成功金额(元)

18,2

<failPayNum>

失败笔数

<failPayAmt>

失败金额(元)

18,2

<transDate>

制单日期

<payDate>

交易日期

8

14

<batPayStatus>

批次处理状态

1

格式：yyyyMMddHHmmSS

0-待申报缴款

1-申报缴款中

2-申报缴款完成

E-批次失败

X-待网银审批

<batPayResult>

批次支付结果

200

处理失败，展示失败原因

<total>

批次明细总数量

int

<taxPayInfoList> 批次信息列表

<Map>

<batDetailNo> 批次明细序号

<taxPayCode> 纳税人识别号

32

20

<taxPayName> 纳税人名称

600

<taxOrgCode> 税务机关代码

12

17 / 39

  标记

说明

长度

描述

民生银行银企直联

<outerLevyNo> 银行端查询缴税凭

20

证序号

<traAmt>

应缴金额

18,2

<payRemark>

客户附言

<transStatus>

交易状态

60

2

0、WP-处理中

LF、 FT、 FA-交易失败

SS-交易成功

<transResult>

交易结果

200

缴税失败，展示失败原因

</Map>

</taxPayInfoList>

</xDataBody>

4.3.例子

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayResult">

<requestHeader>

<dtClient>2023-11-21 11:21:52</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

18 / 39

<xDataBody>

<trnId>CMBCTRN2023112018134912</trnId>

<batchId>31300202311210734410077554B25007</batchId>

民生银行银企直联

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayResult">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-21 11:21:55</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<taxPayInfoList>

<item>

<batDetailNo>31300202311210734410077554B25008</batDetailNo>

<taxPayCode>440201198411138609</taxPayCode>

<taxPayName>杨百建企业</taxPayName>

<taxOrgCode>*********00</taxOrgCode>

<outerLevyNo>4220222018411131009</outerLevyNo>

<traAmt>1566.22</traAmt>

<payRemark>11 月 14 号备注</payRemark>

<transStatus>SS</transStatus>

</item>

</taxPayInfoList>

<batchId>31300202311210734410077554B25007</batchId>

19 / 39

民生银行银企直联

<batPayStatus>2</batPayStatus>

<total>1</total>

<payAcct>*********</payAcct>

<trnId>CMBCTRN2023112018134912</trnId>

<succPayNum>1</succPayNum>

<succPayAmt>1566.22</succPayAmt>

<transDate>20231121</transDate>

<failPayNum>0</failPayNum>

<failPayAmt>0.0</failPayAmt>

<payDate>20231121105247</payDate>

</xDataBody>

</CMBC>

5.批量缴税交易结果查询(B2eQryBatTaxPayInfo)

本部分更新日期:2024-06-18

5.1.请求(B2eQryBatTaxPayInfo)

  标记

说明

长度 描述

<xDataBody>

 <batchId>

批次号

32

与开始日期/结束日期二选

<startDate>

开始日期

<endDate>

结束日期

8

8

一必输

与批次号二选一必输

yyyyMMdd 与结束日期

间跨度最长三个月

与批次号二选一必输

yyyyMMdd 与结束日期

间跨度最长三个月

20 / 39

  标记

说明

长度 描述

<batchStep>

批次当前步骤（★）

2

0：校验（已导入未提

民生银行银企直联

交）

1：申报缴款（已提交支

付）

<batStatus>

批次状态

1

批次当前步骤为 0 时，校

验状态：

1-批次校验中

2-批次校验完成

E-批次校验失败

默认查全部

批次当前步骤为 1 时，缴

款状态：

1-申报缴款中

2-申报缴款完成

E-批次扣款失败

3-查询全部状态

<pageNo>

起始页码（★）

<pageSize>

返回条数（★）

</xDataBody>

5.2.响应(B2eQryBatTaxPayInfo)

  标记

<xDataBody>

说明

长度 描述

21 / 39

  标记

 <total>

说明

长度 描述

总数量

民生银行银企直联

<batchResultList>

批次信息列表

<Map>

<batchId>

批次编号

<fileType>

录入文件类型

32

1

1：录入批量缴税信息

2：影像文件 PDF

<fileName>

文件名称

120

<custName>

付款账户名称

600 取导入核验时上送的付

款账户名称

<payAcct>

付款账户

32

取导入核验是上送的账

户信息

<channelDate>

上传日期(核验日

14

yyyyMMddHHmmSS

期)

<payDate>

交易日期(缴款日

14

yyyyMMddHHmmSS

期)

<transDate>

制单日期

8

yyyyMMdd

<initNum>

初始化笔数

文件导入解析时初始化

金额

<initAmt>

初始化金额

18,2

<batChkStatus>

批次校验状态

1

(批次状态为 0 时必返)

1-批次校验中

2-批次校验完成

22 / 39

  标记

说明

长度 描述

民生银行银企直联

E-批次失败

<batChkResult>

校验失败

120

<batPayStatus>

批次缴款状态

1

(批次状态为 1 时必返)

0-待申报缴款

1-申报缴款中

2-申报缴款完成

E-批次失败

<batPayResult>

缴费失败原因

120

<Map>

</batchResultList>

</xDataBody>

5.3.例子

核验：

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayInfo">

<requestHeader>

<dtClient>2023-11-21 11:29:21</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

23 / 39

<xDataBody>

<trnId>CMBCTRN2023112018134915</trnId>

<batchId>31300202311210734410565554B35001</batchId>

民生银行银企直联

<batchStep>0</batchStep>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayInfo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-21 11:29:24</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>1</total>

<trnId>CMBCTRN2023112018134915</trnId>

<batchResultList>

<item>

<batchId>31300202311210734410565554B35001</batchId>

<fileType>1</fileType>

<fileName>**********1700535652951.xlsx</fileName>

<custName>毕象语企业公司</custName>

<payAcct>*********</payAcct>

<channelDate>20231121110056</channelDate>

<payDate>20231121110154</payDate>

<initNum>1</initNum>

<initAmt>1566.22</initAmt>

24 / 39

<batChkStatus>2</batChkStatus>

<batPayStatus>0</batPayStatus>

民生银行银企直联

</item>

</batchResultList>

</xDataBody>

</CMBC>

缴款：

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayInfo">

<requestHeader>

<dtClient>2023-11-21 11:27:13</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023112018134914</trnId>

<batchId>31300202311210734410077554B25007</batchId>

<batchStep>1</batchStep>

<batStatus>3</batStatus>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryBatTaxPayInfo">

<responseHeader>

<status>

<code>0</code>

25 / 39

民生银行银企直联

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-21 11:27:14</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>1</total>

<trnId>CMBCTRN2023112018134914</trnId>

<batchResultList>

<item>

<batchId>31300202311210734410077554B25007</batchId>

<fileType>1</fileType>

<fileName>**********1700532110532.xlsx</fileName>

<custName>毕象语企业公司</custName>

<payAcct>*********</payAcct>

<channelDate>20231121100150</channelDate>

<transDate>20231121</transDate>

<payDate>20231121105247</payDate>

<initNum>1</initNum>

<initAmt>1566.22</initAmt>

<batChkStatus>2</batChkStatus>

<batPayStatus>2</batPayStatus>

</item>

</batchResultList>

</xDataBody>

</CMBC>

6.缴税交易明细查询(B2eQryTaxPayerInfoList)

本部分更新日期:2024-06-18

26 / 39

6.1.请求(B2eQryTaxPayerInfoList)

民生银行银企直联

  标记

说明

长度 描述

<xDataBody>

 <draweeAccNo>

付款账号（★）

32

必输，客户自己的账号，

网银校验查询权限

<transType>

交易类型（★）

8

ALL:全部

BANK_PAY:主动缴款

THD_DEDU:单笔扣款

BAT_DEDU:批量扣款

<processFlag>

交易状态（★）

2

ALL:全部

SS:成功

FA:失败

8

8

yyyyMMdd

yyyyMMdd

<startDate>

起始日期（★）

<endDate>

截止日期（★）

<pageNo>

起始页码（★）

<pageSize>

返回条数（★）

</xDataBody>

6.2.响应(B2eQryTaxPayerInfoList)

  标记

说明

长度 描述

<xDataBody>

27 / 39

民生银行银企直联

  标记

说明

长度 描述

 <total>

总数量

int

<paymentInfoList>

缴税明细列

表

<Map>

<channelDate>

交易日期

<channelTime>

交易时间

8

6

格式：yyyyMMdd

格式：HHmmSS

<channelSerialNo> 交易流水号 32

<taxVouNo>

税票号码

18

<traAmt>

交易金额

18,2

<transType>

交易类型

8

BANK_PAY:主动缴款

<processFlag>

交易状态

2

THD_DEDU:单笔扣款

BAT_DEDU:批量扣款

SS:交易成功

FA:交易失败

<returnInfo>

失败原因

60

<taxTypeList>

税种明细列

256 明细规则：

表

税种名称|税种金额^

</xDataBody>

taxTypeName|taxTypeAmt^

示例：

社保|20.01^医保|22.01^

28 / 39

民生银行银企直联

6.3.例子

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryTaxPayerInfoList">

<requestHeader>

<dtClient>2021-10-08 20:05:29</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<draweeAccNo>*********</draweeAccNo>

<transType>ALL</transType>

<processFlag>ALL</processFlag>

<startDate>********</startDate>

<endDate>********</endDate>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

返回报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryTaxPayerInfoList">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

29 / 39

<dtServer>2023-11-13 17:11:31</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<totalCounts>0</totalCounts>

</xDataBody>

</CMBC>

7.获取文件 token(B2eGetToken)

本部分更新日期:2024-06-25

接口说明：获取文件 token

7.1.请求(B2eGetToken)

标记

说明

<xDataBody>

是否

长度

必输

<trnId>

客户技术请求流水号，同一客户请勿重复

Y

64

</xDataBody>

7.2.响应(B2eGetToken)

标记

说明

<xDataBody>

<trnId>

<tokenId>

</xDataBody>

客户端交易的唯一标志

文件上传 token

是否

长度

必输

Y

N

64

64

30 / 39

民生银行银企直联

7.3.报文示例

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eGetToken">

<requestHeader>

<dtClient>2021-10-08 20:05:29</dtClient>

<userPswd>s5wg65</userPswd>

<clientId>**********</clientId>

<userId>**********001</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202212261752324</trnId>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eGetToken">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-03 09:45:39</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>CMBCTRN202212261752324</trnId>

31 / 39

<tokenId>TF947947BA884H9Y4eaM8ab97d764fdR27R07EFddfS9451PQ

民生银行银企直联

7EAbd46R62bVD6D</tokenId>

</xDataBody>

</CMBC>

8.文件上传(b2eUpload)

本部分更新日期:2024-06-25

一．接入方式：

使用文件上传下载功能时，调用地址是：

http://ip:port/custom/b2e/接口?sequence=${流水号}$。例如：

二．环境信息：

环境

域名

版本环境

obpmapp.obp.ver.cmbc.cn:19011/custom/b2e/*

生产环境

http://obpgwin.obp.cmbc.com.cn:31179/custom/b2e/*

三．接口说明：

1. 上传贸易合同附件

2. 该接口支持单个以及多个文件的上传。

需要注意的是文件大小的限制问题：目前是写死的 50M，如果文件超过 50M，

APACHE 会正常返回文件超限。

3. 支持的文件格式：

FILE_HEADER_MAP.put("jpg", "FFD8FF");

FILE_HEADER_MAP.put("png", "89504E47");

FILE_HEADER_MAP.put("gif", "47494638");

FILE_HEADER_MAP.put("tif", "49492A00");

FILE_HEADER_MAP.put("bmp", "424D");

FILE_HEADER_MAP.put("dwg", "41433130");

FILE_HEADER_MAP.put("psd", "38425053");

32 / 39

民生银行银企直联

FILE_HEADER_MAP.put("rtf", "7B5C727466");

FILE_HEADER_MAP.put("xml", "3C3F786D6C");

FILE_HEADER_MAP.put("html", "68746D6C3E");

FILE_HEADER_MAP.put("eml", "44656C69766572792D646174653A");

FILE_HEADER_MAP.put("dbx", "CFAD12FEC5FD746F");

FILE_HEADER_MAP.put("pst", "2142444E");

FILE_HEADER_MAP.put("xls", "D0CF11E0");

FILE_HEADER_MAP.put("doc", "D0CF11E0");

FILE_HEADER_MAP.put("ppt", "D0CF11E0");

FILE_HEADER_MAP.put("xlsx", "504B0304");

FILE_HEADER_MAP.put("docx", "504B0304");

FILE_HEADER_MAP.put("mdb", "5374616E64617264204A");

FILE_HEADER_MAP.put("wpd", "FF575043");

FILE_HEADER_MAP.put("pdf", "255044462D312E");

FILE_HEADER_MAP.put("qdf", "AC9EBD8F");

FILE_HEADER_MAP.put("pwl", "E3828596");

FILE_HEADER_MAP.put("zip", "504B0304");

FILE_HEADER_MAP.put("rar", "52617221");

FILE_HEADER_MAP.put("wav", "57415645");

FILE_HEADER_MAP.put("avi", "41564920");

FILE_HEADER_MAP.put("ram", "2E7261FD");

FILE_HEADER_MAP.put("rm", "2E524D46");

FILE_HEADER_MAP.put("mpg", "000001BA");

FILE_HEADER_MAP.put("mov", "6D6F6F76");

FILE_HEADER_MAP.put("asf", "3026B2758E66CF11");

FILE_HEADER_MAP.put("mid", "4D546864");

FILE_HEADER_MAP.put("mp4", "00000020667479706d70");

FILE_HEADER_MAP.put("mlog", "0100000060");

FILE_HEADER_MAP.put("jpeg", "FFD8FF");

FILE_HEADER_MAP.put("7z", "377ABCAF271C");

33 / 39

8.1.参数定义

民生银行银企直联

参数名

参数类型

备注

出入

参数

入参

sceneId

String

场景标识(pub 分片 rule 表配

置 )，固定值:BatTaxScene

files

File Array 文件流数组

fileToken

String

（64）

必选，推荐每次上传文件前都获取

新 Token

是否

必输

Y

Y

N

isUnzip

String

如果需解压上传，则传 Y。只允许

N

（2）

单压缩文件（支持的格式：jpg,

jpeg, png, pdf），且文件中不包

括文件夹，压缩文件中单文件大小

不超过 10M

出参

code

String

返回码

msg

String

返回内容

response

Object

响应体，内容可参考 8.2 报文示例

response.returnC

Object

响应体头

ode

response.files

Object

Array

文件信息（包含影像

id:imageId）

resposne.uuid

String

唯一主键

8.2.报文示例

请求参数

请求 Value

sceneId

testScene

fileToken

FBHFUCCBNS788S9G4EASUAB97D764FDW27907HKDDFH9S51NP7

34 / 39

民生银行银企直联

N9BDM6B62B6PGN

files

1.jpg

"returnCode":{

"code":"AAAAAAA",

"message":"交易成功.",

"type":"S"},

"files":[{

"realName":"2.jpg",

"imageId":"20240117150459039230089001703540016935500458187161"}

]

请求示例：

响应示例：

35 / 39

民生银行银企直联

9.文件下载(b2eDownload)

本部分更新日期:2024-06-25

1.接口说明：下载协议附件

2.单个文件下载，下载之后会弹出“另存为”的选择框

3.常见问题：

1).上传时文件安全校验不通过

请检查文件后缀和文件真实的格式是否一致，比如说把一张名为"test.jpg"的文件名

修改成"test.png"，会影响系统对文件格式的校验，可能导致异常。此外，还有一些调用

方在上传的时候，文件后缀缺失，也会报此异常。报此错误时，请仔细检查入参。

2).错误码对应表

code

message

IGWUPF002

上传文件安全检查不通过

IGWUPF003

未找到有效文件

IGWUPF005

上传文件失败

IGWUPF006

读取文件失败

IGWUPF007

压缩文件中单个文件过大

IGWUPF008

压缩文件中存在目录

IGWUPF009

您还未登录或无权限

IGWUPF0010

从影响平台下载文件失败

IGWUPF0011

记录文件流水失败

IGWUPF0012

当前系统繁忙，请稍后再试

IGWUPF0013

不支持的文件类型

IGWUPF0014

文件大小超过允许的最大值

36 / 39

9.1.参数说明：

民生银行银企直联

出入参数

参数名

备注

参数

类型

入参

imageId

String 影像 ID(上传文件时返回的

imageId)

fileToken

String 必填(文件 token)

推荐每次下载文件前都获取新

Token

是否必输

Y

Y

sceneId

场景编码(文件上传上送的场景

Y

编码以及融资文件查询返回的场

景编码一一对应)

出参

弹出文件下载框

9.2.报文示例：

请求示例：

请求参数

请求参数 value

imageId

20240117150459039230089001703540016935500458187161

fileToken

P9ADCDX9H6CFQ7AHD64QP58CC7CDBB8T357893H3DDU149939D7UBB64E143J8MF

sceneId

B2eDraftProto

37 / 39

民生银行银企直联

返回示例：

38 / 39

