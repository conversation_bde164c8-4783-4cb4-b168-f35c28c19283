银企直联接口文档

（账户管理-回单）

邮箱：<EMAIL>

版本

日期

说明

编写者 审核者

文档修改记录

民生银行银企直联

V1.0.0 2021-03-18

定义接口文档

V2.0.0 2024-09-09

回单下载支持 OFD 回单格式，

回单查询支持贴现回单查询。

V2.1.0 2024-11-01

更新回单查询与下载字段及说明

V3.0.0 2025-03-04

回单查询新增新增 redFlag、

redFlagShow 字段。

V3.1.0 2025-04-10

增加回单查询、下载接口白名单逻

辑字段说明。

1 / 15

文档说明：（必读）

1. 请 求 报 文 头 需 上 送 客 户 号 （ clientId ） 、 操 作 员

（userId）、银企直联密码（userPswd）（开通银企

直联后邮件或短信下发的银企交易密码，非 Ukey 密

码，如有疑问请联系客户经理）。其他 XML 报文说明

请参照接口文档：01.银企接口文档公共说明

2. XML 格式字段：接口字段名称。

3. 字段说明：字段中文释义

4. 是否必输：是否为必输字段：Y（是） / N（不是）

5. 长度：

汉字长度：使用字符数（例如“最多小于等于 20 个汉

字”，即：“20”）来限制。

数字长度：使用位数描述整数部分和小数部分的长度限

制（例如“最多 10 位数字，其中 2 位小

数”）即：10,2。

目录

目录 .............................................................................................0

文档说明：（必读） ............................................................................. 0

1. 客户回单查询(ELECTNOTELISTQRY) ................................................. 1

1.1. 请求(ELECTNOTELISTQRY) ........................................................... 1

1.2. 响应(ELECTNOTELISTQRY) ........................................................... 3

1.3. 报文示例 ................................................................................. 4

1.3.1. 请求报文 .............................................................................................. 4

1.3.2. 响应报文 .............................................................................................. 5

2. 客户账单回单下载(B2EELECTNOTEDOWNLOADNEW) .......................... 6

2.1. 请求(B2EELECTNOTEDOWNLOADNEW) ........................................... 7

2.2. 响应(B2EELECTNOTEDOWNLOADNEW) ........................................... 9

2.3. 报文示例 ................................................................................. 9

2.3.1. 请求报文： ........................................................................................... 9

2.3.2. 响应报文： .........................................................................................10

民生银行银企直联

1. 客户回单查询(ElectnoteListQry)

本部分更新日期:2025-03-11

说明：1、支持贴现回单查询（请联系客户经理添加贴现白名单）

2、支持 OFD 文件格式回单查询（请联系客户经理添加 OFD 白名单）

3、T0 回单、贴现回单暂不支持 OFD 格式查询下载

通过输入回单相关要素来查询客户账号下的回单。

注意：

1. 回单查询返回的文件名称，回单下载时原值返回即可，不代表回单文件就是 xml 格式

的，回单下载返回的是回单 PDF 数据流；

2. 回单查询后需尽快进行回单下载，否则报错“无记录”，目前生产时间间隔为 1 小时，

回单下载时，回单内部编号和文件名称需最新查询的结果；

3.

svrId 流水号 手续费回单的流水号与原转账交易的流水号相同，结息回单的流水号为

空。

4.

recseq 用于和账户明细查询中的记账流水号匹配使用。

5. 本接口支持当日（T0）与历史（T-n）回单。

1.1. 请求(ElectnoteListQry)

  标记

说明

<xDataBody>

 <insId>

流水号

 <saAcctNo>

账号

 <qryStartDate> 查询起始日期（形如：yyyy-MM-dd）

 <qryEndDate> 查询截止日期（形如：yyyy-MM-dd）

是否

长度

必输

64

32

Y

Y

Y

Y

1 / 15

  标记

说明

 <printState>

打印状态

0—全部，

1—已打印，

2 未打印

不输默认为 2

 <minMoney>

发生额范围小(15,2)

 <maxMoney>

发生额范围大(15,2)

 <loanFlag>

借贷标识:

0-借

1-贷

非必输，不输入默认全部

 <pageNo>

当前页码

 <pageSize>

查询笔数

 <fileStyle>

文件类型：

0 表示 pdf,

1 表示 ofd

 <receiptType> 回单类型

0-正常回单，

1-贴现回单，贴现回单仅有 pdf 类型文件

<redFlag>

冲正表示

Y-展示冲正 ，

N-不展示冲正

不输默认为 Y

民生银行银企直联

是否

长度

必输

N

N

N

N

Y

Y

N

N

N

2 / 15

民生银行银企直联

是否

长度

必输

N

  标记

说明

<redFlagShow> 交易区分：

A-金额正负区分

B-借贷区分

默认 B 借贷区分

</xDataBody>

1.2. 响应(ElectnoteListQry)

  标记

说明

是否

长度

必返

<xDataBody>

客户端产生的交易唯一标志

<insId>

64

 <Count>

总笔数

 <List>

  <Map>

   <innerNo>

回单内部编号

   <trsDate>

交易日期

   <billName>

回单名称

   <acNo >

   <currency>

   <loanFlag>

账号

币种

借贷标识:

0-借 、1-贷

3 / 15

民生银行银企直联

是否

长度

必返

  标记

说明

   <trsAmount>

交易金额

   <printCount>

打印次数

   <fileName>

文件名称

   <svrId>

交易流水号（流水号+交易金额 可与交

易明细一一对应）

   <recseq>

记账流水号(返回值唯一且非空)

   <opAcntNo>

对方账号

   <opAcntName> 对方账户名称

  </Map>

 </List>

</xDataBody>

1.3. 报文示例

1.3.1. 请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="ElectnoteListQry">

<requestHeader>

<dtClient>2023-05-26 17:20:46</dtClient>

<clientId>2200003220</clientId>

<userId>2200003220001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

4 / 15

民生银行银企直联

</requestHeader>

<xDataBody>

<insId>CMBCINS2023050413444019</insId>

<saAcctNo>*********</saAcctNo>

<qryStartDate>2024-11-14</qryStartDate>

<qryEndDate>2024-11-14</qryEndDate>

<printState>0</printState>

<loanFlag></loanFlag>

<minMoney>0</minMoney>

<maxMoney>10000</maxMoney>

<pageNo>1</pageNo>

<pageSize>20</pageSize>

<fileStyle>1</fileStyle>

<receiptType>0</receiptType>

<extent1></extent1>

<extent2></extent2>

<extent3></extent3>

</xDataBody>

</CMBC>

1.3.2. 响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="ElectnoteListQry" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2024-11-15 16:47:51</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<Count>1</Count>

5 / 15

民生银行银企直联

<List>

<Map>

<innerNo>dDFvZmQyMDI0LTExLTE0XzM1MTY2ODQxMTU3</innerNo>

<trsDate>2024-11-14</trsDate>

<billName>电子银行业务回单（收款）</billName>

<acNo>*********</acNo>

<currency>人民币</currency>

<loanFlag>1</loanFlag>

<trsAmount>5.55</trsAmount>

<printCount>0</printCount>

<fileName>t1ofd2024-11-14_35166841157</fileName>

<svrId>31351202411140300144386</svrId>

<recseq>00010066000000000000000099959759</recseq>

<opAcntNo>*********</opAcntNo>

<opAcntName>版本测试公用账户 01 成员账户</opAcntName>

<insId></insId>

<extent4></extent4>

<extent5></extent5>

<extent6></extent6>

</Map>

</List>

</xDataBody>

</CMBC>

2. 客户账单回单下载(b2eElectNoteDownLoadNew)

本部分更新日期:2025-03-11

说明：

1.现已支持 OFD 回单，回单编号 innerNo 需通过 ElectnoteListQry 获取。

2.现已支持贴现回单下载。

3.至多同时下载三笔回单。

4.通过输入回单相关要素来下载客户账号下的回单，支持 T 和 T-n（n>0）日的回单，

单次下载回单支持 1~3 笔。

6 / 15

民生银行银企直联
5.多个回单下载时，回单编号以“|”隔开，且这些回单编号必须是回单分页查询时同一页

返回的，否则回单名称 FileName 不一样，导致整个批次下载失败；

常见报错说明：

若报错“账号 XXX 下回单编号 XXX 不存在”：

1. 请检查回单是否为此账号下的回单，

2.回单下载时与第一次查询此回单时间间隔是否超过规定时间（目前正式环境是 2 小

时），若是超过 2 小时需要重新查询回单获取最新回单编号在进行下载。

2.1. 请求(b2eElectNoteDownLoadNew)

  标记

说明

<xDataBody>

是否

长度

必输

 <insId>

客户端产生的交易唯一标志（★）

Y

64

必输，原值返回，数字字母组成；可做为客户端自

己的交易标识

 <AcNo>

账号

 <innerNo>

回单编号

32

Y

Y

回单查询返回回单编号（base64），多个回单编

号以“|”隔开

<FileName> 文件名称（通过回单查询 ElectnoteListQry 获

N

取）

注意：

未开通 OFD、贴现回单白名单 或 上送的是

非 ElectnoteListQry（1.1）接口返回的回单编

号 innerNo，则需要上送该字段

7 / 15

  标记

说明

<OperFlag> 操作标识

0：T 日回单，

1：T-n 日回单

注意：

民生银行银企直联

是否

长度

必输

N

1

未开通 OFD、贴现回单白名单 或 上送的是

非 ElectnoteListQry（1.1）接口返回的回单编

号 innerNo，则需要上送该字段

 <NeedSign> 是否添加可信签章

0-不添加。

1-添加。

默认为 0

 <pdfStyle>

文件打印类型：

N

N

 N–表示所有回单都生成在一个 PDF，其中每页三

个

 T--表示每 3 张回单生成一个 PDF，生成多个

PDF

 B--表示每页一个回单，所有回单都在一个 PDF

内

 A--表示每个回单单独一个 PDF，并提供 txt 与

PDF 对照表

 OFD 类型文件只支持传 A，只有每页包含 1 笔回

单，每笔回单单独生产 1 个 A5 大小 OFD 回

单。

 贴现回单只支持传 B，仅生成一张 PDF,所有回单

都在一个 PDF 内，贴现回单仅有 pdf 类型文件

 客户如果不上送，默认赋值 N

8 / 15

  标记

说明

</xDataBody>

2.2. 响应(b2eElectNoteDownLoadNew)

  标记

说明

<xDataBody>

民生银行银企直联

是否

长度

必输

是否

长度

必返

 <insId>

客户端产生的交易唯一标志

Y

64

必输，原值返回，数字字母组成；可做为客

户端自己的交易标识

 <FileContent >

文件流：

经 Base64 加密之后的字符串，需要经过

sun.misc.BASE64Decoder ;

sun.misc.BASE64Encoder;

org.apache.commons.codec.binary.Ba
se64;解密。

 <FileName>

文件名称

</xDataBody>

2.3. 报文示例

2.3.1. 请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="b2eElectNoteDownLoadNew">

<requestHeader>

9 / 15

民生银行银企直联

<dtClient>2021-07-08 17:44:01</dtClient>

<clientId>2200003220</clientId>

<userId>2200003220001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<insId>CMBCINS202107081744011</insId>

<AcNo>*********</AcNo>

<innerNo>dDFvZmQyMDI0LTExLTE0XzM1MTY2ODQxMTU3</innerNo>

<FileName>t1ofd2024-11-14_35166841157</FileName>

<OperFlag>1</OperFlag>

<pdfStyle>A</pdfStyle>

<extendReq1></extendReq1>

<extendReq2></extendReq2>

<extendReq3></extendReq3>

</xDataBody>

</CMBC>

2.3.2. 响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="b2eElectNoteDownLoadNew"

header="100" lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2024-11-15 15:40:33</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

10 / 15

民生银行银企直联

<xDataBody>

<insId>CMBCINS202107081744011</insId>

<FileContent>UEsDBBQAAAgIACl9b1kFJg7if30AAC6CAABXAAAAMzEzNTEyMDI0

MTExNTAzMDA1MDYxNDMzMTMwMDAwMDAvc2lnbmVkMzEzNTEyMDI0MTExNT

AzMDA1MDYxNDMzMTMwMDAwMDBfMzUxNjY4NDExNTcub2ZkfHpzcKbdt2WsDj

u21bGTjm12bDtvbNu2bdu2batjO3mTzPfdunVrpmbm91Sdfer58+xVa6296hwFa

XAINBAYGBgQKm9bDZD/7fv5zxKxNdJjZPinOtuYAJzo3WysdRJ0bAeI0EJuXmhgX

miWI21wIhU7ySm0DIFj9JgiVcZ0rH6NXx93inRPBUEPPbP7eIjZMpkVEL4yFvSiUNC

ScKCoygRtVMxtmz48vf3UPhljhwZZ0h41aYx9JTC8LUpQ8iNLRkH0qdKGRjosGRIq

eQa/3fzXzOiMVSTjUYWFgKIZ2Xum6Yfz5gHvEdCqRfosw5pezh2WcpJcqQhygnAh

4B/RusKm1i1SDG8rdxkWeFeonv5+owJUrjupWR23PRenM/B3h8va9ts2/cytqMH6

cqwfSayBO+KAT8l5ITgjms5F4oiOXvw+5eFmz2RVN367wOjfjX7vc7gsxMaXQZx

HrM3K3VVEbo6DPwSnNOrHblhjvVCNNonOOYR+0z2WtzHiVmChoqBV4kMBpZ7JJ

/OMtrsH8S4uUtjsC13LIYFVK1V7G3uk1E/4kdEtz4dHpiPIHg6zVfZrVmgW2x8j+t/ff

d8K0tAwBOebJQagICBU4CAgCv9fgKD/WfJiIv+i4pyg55DJiDyy6wkndqOuqSFXN6

o6AuVJgfTJIpnBxyxCxb4YoC05NA3rEPiMxqvqEUO/57fZxJCn89ljq7Yww9CTrBcAb

aNjUAHpj0LqX5M0HmnZSURoy24gQn+U2d4+a3bDIenndGyKRTc28/EZtTC4zzi

wdtWMg91ZnNWPDfuzEEDw4iGoIBKw2U8YZj2NrJ4OrEeir/RkgQjvAh8hdYY2KEp

CBG1CLIUTgUHFB6UXODWXg+5JprfqZVGlf2gogzux+Rt9lXcoJz2sakawKclcS27

mFfVMzH/Tx8+uBawe15OYj2M8G0sF7oVD8nm3Ifp4+3p8vR0O1TBoc9tKoZlc34

jc0tPctfyq+sxb7GZzr24iX8B3+NMBfFDLZc9mRrw035L+NnLG3v2JLbsy/vYrYoVJ

D5Y+4ZhV40eLqSGePMekWzKYr4243pPU95OE2ohb+HeSInCluAYIM1oYuvl5rdE

1u1QDmrU0c1jMdvFXAAxExGMEC/dwvSTwS3nBQxtTqytrw9K1ap0MD38h8BniK

vPMNJpgVP0L6l88Z0tePzP/wfMF7D/hSfQ/hPtjYQZw/K+qx/xfm4GTs4PJv0B3q+rY

Vkihed/UEnWxY5qCoGScwxIO12qKzlvq4QShWBgIYMG9Xsyqa216zMhOyXYFgTS

aPZteDedMd3yqDKM8hDTwyNhFBuPpYwd1DyHZzxmaxTzFDjBz2dffiMGK0bWIc

X1UejtnVn1mWmDpp6JhG5ojZ+0qaEVuIyfs3HpppODhn1Dft5H9cAMVSLfRY3rkf

CfpDV0d73fLwjIb1zotjGJK4HS23zZEmJxT/7v4tTdyusj+PUTn4EdyIi+Ef4eAsk7mg

+sABo3LDA2qnIl3hAOyc++r9cpgIJNKRBmlqAUSAEFs7UfRPS5PKRgz6ROSnsc2Q

dVKZRaFcK+LdLPh+IWaDEH0cfxeym0UjFJQiDGZGf4C/7xKIpZm90iT5UbQvbvL6

VoI1oz5QsO2LMBbW/lmWhHXsKi+k+ryilXVhM9Y7FVTnGCIu01kjs/88ReIivkNvpa

eJ57iTrGwXVRfZb+1

</FileContent>

<FileName>31351202411150300506143313000000.zip</FileName>

</xDataBody>

</CMBC>

11 / 15

