银企直联接口文档

(线上开立银行承兑汇票)

邮箱：<EMAIL>

版本

日期

说明

编写者 审核者

文档修改记录

民生银行银企直联

V1.0.0 2024-06-25

定义接口文档

V1.0.1 2024-07-23

1. 融资文件查询出参新增文件类型

2. 预申请、文件上传、下载接口字

段补充备注

V1.0.2 2024-11-13

1.融资申请结果查询(B2eQueryBa

sicDraftResult)新增入参 channel

-渠道标识，issueAccountNo-出票

账户、返回列表元素新增出参 chan

nel-渠道标识；

2.新增接口：融资申请信息查询(B2

eQueryBasicDraftDetails)；

V2.0.1 2025-02-06

1. 修改生产环境文件上传、文件下

载域名地址：

openapi.cmbc.com.cn: 2443

2. 修 改 获 取 文 件 Tokne 、 文 件 上

传、文件下载接口说明。

V2.0.2 2025-02-18

1. 融资预申请(B2eBasicDraftAp

ply)接口，<tradeContractAmou

nt>贸易合同金额字段增加非必输

说明。

V2.1.0 2025-02-27

融资预申请(B2eBasicDraftApply)

接口新增保证金账户说明。

V2.2.0 2025-03-11

融资预申请(B2eBasicDraftApply)

接口新增字段：

<hashAlg> 签名算法（云证书仅

支持 sm3）、

<x509Cert> 公钥证书（sm3 算

1 / 67

法必输）。

民生银行银企直联

V2.3.1 2025-04-07

1.修改生产环境文件上传、文件下

载域名地址：

生产环境：

https://openapi.cmbc.com.cn:

2443

测试环境：

https://obpgateway.cmbc.com.

cn:10284

2. 文件下载接口修改返回文件流说

明。

3. 将原有签名说明“1.1.4 签名请

求”标题改为“1.2 签名请求”

4. 增加文件上传、下载代码 Demo

2 / 67

文档说明：（必读）

1.请 求 报 文 头 需 上 送 客 户 号 （ clientId ） 、 操 作 员

（userId）、银企直联密码（userPswd）（开通银企

直联后邮件或短信下发的银企交易密码，非 Ukey 密

码，如有疑问请联系客户经理）。其他 XML 报文说明

请参照接口文档：01.银企接口文档公共说明

2.XML 格式字段：接口字段名称。

3.字段说明：字段中文释义

4.是否必输：是否为必输字段：Y（是） / N（不是）

5.长度：

汉字长度：使用字符数（例如“最多小于等于 20 个汉

字”，即：“20”）来限制。

数字长度：使用位数描述整数部分和小数部分的长度限制

（例如“最多 10 位数字，其中 2 位小数”）即：10,

目录

文档说明：（必读） ............................................................................. 0

目录 ................................................................................................ 0

1. 银行承兑汇票融资申请 .......................................................................2

1.1. 融资预申请(B2EBASICDRAFTAPPLY) ...............................................2

1.1.1. 请求(B2eBasicDraftApply) ..................................................................2

1.1.2. 响应(B2eBasicDraftApply) ..................................................................6

1.1.3. 报文示例： ........................................................................................... 8

1.2. 签名请求 ............................................................................... 11

1.2.1. 测试环境模拟签名说明： ..................................................................... 11

1.2.2. 发送报文示例: .................................................................................... 11

1.2.3. 接收报文示例: .................................................................................... 12

1.3. 融资申请提交(B2EDRAFTAPPLY) ..................................................14

1.3.1. 请求(B2eDraftApply) ........................................................................ 14

1.3.2. 响应(B2eDraftApply) ........................................................................ 16

1.3.3. 报文示例： .........................................................................................16

2. 文件上传下载 ............................................................................... 20

2.1. 获取文件 TOKEN(B2EGETTOKEN) ................................................. 20

2.1.1. 请求(B2eGetToken) .......................................................................... 20

2.1.2. 响应(B2eGetToken) .......................................................................... 21

2.1.3. 报文示例 ............................................................................................ 21

2.2. 文件上传(B2EUPLOAD) ............................................................. 22

2.2.1. 参数定义 ............................................................................................ 24

2.2.2. 报文示例 ............................................................................................ 25

2.2.3. 文件上传代码示例 Demo .................................................................... 26

2.3. 文件下载(B2EDOWNLOAD) ......................................................... 27

2.3.1. 参数说明： .........................................................................................28

2.3.2. 报文示例： .........................................................................................29

民生银行银企直联
2.3.3. 文件下载代码示例 Demo .................................................................... 30

3. 通用查询 ..................................................................................... 31

3.1. 融资申请结果查询(B2EQUERYBASICDRAFTRESULT) ...........................31

3.1.1. 请求(B2eQueryBasicDraftResult) .................................................... 32

3.1.2. 响应(B2eQueryBasicDraftResult) .................................................... 33

3.1.3. 报文示例： .........................................................................................35

3.2. 融资文件查询(B2EQUERYCONTRACTINFO) ...................................... 37

3.2.1. 请求(B2eQueryContractInfo) ........................................................... 37

3.2.2. 响应(B2eQueryContractInfo) ........................................................... 37

3.2.3. 报文示例： .........................................................................................38

3.3. 开户行信息查询 (B2ENBSQUERYBANKINFO) ................................... 40

3.3.1. 请求(B2eNbsQueryBankInfo) ........................................................... 40

3.3.2. 响应(B2eNbsQueryBankInfo) ........................................................... 41

3.3.3. 报文示例： .........................................................................................41

3.4. 银承中收减免审批单号查询(B2EQUERYTASKNO) ...............................43

3.4.1. 请求(B2eQueryTaskNo) ....................................................................43

3.4.2. 响应(B2eQueryTaskNo) ....................................................................43

3.4.3. 报文示例： .........................................................................................44

3.5. 银承客户协议查询(B2EQUERYCUSTAGREEMENT) .............................. 45

3.5.1. 请求(B2eQueryCustAgreement) ...................................................... 45

3.5.2. 响应(B2eQueryCustAgreement) ...................................................... 46

3.5.3. 报文示例： .........................................................................................47

3.6. 融资申请信息查询(B2EQUERYBASICDRAFTDETAILS) ..........................50

3.6.1. 请求(B2eQueryBasicDraftDetails) ................................................... 50

3.6.2. 响应(B2eQueryBasicDraftDetails) ................................................... 50

3.6.3. 报文示例： .........................................................................................57

1 / 67

民生银行银企直联

1.银行承兑汇票融资申请

1.1.融资预申请(B2eBasicDraftApply)

本部分更新日期:2025-03-11

交易规则：通过该交易发起银行承兑汇票融资预申请，生成待签名文件，返回待签名文件

hash 值等信息。

1.1.1.请求(B2eBasicDraftApply)

标记

说明

是否

长度

必输

<xDataBody>

<trnId>

客户技术请求流水号，同一客户请勿

Y

64

重复

<outApplicationNo>

外部融资申请编号，请勿重复

<productCode>

产品编码

<isAllowSplitBill>

<creditType>

******** 基础银承

******** 票据管家

是否可分包

1-可分包

0-不可分包

授信类型

01-综合授信

02-单笔授信(含低风险业务)

<financingAmount>

融资金额

<validFrom>

<validTo>

<custPhone>

出账起始日 yyyy-MM-dd 当天

出账到期日 yyyy-MM-dd

公司客户联系人手机号码，短信业务

Y

Y

32

20

Y

1

Y

2

Y

Y

Y

Y

15,2

10

10

20

2 / 67

民生银行银企直联

<accountType>

保证金账户类型

通知使用

01-活期

02-定期

若无保证金请将<accountType>

删除，不能传空值。

<repayAccountNo>

还款账号

<feeAccountNo>

手续费扣费账号

(银承客户协议查询接口查询)

(银承客户协议查询接口查询)

<bothAgreedMatter>

协议双方约定事项

<taskNo>

银承中收减免审批单号

(减免审批单接口查询)

<isDeduct>

是否从扣款账号中扣划至保证金账户

0-否，1-是

保证金类型为活期时，必输，只支持

划扣至活期保证金账户，定期保证金

账户则该字段无需送值

<deductAccountNo>

扣款账号

选择是从扣款账号扣款之后，选择的

扣款账户，划扣至活期保证金账户

(银承客户协议查询接口查询)

<issuanceFlag>

是否提示收票:

0 否 、1 是

<isAutoDelPayeeInfo>

是否合并票面信息：

N

Y

Y

N

N

N

N

Y

Y

2

32

32

3000

32

1

32

1

2

0 否 、1 是

如果需要合并票面，则会将收款人名

称、开户行行名、收款账号相同的支

付信息合并成一条票面信息，金额累

加作为新的票面金额

3 / 67

<issueAccountNo>

电票签约账号

<endoFlag>

(银承客户协议查询接口查询)

是否可背书转让

EM00 可转让

EM01 不可转让

民生银行银企直联

Y

Y

32

4

<hashAlg>

签名算法（云证书仅支持 sm3）

N

sha1

sha256

sm3

不送默认 sha1

<x509Cert>

公钥证书（使用云证书客户端代理

N

方式，sm3 算法必输）

在当前云证书操作员前置机构 ID 安

装路径（云证书客户端前置客户端

列表点击所对应的“机构 ID”，进

入“基本信息”—>“客户端服务”

—>“客户端安装路径”） 的 FepC

onfig 文件夹下，找到 FepConfig.

properties 文件，打开后找到公钥

的字符串“dispSignCert”。

注意：该字段需上送当前银企直联

操作员所对应公钥字符串，如公钥

上送错误，融资申请 1.2 接口将返

回“PDF 合成外部签名失败”。

<counterPartyList>

支付信息和贸易合同信息

<Map>

<partyName>

交易对手名称

<tradeContractNo>

贸易合同号(非一户一策白名单，必

输，具体请咨询客户经理)

<tradeContractAmount> 贸易合同金额(非一户一策白名单，

Y

Y

N

N

200

100

18,2

4 / 67

必输，具体请咨询客户经理)

民生银行银企直联

该字段为 BigDecimal 格式，

若无需上送该字段，则请求报文中

需删除该字段，不能将该字段上送

为空。否则会返回：“报文体 json

解析失败”

<tradeValidFrom>

贸易合同签署日 yyyy-MM-dd(非一

N

户一策白名单，必输，具体请咨询客

户经理)

<tradeValidTo>

贸易合同到期日 yyyy-MM-dd(非一

N

策一户白名单，且 isValid 为 0（贸

易合同非长期有效）则必输，具体请

咨询客户经理)

<isValid>

贸易合同付款期限是否长期有效,

N

10

10

0 否 、1 是 。

当为 1 时 贸易合同到期日将被忽略

1

(非一户一策白名单，必输，具体请

咨询客户经理)

<isSelfBank>

是否本行： 0 否、 1 是

<depositBankNo>

收款人开户行行号 大额行号

<depositBankName>

收款人开户行名称

<payeeAccountNo>

收款人账号

<paymentAmount>

票面金额(本次支付金额/本次出账金

<remark>

</Map>

</counterPartyList>

<bailInfoList>

额)

票面备注

出票保证金账号列表

低风险业务保证金必输，

若 无 保 证 金 请 将 <bailInfoList>

1

20

200

40

(18,2

)

200

Y

Y

Y

Y

Y

N

Y

N

5 / 67

删除，不能传空值

民生银行银企直联

<Map>

<accountType>

账号类型

01 活期 、02 定期

<bailAccountNo>

保证金账号

<addAmount>

开票保证金金额

</Map>

</bailInfoList>

<fileListInfo>

<Map>

<fileName>

融资预申请或融资申请提交二选一上

送

附件文件名

文件类型

04 贸易合同附件

135 增值税纳税申报表附件(低风险

<fileType>

自动授信)

Y

Y

Y

N

Y

Y

2

50

18,2

255

6

136 审计报告附件(低风险自动授信)

139 担保资金证明材料附件

140 其他材料附件

影像 id

系统编号(上送 734_354)

Y

Y

50

10

<imageId>

<sysNo>

</Map>

</fileListInfo>

</xDataBody>

1.1.2.响应(B2eBasicDraftApply)

标记

说明

<xDataBody>

<svrId>

<trnId>

银行渠道交易流水号

客户技术请求流水号，同一客户请勿重复

是否

长度

必输

N

Y

32

64

6 / 67

民生银行银企直联

<outApplicationNo>

外部融资申请编号

<creditCode>

借据号

<checkResultList>

收款人信息列表校验结果

<Map>

<elecAcctNo>

电票帐号

<elecAcctName>

电票账号名称

<elecBankNo>

账号开户行行号

<result>

校验结果

<failReason>

校验未通过原因

</Map>

</checkResultList>

<fileInfoList>

待签章文件信息列表

<Map>

<fileSealhash>

文件 Hash 值

此字段为后续银企直联云证书协同签名客

户端或银企盒子进行签名的入参数据（具

体签名方式请参照 1.2）

<cntrNum>

<id>

合同号

文件 id

有效时间 1 小时，有效期内未提交融资正

式申请（B2eDraftApply）则会导致正式

申请时签章失败

影像文件编号

文件名称

文件类型

119 授信申请书

18 承兑协议

48 信用信息查询使用授权书

<imageId>

<fileName>

<fileType>

</Map>

</fileInfoList>

</xDataBody>

Y

Y

N

Y

Y

Y

Y

N

Y

Y

Y

Y

Y

Y

Y

32

16

40

200

20

12

200

30

32

30

50

255

6

7 / 67

民生银行银企直联

1.1.3.报文示例：

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC

header="100"

version="100"

security="none"

lang="chs"

trnCode="B2eBasicDraftApply">

<requestHeader>

<dtClient>2024-06-26 17:15:18</dtClient>

<clientId>******</clientId>

<userId>*************</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202406260917425</trnId>

<outApplicationNo>ELOAN202403220070234796</outApplicationNo>

<productCode>********</productCode>

<isAllowSplitBill>1</isAllowSplitBill>

<creditType>01</creditType>

<financingAmount>10000</financingAmount>

<validFrom>2024-06-26</validFrom>

<validTo>2024-06-30</validTo>

<custPhone>***********</custPhone>

<accountType>02</accountType>

<repayAccountNo>*********</repayAccountNo>

<feeAccountNo>*********</feeAccountNo>

<bothAgreedMatter>约定事项</bothAgreedMatter>

<taskNo>44100202405140018283367</taskNo>

<isDeduct>0</isDeduct>

<deductAccountNo></deductAccountNo>

<isAutoDelPayeeInfo>1</isAutoDelPayeeInfo>

<issueAccountNo>*********</issueAccountNo>

<endoFlag>EM00</endoFlag>

<issuanceFlag>0</issuanceFlag>

8 / 67

民生银行银企直联

<hashAlg></hashAlg>

<x509Cert></x509Cert>

<counterPartyList>

<Map>

<partyName>李杰拜企业公司</partyName>

<tradeContractNo>1111</tradeContractNo>

<tradeContractAmount>***********</tradeContractAmount>

<tradeValidFrom>2024-05-01</tradeValidFrom>

<tradeValidTo>2024-05-30</tradeValidTo>

<isValid>1</isValid>

<isSelfBank>1</isSelfBank>

<depositBankNo>************</depositBankNo>

<depositBankName> 中 国 民 生 银 行 股 份 有 限 公 司 北 京 中 关 村 支 行

</depositBankName>

<payeeAccountNo>*********</payeeAccountNo>

<paymentAmount>10000</paymentAmount>

<remark>备注</remark>

</Map>

</counterPartyList>

<bailInfoList>

<Map>

<accountType>02</accountType>

<bailAccountNo>*********</bailAccountNo>

<addAmount>5000</addAmount>

</Map>

</bailInfoList>

<fileListInfo></fileListInfo>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC

header="100"

version="100"

security="none"

lang="chs"

trnCode="B2eBasicDraftApply">

<responseHeader>

<status>

<code>0</code>

9 / 67

民生银行银企直联

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-06-26 17:15:45</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31300202406261388811782354002BEB</svrId>

<trnId>CMBCTRN202406260917425</trnId>

<creditCode>****************</creditCode>

<checkResultList />

<fileInfoList>

<Map>

<fileSealHash>U4MrmnOeok9mq8BHPTSxDXynbeo=</fileSealHash>

<cntrNum>******************</cntrNum>

<id>202406261715444267003879097419</id>

<imageId>20240626171133075**********07340045548200019791433

</imageId>

<fileName>20240626171133075**********0734004554820001979143

3.pdf</fileName>

<fileType>48</fileType>

</Map>

<Map>

<fileSealHash>IAqAdq4Hy7dcRZxfF5pHg98Z7Io=</fileSealHash>

<cntrNum>CT0001202404470338</cntrNum>

<id>202406261715447534812797639365</id>

<imageId>20240626171133387**********073400964972000537351

86</imageId>

<fileName>20240626171133387**********07340096497200053735

186.pdf</fileName>

<fileType>18</fileType>

</Map>

</fileInfoList>

<outApplicationNo>ELOAN202403220070234796</outApplicationNo>

10 / 67

民生银行银企直联

</xDataBody>

</CMBC>

1.2.签名请求

说明：

1.目前仅支持通过云证书客户端或银企直联盒子加签，向云证书协同签名客户端或银企盒子发

送 http 请求。

2.请求路径为:POST http://本地 ip+本地代理监听端口/eweb/b2e/connect.do HTTP/1.1

3.报文头增加参数 Business-Type:CMBC_SIGN_HASH 。

4.报文体为融资预申请 1.1 接口返回 “fileSealhash”字段。

1.2.1.测试环境模拟签名说明：

1.若通过银企云证书客户端代理方式获取“fileSealhash”签名，请联系分行对接老师获

取测试环境云证书配置三码信息。

2.若模拟通过银企盒子代理方式获取“fileSealhash”签名，可通过 CFCA（选择指定测试

证书）+“CFCA 签章控件”方式获取。具体请联系分行对接老师获取相关测试物料。

1.2.2.发送报文示例:

POST http://*************:15002/eweb/b2e/connect.do HTTP/1.1

Accept-Encoding:*

/--请求头入参--/

Business-Type:CMBC_SIGN_HASH

Content-Type:application/x-NS-BDES

User-Agent:Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; .NET CLR

1.0.3705;)

Host:*************:15002

Content-Length:28

Connection:Close

/--报文体--/

2p4zDpn5WQdCi34uWlPh4QPoZ/Y=

11 / 67

注意:“*************:15002”处为本地 ip+云证书协同签名客户端监听端口/银企盒子

民生银行银企直联

监听端口 /，此处仅为示例。

1.2.3.接收报文示例:

HTTP/1.1 OK OK

Connection:close

Transaction-Schema:Proxy1000/******* AsynAlg/RSA2048

Content-Length:4363

Content-Type:application/octet-stream

Date:Sat, 26 Nov 2022 12:45:59 GMT

Server:sslgw/4.02.1

<?xml version="1.0" encoding="UTF-8"?>

<CMBC security="none" trnCode="CMBCSIGN" header="100" lang="chs"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>Hash 签名成功</message>

</status>

<dtServer>2022-11-26 20:45:59</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<fileHash>2p4zDpn5WQdCi34uWlPh4QPoZ/Y=

</fileHash>

<signResult>MIIGSQYJKoZIhvcNAQcCoIIGOjCCBjYCAQExCzAJBgUrDgMCGgUAMAs

GCSqGSIb3DQEHAaCCBLYwggSyMIIDmqADAgECAgUwFHg1GTANBgkqhkiG9w0BA

QUFADArMQswCQYDVQQGEwJDTjEcMBoGA1UECgwTQ0ZDQSBSU0EgVEVTVCBPQ

0EyMTAeFw0yMTEyMDMwNzU1MDNaFw0yNjEyMDMwNzU1MDNaMGYxCzAJBgNV

BAYTAkNOMQ0wCwYDVQQKDARDTUJDMRIwEAYDVQQLDAlDTUJDX0RDTVMxGTAX

BgNVBAsMEE9yZ2FuaXphdGlvbmFsLTMxGTAXBgNVBAMMEDAzMDU2NDkxMDAw

MDAxNzMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDhi6WbUJnsdR

BSMYk+VVhYWVHksTHgqCU2Nn3wQEeK2bqaBoWcNUz2EPligBAft9uBNP/E1fcOC

KhXsRQ4W0CPS9h9o5a2OhBfg322KvhdrF9O/tZqTAfGSsTS92n4aZSy7xU/ZilSb6h

12 / 67

民生银行银企直联

E/8oRv4umsrppHToociYfgCrIUxu5h2ozJ8X/rz2Gf50L1hLwDY329qQG9PVcUuKpDB

e2bFnzgXYiacPBjlGEgX0ovOpE2s8Y1hWsciChP8rVbd9hFmAzdwYB6uMgS1pFhGc

U1XzXQysD3o+vev9UnuOJoF6zxQfsl/guEeD5Dy3bdomU4lZ+5Btaf9A9/XMjG3exR

0M/AgMBAAGjggGgMIIBnDAfBgNVHSMEGDAWgBTP35n7hiIWEzksB16OPXcruWnvj

jAMBgNVHRMBAf8EAjAAMEgGA1UdIARBMD8wPQYIYIEchu8qAgIwMTAvBggrBgEFB

QcCARYjaHR0cDovL3d3dy5jZmNhLmNvbS5jbi91cy91cy0xMy5odG0wgdIGA1UdH

wSByjCBxzAuoCygKoYoaHR0cDovLzIxMC43NC40Mi4zL09DQTIxL1JTQS9jcmw4MT

ExLmNybDCBlKCBkaCBjoaBi2xkYXA6Ly8yMTAuNzQuNDIuMTA6Mzg5L0NOPWNyb

DgxMTEsT1U9UlNBLE9VPUNSTCxPPUNGQ0EgU00yIFRFU1QgT0NBMjEsQz1DTj9jZ

XJ0aWZpY2F0ZVJldm9jYXRpb25MaXN0P2Jhc2U/b2JqZWN0Y2xhc3M9Y1JMRGlzdHJ

pYnV0aW9uUG9pbnQwDgYDVR0PAQH/BAQDAgPIMB0GA1UdDgQWBBQyG3gt8to

SZIGZ0z+KY+VkbwrXHTAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQwD

QYJKoZIhvcNAQEFBQADggEBAAzd7wsiq7mJNyBuapGBp9eA/0EIGwDJMLX8re28Cp

7cr3OfvZx/ONLHWt9jsAjVmb1dYF6ekKc3jmnOitnP42K/NE9Ck/1KezJzCys4YFdK+is

13LjgVKKyBf6EKze/5FXVNQvDvADpNniHoF+Y27FEshUM2djicaiRK/iKfXdLlcaQoKu

hA5sDHcCV+7sZ83ZR4LYpMR5qGVp+9Ym0LIiH/NJlPvKGuG1mroNQW4WLHKH1V

VPv7QjpV3nZOD62VgRN62foK2LJ2d9HOTsvMUmr+pYYNe/DVgggqZOcJgHGxykS4

GLa5YzPiHSVULkKq23cXMnxFyK6pan4TFZkxRQxggFbMIIBVwIBATA0MCsxCzAJBg

NVBAYTAkNOMRwwGgYDVQQKDBNDRkNBIFJTQSBURVNUIE9DQTIxAgUwFHg1GTA

JBgUrDgMCGgUAMA0GCSqGSIb3DQEBAQUABIIBAHr0jYSlFygS1+dT41Z8NcbneNB

0CIqxeqwFclDWzERyA1ewAKhqZguOCy8CtBsYFvgpR400iwY1Dl6JfatDKyoR5JGBA

5KfqEPK0prZ2CmFI57OELuZ+Tlgs0Mg0Fzaknh574UU7yOygdC95UagKMr3EdG5Vl

NC3y3e3AFEKc3kozVxStuuXeF9hfO0T0F6IZ9GETSp7RaCwtc/N0n2T8oQbyXzrc8z

b0i7+l6aMqbCPbk2jZK+uup0x9+wqVCVy/vHjE/Vz/FXX03Qam2PfYv8Li4cub/3hxla

TdQoa0Xx030bTNPNfUfQdmnw8ndFXEPOPZ9d7Ck+gd3Io3rA0Fo=</signResult>

<custPubCert>MIIEsjCCA5qgAwIBAgIFMBR4NRkwDQYJKoZIhvcNAQEFBQAwKzEL

MAkGA1UEBhMCQ04xHDAaBgNVBAoME0NGQ0EgUlNBIFRFU1QgT0NBMjEwHhcN

MjExMjAzMDc1NTAzWhcNMjYxMjAzMDc1NTAzWjBmMQswCQYDVQQGEwJDTjENM

AsGA1UECgwEQ01CQzESMBAGA1UECwwJQ01CQ19EQ01TMRkwFwYDVQQLDBBP

cmdhbml6YXRpb25hbC0zMRkwFwYDVQQDDBAwMzA1NjQ5MTAwMDAwMTczMIIBI

jANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4Yulm1CZ7HUQUjGJPlVYWFlR5

LEx4KglNjZ98EBHitm6mgaFnDVM9hD5YoAQH7fbgTT/xNX3DgioV7EUOFtAj0vYfaO

WtjoQX4N9tir4XaxfTv7WakwHxkrE0vdp+GmUsu8VP2YpUm+oRP/KEb+LprK6aR0

6KHImH4AqyFMbuYdqMyfF/689hn+dC9YS8A2N9vakBvT1XFLiqQwXtmxZ84F2Imn

DwY5RhIF9KLzqRNrPGNYVrHIgoT/K1W3fYRZgM3cGAerjIEtaRYRnFNV810MrA96Pr3

r/VJ7jiaBes8UH7Jf4LhHg+Q8t23aJlOJWfuQbWn/QPf1zIxt3sUdDPwIDAQABo4IBoDC

CAZwwHwYDVR0jBBgwFoAUz9+Z+4YiFhM5LAdejj13K7lp744wDAYDVR0TAQH/BA

IwADBIBgNVHSAEQTA/MD0GCGCBHIbvKgICMDEwLwYIKwYBBQUHAgEWI2h0dHA6

Ly93d3cuY2ZjYS5jb20uY24vdXMvdXMtMTMuaHRtMIHSBgNVHR8EgcowgccwLqAs

13 / 67

民生银行银企直联

oCqGKGh0dHA6Ly8yMTAuNzQuNDIuMy9PQ0EyMS9SU0EvY3JsODExMS5jcmwwgZ

SggZGggY6GgYtsZGFwOi8vMjEwLjc0LjQyLjEwOjM4OS9DTj1jcmw4MTExLE9VPVJT

QSxPVT1DUkwsTz1DRkNBIFNNMiBURVNUIE9DQTIxLEM9Q04/Y2VydGlmaWNhdGV

SZXZvY2F0aW9uTGlzdD9iYXNlP29iamVjdGNsYXNzPWNSTERpc3RyaWJ1dGlvblBva

W50MA4GA1UdDwEB/wQEAwIDyDAdBgNVHQ4EFgQUMht4LfLaEmSBmdM/imPlZG

8K1x0wHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMEMA0GCSqGSIb3DQE

BBQUAA4IBAQAM3e8LIqu5iTcgbmqRgafXgP9BCBsAyTC1/K3tvAqe3K9zn72cfzjSx1

rfY7AI1Zm9XWBenpCnN45pzorZz+NivzRPQpP9SnsycwsrOGBXSvorNdy44FSisgX

+hCs3v+RV1TULw7wA6TZ4h6BfmNuxRLIVDNnY4nGokSv4in13S5XGkKCroQObAx

3Alfu7GfN2UeC2KTEeahlafvWJtCyIh/zSZT7yhrhtZq6DUFuFixyh9VVT7+0I6Vd52Tg

+tlYETetn6CtiydnfRzk7LzFJq/qWGDXvw1YIIKmTnCYBxscpEuBi2uWMz4h0lVC5Cqtt

3FzJ8RciuqWp+ExWZMUU</custPubCert>

<signAlg>RSA</signAlg>

</xDataBody>

</CMBC>

1.3. 融资申请提交(B2eDraftApply)

本部分更新日期:2024-06-25

交易规则：

1、通过预申请获取到的待签名文件信息，通过银企云证书协同客户端或银企签名盒子对 PDF
文件的 Hash 值签名，具体可参照“1.2 签名请求”。

2、签名成功后将预申请结果放入融资申请提交参数中，进行正式的提交操作

1.3.1.请求(B2eDraftApply)

标记

说明

<xDataBody>

<insId>

客户业务请求流水号，同一业务请求请勿

重复

<trnId>

客户技术请求流水号，同一客户请勿重复

是否

长度

必输

Y

Y

64

64

14 / 67

标记

说明

<outApplicationNo>

外部融资申请编号

<creditCode>

借据号

<fileListInfo>

融资预申请或融资申请提交二选一上送

<Map>

<fileName>

附件文件名

文件类型

04 贸易合同附件

民生银行银企直联

是否

长度

必输

Y

Y

N

Y

32

16

255

135 增值税纳税申报表附件(低风险自动

<fileType>

授信)

Y

6

136 审计报告附件(低风险自动授信)

139 担保资金证明材料附件

140 其他材料附件

影像 id

系统编号(上送 734_354)

<imageId>

<sysNo>

</Map>

</fileListInfo>

<signatureFileList>

签名文件信息列表

<Map>

<signature>

盒子签名结果数据

<imageId>

影像文件编号

<fileName>

<fileType>

<sysNo>

<cntrNum>

<id>

</Map>

文件名称

文件类型

119 授信申请书

18 承兑协议

48 信用信息查询使用授权书

系统编号（上送 734_734）

合同号

文件 id

Y

Y

Y

Y

Y

Y

Y

Y

Y

Y

50

10

List

50

255

6

10

32

30

15 / 67

标记

说明

</signatureFileList>

</xDataBody>

1.3.2.响应(B2eDraftApply)

标记

说明

民生银行银企直联

是否

长度

必输

10

是否

长度

必输

N

Y

Y

Y

32

64

64

64

Y

64

银行渠道交易流水号

客户业务请求流水号，同一业务请求请勿重

复

客户技术请求流水号，同一客户请勿重复

错误码

1：成功

0：失败

返回信息

1：交易成功

0：具体失败错误信息

<xDataBody>

<svrId>

<insId>

<trnId>

<retCode>

<retMsg>

</xDataBody>

1.3.3.报文示例：

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eDraftApply">

<requestHeader>

<dtClient>2024-06-26 17:19:08</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

16 / 67

民生银行银企直联

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202406261719033</trnId>

<insId>CMBCINS202406261719034</insId>

<outApplicationNo>ELOAN202403220070234796</outApplicationNo>

<creditCode>****************</creditCode>

<signatureFileList>

<Map>

<!-- 使用预交易的 fileSealHash 加签之后生成的值 -->

<sysNo>734_734</sysNo>

<signature>MIIGSQYJKoZIhvcNAQcCoIIGOjCCBjYCAQExCzAJBgUrDgMCGgUA

MAsGCSqGSIb3DQEHAaCCBLYwggSyMIIDmqADAgECAgUwFHg1GTANBgkqhkiG9

w0BAQUFADArMQswCQYDVQQGEwJDTjEcMBoGA1UECgwTQ0ZDQSBSU0EgVEVTV

CBPQ0EyMTAeFw0yMTEyMDMwNzU1MDNaFw0yNjEyMDMwNzU1MDNaMGYxCzAJ

BgNVBAYTAkNOMQ0wCwYDVQQKDARDTUJDMRIwEAYDVQQLDAlDTUJDX0RDTVMx

GTAXBgNVBAsMEE9yZ2FuaXphdGlvbmFsLTMxGTAXBgNVBAMMEDAzMDU2NDkx

MDAwMDAxNzMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDhi6Wb

UJnsdRBSMYk+VVhYWVHksTHgqCU2Nn3wQEeK2bqaBoWcNUz2EPligBAft9uBNP/E

1fcOCKhXsRQ4W0CPS9h9o5a2OhBfg322KvhdrF9O/tZqTAfGSsTS92n4aZSy7xU/Zil

Sb6hE/8oRv4umsrppHToociYfgCrIUxu5h2ozJ8X/rz2Gf50L1hLwDY329qQG9PVcUuK

pDBe2bFnzgXYiacPBjlGEgX0ovOpE2s8Y1hWsciChP8rVbd9hFmAzdwYB6uMgS1pF

hGcU1XzXQysD3o+vev9UnuOJoF6zxQfsl/guEeD5Dy3bdomU4lZ+5Btaf9A9/XMjG3

exR0M/AgMBAAGjggGgMIIBnDAfBgNVHSMEGDAWgBTP35n7hiIWEzksB16OPXcru

WnvjjAMBgNVHRMBAf8EAjAAMEgGA1UdIARBMD8wPQYIYIEchu8qAgIwMTAvBggrB

gEFBQcCARYjaHR0cDovL3d3dy5jZmNhLmNvbS5jbi91cy91cy0xMy5odG0wgdIGA1

UdHwSByjCBxzAuoCygKoYoaHR0cDovLzIxMC43NC40Mi4zL09DQTIxL1JTQS9jcmw

4MTExLmNybDCBlKCBkaCBjoaBi2xkYXA6Ly8yMTAuNzQuNDIuMTA6Mzg5L0NOPW

NybDgxMTEsT1U9UlNBLE9VPUNSTCxPPUNGQ0EgU00yIFRFU1QgT0NBMjEsQz1DTj

9jZXJ0aWZpY2F0ZVJldm9jYXRpb25MaXN0P2Jhc2U/b2JqZWN0Y2xhc3M9Y1JMRGlz

dHJpYnV0aW9uUG9pbnQwDgYDVR0PAQH/BAQDAgPIMB0GA1UdDgQWBBQyG3gt

8toSZIGZ0z+KY+VkbwrXHTAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQ

wDQYJKoZIhvcNAQEFBQADggEBAAzd7wsiq7mJNyBuapGBp9eA/0EIGwDJMLX8re2

8Cp7cr3OfvZx/ONLHWt9jsAjVmb1dYF6ekKc3jmnOitnP42K/NE9Ck/1KezJzCys4YFd

K+is13LjgVKKyBf6EKze/5FXVNQvDvADpNniHoF+Y27FEshUM2djicaiRK/iKfXdLlcaQ

17 / 67

民生银行银企直联

oKuhA5sDHcCV+7sZ83ZR4LYpMR5qGVp+9Ym0LIiH/NJlPvKGuG1mroNQW4WLHK

H1VVPv7QjpV3nZOD62VgRN62foK2LJ2d9HOTsvMUmr+pYYNe/DVgggqZOcJgHGx

ykS4GLa5YzPiHSVULkKq23cXMnxFyK6pan4TFZkxRQxggFbMIIBVwIBATA0MCsxCz

AJBgNVBAYTAkNOMRwwGgYDVQQKDBNDRkNBIFJTQSBURVNUIE9DQTIxAgUwFHg

1GTAJBgUrDgMCGgUAMA0GCSqGSIb3DQEBAQUABIIBAA14cJiih2DJPhXzNsAAEYsk

TNGg2dm8Y+i1sCM4t7p7JVTIM2dPikDtDvjxf38CQSmeiRD/OBrnaE7dK6VoM/7LR8

LwaYDgb9VHKYA5iQi0PxQmmyfQmUybrUQZviV3OOk0DNTIENwGKBpbqPPKc8uCj

Swp9gRhXXVit8MF0+7uQtELisTzGjhWP0kQE98YT0MmVbklfgZ7R+1uvwhEh1+sm

gxdGdfHcXHS6iJzSL+wRPcmAKyDm7towF0Ki2LWTQVm0M/l+FD0DbW6AuzWpOE

U0LislFg1CTZgE8u6LMPr1dddzty69hHjZvcecyZaQ5Ud1iQcLB69RLZlIOGjRkg=</si

gnature>

<cntrNum>******************</cntrNum>

<id>202406261715444267003879097419</id>

<imageId>20240626171133075**********07340045548200019791433<

/imageId>

<fileName>20240626171133075**********07340045548200019791433.pdf<

/fileName>

<fileType>48</fileType>

</Map>

<Map>

<!-- 使用预交易的 fileSealHash 加签之后生成的值 -->

<sysNo>734_734</sysNo>

<signature>MIIGSQYJKoZIhvcNAQcCoIIGOjCCBjYCAQExCzAJBgUrDgMCGgUA

MAsGCSqGSIb3DQEHAaCCBLYwggSyMIIDmqADAgECAgUwFHg1GTANBgkqhkiG9

w0BAQUFADArMQswCQYDVQQGEwJDTjEcMBoGA1UECgwTQ0ZDQSBSU0EgVEVTV

CBPQ0EyMTAeFw0yMTEyMDMwNzU1MDNaFw0yNjEyMDMwNzU1MDNaMGYxCzAJ

BgNVBAYTAkNOMQ0wCwYDVQQKDARDTUJDMRIwEAYDVQQLDAlDTUJDX0RDTVMx

GTAXBgNVBAsMEE9yZ2FuaXphdGlvbmFsLTMxGTAXBgNVBAMMEDAzMDU2NDkx

MDAwMDAxNzMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDhi6Wb

UJnsdRBSMYk+VVhYWVHksTHgqCU2Nn3wQEeK2bqaBoWcNUz2EPligBAft9uBNP/E

1fcOCKhXsRQ4W0CPS9h9o5a2OhBfg322KvhdrF9O/tZqTAfGSsTS92n4aZSy7xU/Zil

Sb6hE/8oRv4umsrppHToociYfgCrIUxu5h2ozJ8X/rz2Gf50L1hLwDY329qQG9PVcUuK

pDBe2bFnzgXYiacPBjlGEgX0ovOpE2s8Y1hWsciChP8rVbd9hFmAzdwYB6uMgS1pF

hGcU1XzXQysD3o+vev9UnuOJoF6zxQfsl/guEeD5Dy3bdomU4lZ+5Btaf9A9/XMjG3

exR0M/AgMBAAGjggGgMIIBnDAfBgNVHSMEGDAWgBTP35n7hiIWEzksB16OPXcru

WnvjjAMBgNVHRMBAf8EAjAAMEgGA1UdIARBMD8wPQYIYIEchu8qAgIwMTAvBggrB

gEFBQcCARYjaHR0cDovL3d3dy5jZmNhLmNvbS5jbi91cy91cy0xMy5odG0wgdIGA1

UdHwSByjCBxzAuoCygKoYoaHR0cDovLzIxMC43NC40Mi4zL09DQTIxL1JTQS9jcmw

18 / 67

民生银行银企直联

4MTExLmNybDCBlKCBkaCBjoaBi2xkYXA6Ly8yMTAuNzQuNDIuMTA6Mzg5L0NOPW

NybDgxMTEsT1U9UlNBLE9VPUNSTCxPPUNGQ0EgU00yIFRFU1QgT0NBMjEsQz1DTj

9jZXJ0aWZpY2F0ZVJldm9jYXRpb25MaXN0P2Jhc2U/b2JqZWN0Y2xhc3M9Y1JMRGlz

dHJpYnV0aW9uUG9pbnQwDgYDVR0PAQH/BAQDAgPIMB0GA1UdDgQWBBQyG3gt

8toSZIGZ0z+KY+VkbwrXHTAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQ

wDQYJKoZIhvcNAQEFBQADggEBAAzd7wsiq7mJNyBuapGBp9eA/0EIGwDJMLX8re2

8Cp7cr3OfvZx/ONLHWt9jsAjVmb1dYF6ekKc3jmnOitnP42K/NE9Ck/1KezJzCys4YFd

K+is13LjgVKKyBf6EKze/5FXVNQvDvADpNniHoF+Y27FEshUM2djicaiRK/iKfXdLlcaQ

oKuhA5sDHcCV+7sZ83ZR4LYpMR5qGVp+9Ym0LIiH/NJlPvKGuG1mroNQW4WLHK

H1VVPv7QjpV3nZOD62VgRN62foK2LJ2d9HOTsvMUmr+pYYNe/DVgggqZOcJgHGx

ykS4GLa5YzPiHSVULkKq23cXMnxFyK6pan4TFZkxRQxggFbMIIBVwIBATA0MCsxCz

AJBgNVBAYTAkNOMRwwGgYDVQQKDBNDRkNBIFJTQSBURVNUIE9DQTIxAgUwFHg

1GTAJBgUrDgMCGgUAMA0GCSqGSIb3DQEBAQUABIIBAEYFMYcu0pNVtdtTOMW8C

LYFhKWIPByiqcHtudEz0vHFtIhyWjC2kAj3H0SW7Psm2Ab2QPnDG2t2dGZvfJWFZ+

6+i0XNd/iMQvacJBrZLkARP/GI9t+QCE+hWVF34bMYDUgtvLFd2/Wx/bBNhkmS1pf/

o8i8gzuXxdl7JMa1a5b2FzFNrNtch6xxUtCzCrwzCM747SCo4PLWmYd8gcw9dM3z3

2yT4AMLGATtvHSMwtWuPrExIrcaYSc9HHACGEOoyj8y6DAmaaQrVuS1EhhsUc0NG

MG3ohj7o8RLmMOeWO1Yll7AbCnPAeVTrPrxuO9zQV6mQ2piOwm9LNyZ563tciM=

</signature>

<cntrNum>CT0001202404470338</cntrNum>

<id>202406261715447534812797639365</id>

<imageId>20240626171133387**********07340096497200053735186<

/imageId>

<fileName>20240626171133387**********07340096497200053735186.

pdf</fileName>

<fileType>18</fileType>

</Map>

</signatureFileList>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eDraftApply">

<responseHeader>

<status>

19 / 67

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-06-26 17:19:22</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31300202406261388781647354002FRL</svrId>

<insId>CMBCINS202406261719034</insId>

<trnId>CMBCTRN202406261719033</trnId>

<retCode>1</retCode>

<retMsg>交易成功</retMsg>

</xDataBody>

</CMBC>

2.文件上传下载

2.1.获取文件 token(B2eGetToken)

本部分更新日期:2025-02-06

接口说明：获取文件 token，

若返回“该客户没有获取 Token 权限的权限！”，请联系分行老师向总行银企直联合网金发

送获取 Token 权限申请邮件并附带客户号等信息。

2.1.1.请求(B2eGetToken)

标记

说明

是否

长度

必输

<xDataBody>

<trnId>

客户技术请求流水号，同一客户请勿重复

Y

64

20 / 67

民生银行银企直联

是否

长度

必输

Y

N

64

64

</xDataBody>

2.1.2.响应(B2eGetToken)

标记

说明

客户端交易的唯一标志

文件上传 token

<xDataBody>

<trnId>

<tokenId>

</xDataBody>

2.1.3.报文示例

请求报文：

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eGetToken">

<requestHeader>

<dtClient>2021-10-08 20:05:29</dtClient>

<userPswd>******</userPswd>

<clientId>**********</clientId>

<userId>*************</userId>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202212261752324</trnId>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eGetToken">

<responseHeader>

<status>

21 / 67

民生银行银企直联

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-11-03 09:45:39</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>CMBCTRN202212261752324</trnId>

<tokenId>TF947947BA884H9Y4eaM8ab97d764fdR27R07EFddfS9451PQ7EA

bd46R62bVD6D</tokenId>

</xDataBody>

</CMBC>

2.2.文件上传(b2eUpload)

本部分更新日期:2025-02-06

一．接入方式：

使用文件上传下载功能时，调用地址是：

https://ip:port/custom/b2e/接口?sequence=${流水号}$。例如：

“ip:port”信息请参照下方“二.环境信息”

二．环境信息：

环境

域名

测试环境

https://obpgateway.cmbc.com.cn:10284/custom/b2e/*

生产环境

https://openapi.cmbc.com.cn: 2443/custom/b2e/*

三．接口说明：

1. 上传贸易合同附件

22 / 67

2. 该接口支持单个以及多个文件的上传。

需要注意的是文件大小的限制问题：目前是写死的 50M，如果文件超过 50M，APACHE

民生银行银企直联

会正常返回文件超限。

3. 支持的文件格式：

FILE_HEADER_MAP.put("jpg", "FFD8FF");

FILE_HEADER_MAP.put("png", "89504E47");

FILE_HEADER_MAP.put("gif", "47494638");

FILE_HEADER_MAP.put("tif", "49492A00");

FILE_HEADER_MAP.put("bmp", "424D");

FILE_HEADER_MAP.put("dwg", "41433130");

FILE_HEADER_MAP.put("psd", "38425053");

FILE_HEADER_MAP.put("rtf", "7B5C727466");

FILE_HEADER_MAP.put("xml", "3C3F786D6C");

FILE_HEADER_MAP.put("html", "68746D6C3E");

FILE_HEADER_MAP.put("eml", "44656C69766572792D646174653A");

FILE_HEADER_MAP.put("dbx", "CFAD12FEC5FD746F");

FILE_HEADER_MAP.put("pst", "2142444E");

FILE_HEADER_MAP.put("xls", "D0CF11E0");

FILE_HEADER_MAP.put("doc", "D0CF11E0");

FILE_HEADER_MAP.put("ppt", "D0CF11E0");

FILE_HEADER_MAP.put("xlsx", "504B0304");

FILE_HEADER_MAP.put("docx", "504B0304");

FILE_HEADER_MAP.put("mdb", "5374616E64617264204A");

FILE_HEADER_MAP.put("wpd", "FF575043");

FILE_HEADER_MAP.put("pdf", "255044462D312E");

FILE_HEADER_MAP.put("qdf", "AC9EBD8F");

FILE_HEADER_MAP.put("pwl", "E3828596");

FILE_HEADER_MAP.put("zip", "504B0304");

FILE_HEADER_MAP.put("rar", "52617221");

FILE_HEADER_MAP.put("wav", "57415645");

FILE_HEADER_MAP.put("avi", "41564920");

FILE_HEADER_MAP.put("ram", "2E7261FD");

FILE_HEADER_MAP.put("rm", "2E524D46");

FILE_HEADER_MAP.put("mpg", "000001BA");

FILE_HEADER_MAP.put("mov", "6D6F6F76");

FILE_HEADER_MAP.put("asf", "3026B2758E66CF11");

FILE_HEADER_MAP.put("mid", "4D546864");

23 / 67

民生银行银企直联

FILE_HEADER_MAP.put("mp4", "00000020667479706d70");

FILE_HEADER_MAP.put("mlog", "0100000060");

FILE_HEADER_MAP.put("jpeg", "FFD8FF");

FILE_HEADER_MAP.put("7z", "377ABCAF271C");

2.2.1.参数定义

入参报文为 form-data 形式

出入

参数

参数名

参数类

备注

型

是否

必输

入参

sceneId

String

场景标识，B2eDraftContractAttach 合

Y

files

File

Array

同附件

文件流数组

fileToken

String

必选，推荐每次上传文件前都获取新

（64）

Token

Y

N

isUnzip

String

如果需解压上传，则传 Y。只允许单压

N

（2）

缩文件（支持的格式：jpg, jpeg, png,

pdf），且文件中不包括文件夹，压缩文

件中单文件大小不超过 10M

出参

code

String

返回码

msg

String

返回内容

response

Object 响应体，内容可参考 2.2.2 报文示例

response.returnCo

Object 响应体头

de

response.files

Object

文件信息

Array

resposne.uuid

String

唯一主键

24 / 67

民生银行银企直联

2.2.2.报文示例

请求参数

请求 Value

sceneId

testScene

fileToken

FBHFUCCBNS788S9G4EASUAB97D764FDW27907HKDDFH9S51NP7N9B

DM6B62B6PGN

files

1.jpg

"returnCode":{

"code":"AAAAAAA",

"message":"交易成功.",

"type":"S"},

"files":[{

"realName":"2.jpg",

"imageId":"20240117150459039230089001703540016935500458187161"}

]

请求示例：

响应示例：

25 / 67

民生银行银企直联

2.2.3.文件上传代码示例 Demo

以下为简易的请求接口上传文件的示例 Demo：

private static void uploadFile(String token) throws IOException {

// 设置请求地址

String url = "http://obpmapp.obp.uat.cmbc.cn:19011/custom/b2e/b2eUpload";

// 需要上传的文件路径

String filePath = "test.pdf";

// 附件上传时固定的 sceneId

String sceneId = "B2eDraftProto";

// 通过 b2eGetToken 获取的 token,每次上传之前都重新获取

String fileToken = token;

try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

// 创建请求对象，设置请求地址和请求参数等，需要读取文件流

HttpPost httpPost = new HttpPost(url);

File file = new File(filePath);

HttpEntity entity = MultipartEntityBuilder.create()

.setMode(HttpMultipartMode.STRICT)

.addTextBody("sceneId", sceneId)

.addTextBody("fileToken", fileToken)

.addBinaryBody("files", file, ContentType.DEFAULT_BINARY, file.getName

())

.build();

httpPost.setEntity(entity);

// 发起请求，并获取响应

try (CloseableHttpResponse response = httpClient.execute(httpPost)) {

26 / 67

System.out.println("Response Code: " + response.getStatusLine().getStat

民生银行银企直联

usCode());

HttpEntity responseEntity = response.getEntity();

if (responseEntity != null) {

System.out.println("Response Content: " + EntityUtils.toString(respons

eEntity));

}

}

}

}

2.3.文件下载(b2eDownload)

本部分更新日期:2025-02-06

一．接入方式：

使用文件上传下载功能时，调用地址是：

https://ip:port/custom/b2e/接口?sequence=${流水号}$。例如：

“ip:port”信息请参照下方“二.环境信息”

二．环境信息：

环境

域名

测试环境

https://obpgateway.cmbc.com.cn:10284/custom/b2e/*

生产环境

https://openapi.cmbc.com.cn: 2443/custom/b2e/*

三.接口说明：下载协议附件

四.单个文件下载，下载之后会弹出“另存为”的选择框

五.常见问题：

1).上传时文件安全校验不通过

27 / 67

请检查文件后缀和文件真实的格式是否一致，比如说把一张名为"test.jpg"的文件名修改

成"test.png"，会影响系统对文件格式的校验，可能导致异常。此外，还有一些调用方在上

传的时候，文件后缀缺失，也会报此异常。报此错误时，请仔细检查入参。

民生银行银企直联

2).错误码对应表

code

message

IGWUPF002

上传文件安全检查不通过

IGWUPF003

未找到有效文件

IGWUPF005

上传文件失败

IGWUPF006

读取文件失败

IGWUPF007

压缩文件中单个文件过大

IGWUPF008

压缩文件中存在目录

IGWUPF009

您还未登录或无权限

IGWUPF0010

从影响平台下载文件失败

IGWUPF0011

记录文件流水失败

IGWUPF0012

当前系统繁忙，请稍后再试

IGWUPF0013

不支持的文件类型

IGWUPF0014

文件大小超过允许的最大值

2.3.1.参数说明：

入参报文为 form-data 形式

出入参数

参数名

备注

参数

类型

是否必输

28 / 67

民生银行银企直联

入参

imageId

String 影像 ID(上传文件时返回的

imageId)

fileToken

String 必填(文件 token)

推荐每次下载文件前都获取新

Token

Y

Y

sceneId

场景编码(文件上传上送的场景

Y

编码以及融资文件查询返回的场

景编码一一对应)

出参

文件流

2.3.2.报文示例：

请求示例：

请求参数

请求参数 value

imageId

20240117150459039230089001703540016935500458187161

fileToken

P9ADCDX9H6CFQ7AHD64QP58CC7CDBB8T357893H3DDU149939D7UBB64E143J8MF

sceneId

B2eDraftProto

返回示例：

29 / 67

民生银行银企直联

2.3.3.文件下载代码示例 Demo

1.后台接口下载文件时，返回的数据报文头包含 Content-Type: application/x-downloa

d，若使用浏览器请求可通过浏览器下载，如调用方自行处理文件，可参考如下形式处理文

件流。

2.以下为简易的请求接口下载文件的示例 Demo，主要展示了请求接口成功获取文件流并写

入到本地的过程。

private static void downloadFile(String token) throws IOException {

// 设置请求地址

String url = "http://obpmapp.obp.uat.cmbc.cn:19011/custom/b2e/b2eDownload";

// 需要下载的文件 ID

String imageId = "20250410143017750**********0734006102920005389121

0";

// 对应文件的 sceneId

String sceneId = "B2eDraftProto";

// 通过 b2eGetToken 获取的 token,每次下载之前都重新获取

String fileToken = token;

// 下载文件保存的路径

String filePath = "Xxxx\\xxxx\\xxx.pdf";

try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

// 创建 HttpPost 对象，设置请求地址和请求参数等

HttpPost httpPost = new HttpPost(url);

HttpEntity entity = MultipartEntityBuilder.create()

.setMode(HttpMultipartMode.STRICT)

.addTextBody("sceneId", sceneId)

.addTextBody("fileToken", fileToken)

30 / 67

.addTextBody("imageId", imageId)

.build();

httpPost.setEntity(entity);

民生银行银企直联

// 发起请求，并获取响应(下载成功则接口会返回文件流)

try (CloseableHttpResponse response = httpClient.execute(httpPost)) {

System.out.println("Response Code: " + response.getStatusLine().getStat

usCode());

HttpEntity responseEntity = response.getEntity();

if (responseEntity != null) {

// 将响应文件流写入到本地文件

try (InputStream inputStream = responseEntity.getContent();

FileOutputStream fos = new FileOutputStream(filePath)) {

byte[] buffer = new byte[1024];

int bytesRead;

while ((bytesRead = inputStream.read(buffer)) != -1) {

fos.write(buffer, 0, bytesRead);

}

}

}

}

} catch (IOException e) {

e.printStackTrace();

}

}

3.通用查询

3.1.融资申请结果查询(B2eQueryBasicDraftResult)

本部分更新日期:2024-06-25

接口说明：融资申请结果查询。

该接口支持授权账户。

31 / 67

民生银行银企直联

是否

长度

必输

Y

N

N

N

N

64

16

32

10

10

N

BigDe

cimal(

16,2)

N

BigDe

cimal(

16,2)

N

2

3.1.1.请求(B2eQueryBasicDraftResult)

标记

说明

<xDataBody>

<trnId>

复

客户技术请求流水号，同一客户请勿重

<creditCode>

借据号/银承协议号

<applicationNo>

融资申请编号

<startDate>

出账申请起始日期格式 yyyy-MM-dd

<endDate>

出账申请终止日期格式 yyyy-MM-dd

<minAmount>

最小放款金额

<maxAmount>

最大放款金额

<assureWay>

担保方式

不输入查所有担保方式

00-无担保

01-100%保证金

02-理财产品质押

03-电子存单质押

04-票据池质押

<status>

融资申请状态

Y

2

A-所有

32 / 67

民生银行银企直联

S-审批通过

E-审批拒绝

R-审批中

<pageNo>

当前页码(从 1 开始，不传默认为 1)

每页数据条数(默认 10 条，最大每页 100

<pageSize>

<channel>

条)

渠道标识

02 企网；

06 银企直连；

N

N

N

10

6

2

不传默认查询企网+银企直连

出票账户

<issueAccountNo>

客户账户或其已获得授权的账户

N

40

不传默认查询本客户融资结果列表

</xDataBody>

3.1.2.响应(B2eQueryBasicDraftResult)

标记

说明

<xDataBody>

<trnId>

<svrId>

<total>

<List>

<Map>

客户端交易的唯一标志

银行渠道交易流水号

总数

融资申请列表

<customerNo>

<creditCode>

客户号

借据号/银承协议号

<outApplicationNo>

融资申请编号

长度

是否

必输

Y

Y

Y

N

Y

Y

Y

64

32

10

64

32

16

32

33 / 67

<loanAmt>

融资金额

币种，使用 ISO 标准货币符

号。如：CNY 表示人民币。

<currencyType>

<vaildFrom>

<validTo>

<assureWay>

<status>

出账起始日

出账到期日

担保方式

00-无担保

01-100%保证金

02-理财产品质押

03-电子存单质押

04-票据池质押

审批状态

S-审批通过

E-审批拒绝

R-审批中

<applyDt>

dd

申请日期，格式 yyyy-MM-

审批意见

渠道标识

02 民生网银

06 银企直连

<auditMessage>

<channel>

</Map>

</List>

民生银行银企直联

BigDeci

Y

mal

(16,2)

Y

Y

Y

10

10

10

Y

2

Y

1

10

200

Y

N

N

2

34 / 67

民生银行银企直联

</xDataBody>

3.1.3.报文示例：

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryBasicDraftResult">

<requestHeader>

<dtClient>2024-06-26 09:18:53</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202406260917422</trnId>

<creditCode>****************</creditCode>

<applicationNo>ELOAN202406251170647413</applicationNo>

<startDate></startDate>

<endDate></endDate>

<minAmount></minAmount>

<maxAmount></maxAmount>

<assureWay></assureWay>

<status>A</status>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<channel>02</channel>

<issueAccountNo>*********</issueAccountNo>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

35 / 67

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryBasicDraftResult">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-06-26 09:18:57</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>1</total>

<List>

<Map>

<customerNo>**********</customerNo>

<creditCode>****************</creditCode>

<outApplicationNo>ELOAN202406251170647413</outApplicationNo>

<loanAmt>4600.00</loanAmt>

<currencyType>RMB</currencyType>

<vaildFrom>2024-06-25</vaildFrom>

<validTo>2024-06-30</validTo>

<assureWay>04</assureWay>

<status>R</status>

<applyDt>2024-06-25</applyDt>

<auditMessage></auditMessage>

<channel>02</channel>

</Map>

</List>

<trnId>CMBCTRN202406260917422</trnId>

</xDataBody>

</CMBC>

36 / 67

3.2.融资文件查询(B2eQueryContractInfo)

民生银行银企直联

本部分更新日期:2024-06-25

接口说明：融资文件查询，出账成功下载带签名的协议附件

3.2.1.请求(B2eQueryContractInfo)

标记

说明

<xDataBody>

<trnId>

客户技术请求流水号，同一客户请勿重复

<creditCode>

借据号

<outApplication>

外部申请编号

</xDataBody>

3.2.2.响应(B2eQueryContractInfo)

标记

说明

<xDataBody>

<trnId>

<svrId>

<fileList>

<Map>

<fileName>

<imageId>

<sceneId>

客户端交易的唯一标志

银行渠道交易流水号

文件列表

文件名称

影像平台 ID

场景标识(用于文件下载使用)

文件类型

119 授信申请书

<fileType>

18 承兑协议

48 信息信息查询使用授权书

04 贸易合同附件

是否

长度

必输

Y

Y

Y

64

16

32

是否

长度

必输

Y

Y

N

Y

Y

Y

Y

64

32

255

32

32

4

37 / 67

标记

说明

民生银行银企直联

是否

长度

必输

136 审计报告附件

135 增值税纳税申报表附件

139 担保资金证明材料附件

140 其他材料附件

</Map>

</fileList>

</xDataBody>

3.2.3.报文示例：

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryContractInfo">

<requestHeader>

<dtClient>2023-09-14 18:36:04</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>*******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2024051311251891</trnId>

<creditCode>ZX24050000348375</creditCode>

<outApplication>ELOAN20240322007099456482</outApplication>

</xDataBody>

</CMBC>

响应报文：

38 / 67

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryContractInfo">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-06-25 17:22:42</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31300202406251388810579354000QUS</svrId>

<trnId>CMBCTRN2024051311251891</trnId>

<fileList>

<Map>

<fileName>********175104412**********07340093198200053672102.

pdf</fileName>

<imageId>********175104412**********07340093198200053672102<

/imageId>

<sceneId>B2eDraftProto</sceneId>

</Map>

<Map>

<fileName>********175104787**********07340076107200019360387.pdf<

/fileName>

<imageId>********175104787**********07340076107200019360387</ima

geId>

<sceneId>B2eDraftProto</sceneId>

</Map>

</fileList>

</xDataBody>

</CMBC>

39 / 67

3.3.开户行信息查询 (B2eNbsQueryBankInfo)

民生银行银企直联

本部分更新日期:2024-06-25

接口说明：

1.本接口用于查询开户行信息。

2.注意：如果要查询“中国民生银行北京亚运村支行”，可输入以下条件进行查询：1.民生银行

*亚运村；2.北京亚运村支行；3.民生*北京*亚运村。行别不能为简称，如：“工行*和平里”应

为“工商*和平里”。

3.3.1.请求(B2eNbsQueryBankInfo)

标记

说明

<xDataBody>

<trnId>

复

客户技术请求流水号，同一客户请勿重

<memberId>

业务办理渠道代码，码值见字典《业务

办理渠道代码》

是否返回大额支付系统机构名称，非比

输，

0 展示

1 不展示，

默认不展示

机构名称

支持模糊查询，*表示任意字符

行号

当前页码(从 1 开始，不传默认为 1)

每页数据条数(默认 10 条，最大每页

100 条)

<showBankName>

<brchName>

<bankNo>

<pageNo>

<pageSize>

</xDataBody>

是否

长度

必输

Y

N

64

32

N

2

Y

N

N

N

255

12

10

6

40 / 67

3.3.2.响应(B2eNbsQueryBankInfo)

标记

说明

<xDataBody>

民生银行银企直联

是 否
必输

长度

客户技术请求流水号，同一客

户请勿重复

银行渠道交易流水号

总条数

<trnId>

<svrId>

<total>

<List>

<Map>

<bankFullName>

机构全称

<memberId>

<bankCode>

<socCode>

业务办理渠道代码

机构参与者代码

统一社会信用代码

<bankNo>

行号

<bankName>

大额支付系统机构名称

<isAvailable>

是否可用

0 不可用

1 可用

Y

N

Y

Y

Y

Y

Y

Y

Y

N

N

</Map>

</List>

</xDataBody>

3.3.3.报文示例：

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsQueryBankInfo">

<requestHeader>

4

2

nt

55

8

2

55

6

3

2

6

9

1

1

2

2

41 / 67

<dtClient>2023-06-25 11:08:19</dtClient>

民生银行银企直联

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN20230612143835586</trnId>

<brchName>北

京</brchName>

<bankNo>************</bankNo>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eNbsQueryBankInfo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-06-25 11:08:20</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<total>1</total>

<List>

<Map>

42 / 67

<bankFullName>中国民生银行股份有限公司北京分行</bankFullName>

民生银行银企直联

<memberId>100009</memberId>

<bankCode>*********</bankCode>

<socCode>911101028019779621</socCode>

<bankNo>************</bankNo>

</Map>

</List>

<trnId>CMBCTRN20230612143835586</trnId>

</xDataBody>

</CMBC>

3.4.银承中收减免审批单号查询(B2eQueryTaskNo)

本部分更新日期:2024-06-25

接口说明：查询预融资银承中收减免审批单号

3.4.1.请求(B2eQueryTaskNo)

标记

说明

<xDataBody>

<trnId>

客户技术请求流水号，同一客户请勿重复

<financingAmt> 融资金额

审批单开始时间

格式：YYYY-MM-DD

审批单结束时间

格式：YYYY-MM-DD

<taskStartDate>

<taskEndDate>

</xDataBody>

3.4.2.响应(B2eQueryTaskNo)

是否

长度

必输

Y

Y

N

N

64

18,2

10

10

标记

说明

是 否 长度

43 / 67

民生银行银企直联

必输

Y

N

N

Y

N

4

2

2

0

客户端交易的唯一标志

银行渠道交易流水号

中收减免审批单号

申请编号

<xDataBody>

<trnId>

<svrId>

<List>

<Map>

<taskNo>

<serialCode>

</Map>

</List>

</xDataBody>

3.4.3.报文示例：

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryTaskNo">

<requestHeader>

<dtClient>2024-05-10 14:52:25</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN202405101413398</trnId>

<financingAmt>12345678901234</financingAmt>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

44 / 67

民生银行银企直联

trnCode="B2eQueryTaskNo">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-06-25 16:06:18</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>313002024062513888103823540001CS</svrId>

<List>

<Map>

<taskNo>44100202307070008923407</taskNo>

<serialCode>0000079240</serialCode>

</Map>

</List>

<trnId>CMBCTRN202405101413398</trnId>

</xDataBody>

</CMBC>

3.5.银承客户协议查询(B2eQueryCustAgreement)

本部分更新日期:2024-06-25

接口说明：查询预融资申请协议信息

3.5.1.请求(B2eQueryCustAgreement)

标记

说明

<xDataBody>

是 否

长度

必输

45 / 67

<trnId>

客户技术请求流水号，同一客户请勿重复

Y

64

民生银行银企直联

</xDataBody>

3.5.2.响应(B2eQueryCustAgreement)

标记

说明

是否

长度

必输

<xDataBody>

<trnId>

<svrId>

<branchNo>

<customerNo>

<idType>

<idCode>

<type>

客户端交易的唯一标志

银行渠道交易流水号

业务办理机构

客户号

证件类型

证件号码

已维护授信类型

01-综合授信

02-单笔授信(100%保证金)

03-单笔授信(票据管家额度)

04-全部

<comprehensiveCreditInfo> 综合授信信息

<creditAppCode>

<zhCode>

<grantRatioParam>

</comprehensiveCreditInfo>

<singleCreditInfo>

<managerPhone>

<loginName>

授信申请书号

综合授信合同号

保证金比例参数

单笔授信信息

客户经理手机号

客户经理员工号

<billManagerQuotaNo>

票据管家额度编号

</singleCreditInfo>

Y

N

Y

Y

Y

Y

Y

N

Y

Y

Y

N

Y

Y

Y

64

32

10

10

6

60

2

20

50

11

20

20

50

46 / 67

民生银行银企直联

<feeProduct>

<acptCostCOList>

<Map>

<feeProduct>

<feeRate>

</Map>

</acptCostCOList>

<bailAccountNoList>

<item>

</bailAccountNoLis>

<taskNo>

<settleAccountNoList>

收费品种

00 手续费；

01 敞口费；

02 手续费+敞口占用

收费列表

收费类型

费率

活期保证金账户列表

活期保证金账户

减免审批单号(客户经理维护的减免

审批单号)

结算账号列表(可用于作为还款账

户、扣款账户、手续费扣费账户)

<item>

结算账号

电票签约账号列表

电票签约账号

</settleAccountNoList>

<issueAccountNoList>

<item>

</issueAccountNoList>

</xDataBody>

3.5.3.报文示例：

请求报文：

2

2

16,8

Y

Y

Y

Y

N

N

N

Y

Y

Y

Y

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryCustAgreement">

<requestHeader>

<dtClient>2023-09-14 18:36:04</dtClient>

<clientId>**********</clientId>

47 / 67

民生银行银企直联

<userId>*************</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023090819143639</trnId>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryCustAgreement">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-06-25 16:54:46</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>31300202406251388810510354000GFL</svrId>

<acptCostCOList>

<Map>

<feeProduct>00</feeProduct>

<feeRate>0.05</feeRate>

</Map>

<Map>

<feeProduct>01</feeProduct>

<feeRate>0.05</feeRate>

</Map>

</acptCostCOList>

48 / 67

民生银行银企直联

<idType>ZC02</idType>

<idCode>G9K6ND0O-9</idCode>

<type>04</type>

<singleCreditInfo>

<managerPhone>***********</managerPhone>

<loginName>**********</loginName>

<billManagerQuotaNo>30032023050510575000</billManagerQuotaNo>

</singleCreditInfo>

<settleAccountNoList>

<item>*********</item>

<item>*********</item>

<item>*********</item>

<item>*********</item>

</settleAccountNoList>

<trnId>CMBCTRN2023090819143639</trnId>

<feeProduct>02</feeProduct>

<taskNo></taskNo>

<comprehensiveCreditInfo>

<creditAppCode>*****************</creditAppCode>

<zhCode>***************</zhCode>

<grantRatioParam>50.00</grantRatioParam>

</comprehensiveCreditInfo>

<issueAccountNoList>

<item>*********</item>

<item>*********</item>

</issueAccountNoList>

<bailAccountNoList>

<item>*********</item>

<item>*********</item>

<item>*********</item>

<item>*********</item>

</bailAccountNoList>

<customerNo>**********</customerNo>

<branchNo>0105</branchNo>

</xDataBody>

</CMBC>

49 / 67

3.6.融资申请信息查询(B2eQueryBasicDraftDetails)

民生银行银企直联

本部分更新日期:2024-11-13

1. 查询融资申请详情信息

2. 支持授权账户查询

3.6.1.请求(B2eQueryBasicDraftDetails)

标记

说明

<xDataBody>

<trnId>

客户技术请求流水号，同一客户请勿重复

<issueAccountNo> 出票账户

<creditCode>

一）

借据号/银承协议号（与融资申请编号必输其

<applicationNo>

</xDataBody>

融资申请编号（与借据号必输其一）

3.6.2.响应(B2eQueryBasicDraftDetails)

标记

说明

<xDataBody>

<trnId>

<svrId>

<loanApplyInfo>

<creditCode>

<customerName>

<loanType>

客户端交易的唯一标志

银行渠道交易流水号

融资信息

借据号

客户名称

出账品种

********0068 银行承兑汇票

是否

长度

必输

Y

N

N

N

64

50

16

32

是 否

长度

必输

Y

Y

Y

Y

N

Y

64

32

16

300

12

50 / 67

<financingAmount>

<duration>

<durationUnit>

出票金额

期限

期限单位

0-年

1-月

2-日

<settleAccountNo>

还款账号

<custManagerName>

客户经理名称

<custManagerPhone>

客户经理手机号

<custPhone>

</loanApplyInfo>

<acptInfo>

公司客户联系人手机号

银承信息

<issueAccountNo>

出票人账号（签约账号）

民生银行银企直联

Y

Y

Y

N

Y

Y

Y

Y

Y

18,2

20

20

50

200

20

20

50

<billClass>

票据形态

老票据：

1-纸票

2-电票

新票据：

纸票：ME01

电票：ME02

<grantRatioParam>

保证金比例参数

<isDeduct>

是否从结算账号中扣款

0-否，1-是

扣款账号

<deductAccountNo>

(如果 isDeduct 是 1，则必输)

</acptInfo>

<billInfoList>

<Map>

票据列表

<isAllowSplitBill>

是否自动分包 1-是；0-否

<autoType>

是否自动交票 1-是；0-否

Y

4

Y

N

N

N

N

N

11

1

50

1

1

51 / 67

<billClass>

<conferNo>

<billRemark>

<billNo>

<billRangeStart>

<billRangeEnd>

<remitDt>

<dueDt>

<billMoney>

票据形态

老票据：

1-纸票

2-电票

新票据：

纸票：ME01

电票：ME02

合同编号

备注信息

票号

子票区间起始

子票区间截止

出票日

到期日

票据金额

<banEndrsmtMark>

票面是否可转让标识

<drwrName>

1-是；

0-否

出票人

<drwrAcctNo>

出票人账号

<drwrBankName>

出票人开户行行名

<drwrBankNo>

出票人开户行行号

<acptName>

<acptAcctNo>

承兑人名称

承兑人账号

<acptBankName>

承兑人开户行行名

<acptBankNo>

承兑人开户行行号

<pyeeName>

<pyeeAcctNo>

收款人

收款人账号

<pyeeBankName>

收款人开户行行名

民生银行银企直联

N

4

N

N

N

N

N

N

N

N

N

N

N

N

N

N

N

N

N

N

N

N

300

200

30

12

12

10

10

18,2

1

200

50

200

20

200

50

200

20

200

50

200

52 / 67

<pyeeBankNo>

收款人开户行行号

<billStatus>

票据状态（非授权账户查询场景返回）

N

N

20

4

民生银行银企直联

已出票:CS01,

已承兑:CS02,

已收票:CS03,

已到期:CS04,

已终止:CS05,

已结清:CS06

<cirStatus>

流通状态（非授权账户查询场景返回）

N

20

TF0101：待收票,

TF0301：可流通,

TF0301_01：可流通-已录票,

TF0301_02：可流通-已贴现,

TF0301_03：可流通-存票,

TF0301_04：可流通-可贴现锁定,

TF0301_05：流通-流量票,

TF0301_06：可流通-已委托,

TF0301_07：可流通-待权属,

TF0301_08：可流通-承兑登记,

TF0302：已锁定,

TF0303：不可转让,

TF0304：已质押,

TF0304_01：已质押-可贴现锁票,

TF0304_02：已质押-预贴现锁定,

TF0305：待赎回,

TF0401：托收在途,

TF0401_01：托收在途（纸票）,

TF0401_03：托收在途-非拒付-追所有

人,

TF0401_04：托收在途-拒付-线上追偿,

TF0401_05：托收在途-拒付-线下追偿,

53 / 67

民生银行银企直联

TF0401_06：托收在途-拒付-追部分人,

TF0401_07：托收在途-拒付-追所有人

"),

TF0401_08：托收在途-拒付-线上或追

所有人,

TF0402：追索中,

TF0402_01：追索中-拒付-线上追偿,

TF0402_02：追索中-拒付-追所有人,

TF0402_03：追索中-拒付-线下追偿,

TF0402_04：追索中-拒付追索（中间状

态）,

TF0402_05：追索中-非拒付追索（中间

状态）,

TF0402_06：追索中-拒付-再追索,

TF0402_07：追索中-拒付-线下追偿发

起(纸票),

TF0402_08：追索中-拒付-线下追偿偿

付,

TF0402_09：追索中-非拒付-再追索,

TF0402_10：追索中-拒付-线下追偿中

（电票）,

TF0501：已结束

<payType>

付息方式（承贴直通车，必输）

N

1

1：买方付息；

2：卖方付息；

3：协议付息；

<payAcctNo>

付息账号（承贴直通车，必输）

<rate>

<interest>

</Map>

</billInfoList>

贴现利率（承贴直通车，必输）

贴现利息（承贴直通车，必输）

N

N

N

50

10

12

54 / 67

<bailInfoList>

<Map>

<accountType>

<bailAccountNo>

<currency>

<addAmount>

民生银行银企直联

保证金列表

账号类型

01-活期，02-定期

保证金账号

保证金币种

本次追加金额/开票添加保证金金额

出 账 或 开 证 保 证 金 金 额/ 本 次 追 加 金 额

（融资币种下的金额）

N

Y

Y

Y

Y

2

50

3

18,2

<loanCcyAddAmount>

开票保证金折算为融资币种汇率后的金

N

18,2

额

</Map>

</bailInfoList>

<counterPartyList>

支付信息列表

<Map>

<partyName>

<paymentAmount>

<payeeAccountNo>

<depositBankNo>

收款人

本次支付金额

收款账号

开户行行号

<depositBankName>

收款账号开户行

<tradeContractNo>

贸易合同号

<tradeContractAmount> 贸易合同金额

<availableAmount>

贸易合同余额

<isValid>

合同是否长期有效(0-否，1-是)

<tradeValidFrom>

贸易合同签署日 yyyy-MM-dd

<tradeValidTo>

贸易合同到期日 yyyy-MM-dd

</Map>

</counterPartyList>

<fileInfoList>

<Map>

<fileName>

文件信息列表

文件名称

N

N

N

N

N

N

N

N

N

N

N

N

Y

Y

200

18,2

50

20

200

300

18,2

18,2

1

10

10

255

55 / 67

民生银行银企直联

文件类型

04-贸易合同附件

139-担保资金证明材料附件

140-其他材料附件

136-审计报告附件

135-增值税纳税申报表附件

<fileType>

48-《信用信息查询使用授权书》

Y

4

18-《承兑协议》

119-《授信申请书》（100%保证金、

电子存单质押低风险现金担保流程）

35-《质押合同》

134-《银行承兑汇票信息表》（出账成

功状态数据返回）

影像 ID

场景标识（用于文件下载使用）

贴现信息（承贴直通车必输）

贴现日期(yyyy-MM-dd)

贴现行行名

付息金额合计

收费信息列表

收费品种

00-手续费

01-敞口占用费

扣费账号

收费金额

Y

N

N

N

N

N

Y

Y

Y

Y

32

32

10

150

15,2

2

50

18,2

56 / 67

<imageId>

<sceneId>

</Map>

</fileInfoList>

<discInfo>

<discDt>

<discBankName>

<sumInterest>

</discInfo>

<feeInfoList>

<Map>

<feeProduct>

<feeAccountNo>

<feeAmount>

</Map>

</feeInfoList>

</xDataBody>

3.6.3.报文示例：

请求报文：

民生银行银企直联

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs" trnCode=

"B2eQueryBasicDraftDetails">

<requestHeader>

<dtClient>2024-11-13 19:20:38</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2024111311215913</trnId>

<issueAccountNo>*********</issueAccountNo>

<creditCode>****************</creditCode>

<applicationNo>ELOAN********091650362664</applicationNo>

</xDataBody>

</CMBC>

响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs" trnCode="B2e

QueryBasicDraftDetails">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2024-11-13 19:21:41</dtServer>

57 / 67

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<svrId>T7700202411131921192030354063696</svrId>

<trnId>CMBCTRN2024111311215922</trnId>

<loanApplyInfo>

<creditCode>****************</creditCode>

<customerName>飞承贴通银承</customerName>

<loanType>********0068</loanType>

<financingAmount>220.00</financingAmount>

<duration>3</duration>

<durationUnit>1</durationUnit>

<settleAccountNo>*********</settleAccountNo>

<custManagerName>宋丽梅</custManagerName>

<custManagerPhone></custManagerPhone>

<custPhone>***********</custPhone>

</loanApplyInfo>

<discInfo>

<discDt></discDt>

<discBankName></discBankName>

<sumInterest>0</sumInterest>

</discInfo>

<counterPartyList>

<Map>

<partyName>李杰拜企业公司</partyName>

58 / 67

民生银行银企直联

<paymentAmount>220.00</paymentAmount>

<payeeAccountNo>*********</payeeAccountNo>

<depositBankNo>************</depositBankNo>

<depositBankName>中国民生银行股份有限公司北京中关村支行

</depositBankName>

<tradeContractNo>*********</tradeContractNo>

<tradeContractAmount>************.00</tradeContractAmount>

<availableAmount>************.00</availableAmount>

<isValid>1</isValid>

<tradeValidFrom>2024-01-16</tradeValidFrom>

<tradeValidTo></tradeValidTo>

</Map>

</counterPartyList>

<bailInfoList>

<Map>

<accountType>01</accountType>

<bailAccountNo>*********</bailAccountNo>

<currency>RMB</currency>

<addAmount>220.00</addAmount>

</Map>

</bailInfoList>

<billInfoList>

<Map>

<isAllowSplitBill>1</isAllowSplitBill>

<autoType>1</autoType>

<billClass>ME02</billClass>

<conferNo>*********</conferNo>

<billRemark></billRemark>

59 / 67

<billNo>5********************001552107</billNo>

民生银行银企直联

<billRangeStart>1</billRangeStart>

<billRangeEnd>22000</billRangeEnd>

<remitDt>********</remitDt>

<dueDt>********</dueDt>

<billMoney>220.00</billMoney>

<banEndrsmtMark>1</banEndrsmtMark>

<drwrName>飞承贴通银承</drwrName>

<drwrAcctNo>*********</drwrAcctNo>

<drwrBankName>中国民生银行股份有限公司北京中关村支行

</drwrBankName>

<drwrBankNo>************</drwrBankNo>

<acptName>中国民生银行股份有限公司北京分行</acptName>

<acptAcctNo>0</acptAcctNo>

<acptBankName>中国民生银行股份有限公司北京分行</acptBankName>

<pyeeName>李杰拜企业公司</pyeeName>

<pyeeAcctNo>*********</pyeeAcctNo>

<pyeeBankName>中国民生银行股份有限公司北京中关村支行

</pyeeBankName>

<pyeeBankNo>************</pyeeBankNo>

<billStatus></billStatus>

<cirStatus></cirStatus>

<payType></payType>

<payAcctNo></payAcctNo>

<rate></rate>

<interest></interest>

<acptBankNo>************</acptBankNo>

60 / 67

民生银行银企直联

</Map>

</billInfoList>

<feeInfoList>

<Map>

<feeProduct>00</feeProduct>

<feeAccountNo>*********</feeAccountNo>

<feeAmount>0.11</feeAmount>

</Map>

</feeInfoList>

<fileInfoList>

<Map>

<fileName>91800********0947072484918495811.pdf</fileName>

<fileType>134</fileType>

<imageId>********09450938623020785190734007091020005341404

4</imageId>

<sceneId>B2eDraftProto</sceneId>

</Map>

<Map>

<fileName>91800********0916532092918494915.pdf</fileName>

<fileType>18</fileType>

<imageId>********09183285223020785190003006682220005356305

1</imageId>

<sceneId>B2eDraftProtoSign</sceneId>

</Map>

<Map>

<fileName>91800********0916542095918494918.pdf</fileName>

<fileType>48</fileType>

61 / 67

<imageId>********09163405523020785190003009856920001962561

民生银行银企直联

1</imageId>

<sceneId>B2eDraftProtoSign</sceneId>

</Map>

</fileInfoList>

<acptInfo>

<issueAccountNo>*********</issueAccountNo>

<billClass>ME02</billClass>

<grantRatioParam>100.********</grantRatioParam>

<isDeduct>0</isDeduct>

<deductAccountNo></deductAccountNo>

</acptInfo>

</xDataBody>

</CMBC>

62 / 67

