银企直联接口文档

（财富管理-大额存单）

邮箱：<EMAIL>

版本

日期

说明

编写者 审核者

文档修改记录

民生银行银企直联

V1.0.0 2021-03-

定义接口文档

18

V1.2.0 2024-12-

自有账户/授权账户持有大额存单查询

26

(QryGroupFincDtl)支持授权账户及本

企业大额存单查询

1 / 27

目录

民生银行银企直联

目录 .............................................................................................2

1. 大额存单产品列表查询(B2EQUERYDEPOSITLIST) ................................. 4

1.1. 请求(B2EQUERYDEPOSITLIST) ......................................................4

1.2. 响应(B2EQUERYDEPOSITLIST) ......................................................5

1.3. 例子 ...................................................................................... 6

2. 大额存单实时购买(B2EDEPOSITREALTIMEBUY) .................................. 8

2.1. 请求(B2EDEPOSITREALTIMEBUY) .................................................. 8

2.2. 响应(B2EDEPOSITREALTIMEBUY) .................................................. 9

2.3. 例子 .................................................................................... 10

3. 银企直连产品说明书查询(B2EQUERYCDPRDMANUAL) .........................11

3.1. 请求(B2EQUERYCDPRDMANUAL) ................................................ 11

3.2. 响应(B2EQUERYCDPRDMANUAL) ................................................ 12

3.3. 例子 .................................................................................... 12

4. 持有大额存单查询(QRYFINCDTL) ..................................................... 14

4.1. 请求(QRYFINCDTL) ..................................................................14

4.2. 响应(QRYFINCDTL) ..................................................................14

4.3. 例子 .................................................................................... 16

5. 自有账户/授权账户持有大额存单查询(QRYGROUPFINCDTL) .................... 18

5.1. 请求(QRYGROUPFINCDTL) .........................................................18

5.2. 响应(QRYGROUPFINCDTL) .........................................................19

5.3. 例子 .................................................................................... 21

6. 大额存单提前支取(B2EDEPOSITREALTIMESELL) ............................... 23

6.1. 请求(B2EDEPOSITREALTIMESELL) ................................................23

6.2. 响应(B2EDEPOSITREALTIMESELL) ................................................24

2 / 27

民生银行银企直联
6.3. 例子 .................................................................................... 24

3 / 27

1. 大额存单产品列表查询(B2eQueryDepositList)

民生银行银企直联

本部分更新日期:2021-10-15

大额存单产品列表查询。

1.1. 请求(B2eQueryDepositList)

  标记

说明

长度

<xDataBody>

 <trnId>

客户端交易的唯一标志（必输，但无作用）（★）

64

 <pageNo>

页码（★）

 <pageSize>

页面容量（★）允许查询范围：1-50。

 <firstAmtFlag>

起存金额

0:全部,

1：1 千万-5 千万,

2：5 千万以上。

未传默认为全部。

 <liveTime>

存期

0:全部、

1:1 个月、

2:3 个月、

3:6 个月、

4:9 个月、

5:1 年、

6:18 个月、

7:2 年、

8:3 年、

4 / 27

9:5 年。

未传默认为全部。

 <currType>

币种 "156：人民币"。

 <incomeRateFlag> 到期利率

0:全部,

1：2%以下,

2：2%-4%,

3：4%-8%。

未传默认为全部。

</xDataBody>

1.2. 响应(B2eQueryDepositList)

  标记

说明

<xDataBody>

 <trnId>

 <Insid>

 <cltcookie>

客户端交易的唯一标志

服务器该笔交易的标识

 <totalSize>

总条数

 <list>

   <prdCode>

产品代码

   <prdName>

产品名称

   <totAmt>

发行额度

   <startDate>

发行起始日

民生银行银企直联

长度

64

5 / 27

民生银行银企直联

   <endDate>

发行截止日

   <incomeRate>

到期利率

   <liveTimeUnitName> 存期（产品名义周期）(年月日：如：31

天、3 月、1 年)

   <liveTime>

存期（产品实际期限）(天：31、93、

   <currType>

365)

币种

   <firstAmt>

起存金额

   <osubUnit>

机构最小购买单位

 <list>

</xDataBody>

1.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryDepositList">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

6 / 27

民生银行银企直联

<pageNo>1</pageNo>

<pageSize>5</pageSize>

<firstAmtFlag>5000</firstAmtFlag>

<liveTime>1</liveTime>

<currType></currType>

<incomeRateFlag>0.05</incomeRateFlag>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQueryDepositList"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<totalSize>74</totalSize>

<list>

<item>{firstAmt=0.00, endDate=20991231, prdName=单位 2 天自动续存产品,

osubUnit=0, currType=156, liveTime=29514, totAmt=1100000000.00,

incomeRate=0.0043750, prdCode=FGG1902D02, startDate=20190312,

liveTimeUnitName=2 天}</item>

<item>{firstAmt=1000000.00, endDate=20250905, prdName=对公-按季付

息-002, osubUnit=1000000, currType=156, liveTime=1827,

7 / 27

民生银行银企直联

totAmt=9000000000.00, incomeRate=0.0600000, prdCode=FGG0904002,

startDate=20200904, liveTimeUnitName=2 年}</item>

<item>{firstAmt=0.00, endDate=20400907, prdName=FGG0908002,

osubUnit=0, currType=156, liveTime=7304, totAmt=900000000.00,

incomeRate=0.1000000, prdCode=FGG0908002, startDate=20200908,

liveTimeUnitName=5 年}</item>

<item>{firstAmt=0.00, endDate=20250908, prdName=FGG0908003,

osubUnit=0, currType=156, liveTime=1826, totAmt=2780000000.00,

incomeRate=0.1000000, prdCode=FGG0908003, startDate=20200908,

liveTimeUnitName=5 年}</item>

<item>{firstAmt=10000000.00, endDate=20991231, prdName=2 天提支靠

挂大额存单产品, osubUnit=0, currType=156, liveTime=29626,

totAmt=10000000000.00, incomeRate=0.0512340, prdCode=FGG1802002,

startDate=20181120, liveTimeUnitName=2 天}</item>

</list>

</xDataBody>

</CMBC>

2. 大额存单实时购买(B2eDepositRealtimeBuy)

本部分更新日期:2021-10-15

大额存单实时购买。

2.1. 请求(B2eDepositRealtimeBuy)

标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <insId>

客户端交易的唯一标志

 <cltcookie>

长度

64

64

8 / 27

民生银行银企直联

 <prdCode>

产品代码（★）

 <transAccount> 购买帐号（★）

 <transAmount> 购买金额（★）

 <readFlag>

是否已阅读产品说明书（★） 1 已阅读 0 未阅读,传 0

时，提示请先阅读产品说明书

</xDataBody>

2.2. 响应(B2eDepositRealtimeBuy)

标记

说明

<xDataBody>

服务消息集

 <trnId>

客户端交易的唯一标志

 <insId>

客户端交易的唯一标志

 <cltcookie>

 <prdName>

产品名称

 <prdCode>

产品代码

 <cdDmAccount> 大额存单帐号

 <currType>

币种

 <startDate>

大额存单起息日

 <endDate>

大额存单到期日

</xDataBody>

长度

64

64

9 / 27

民生银行银企直联

2.3. 例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eDepositRealtimeBuy">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<prdCode>FGG0908003</prdCode>

<transAccount>*********</transAccount>

<transAmount>60000</transAmount>

<readFlag>1</readFlag>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eDepositRealtimeBuy"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

10 / 27

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

民生银行银企直联

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<prdName>FGG0908003</prdName>

<currType>156</currType>

<prdCode>FGG0908003</prdCode>

<cdDmAccount>*********</cdDmAccount>

<startDate>********</startDate>

</xDataBody>

</CMBC>

3. 银企直连产品说明书查询(B2eQueryCdPrdManua

l)

本部分更新日期:2021-10-15

银企直连产品说明书查询。

3.1. 请求(B2eQueryCdPrdManual)

标记

说明

<xDataBody>

 <trnId>

客户端的唯一标识（★）

 <cltcookie>

 <prdCode>

产品代码（★）

</xDataBody>

长度

64

11 / 27

3.2. 响应(B2eQueryCdPrdManual)

民生银行银企直联

长度

64

标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端的唯一标识

 <cltcookie>

 <protocol>

产品协议,取 tss 后台设置

  <fileTitle> 文件标题

  <fileType> 文件类型,0:产品说明书,1:产品合约,2:风险揭示书

  <filePathl> 文件下载地址

</xDataBody>

3.3. 例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryCdPrdManual">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

12 / 27

民生银行银企直联

<cltcookie>123</cltcookie>

<prdCode>FGG0908003</prdCode>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQueryCdPrdManual"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<protocol>

<item>{fileTitle=产品说明书, fileType=0,

filePath=http://197.3.155.144:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FS

DE00001B_0_20201218.pdf, controlFlag=000}</item>

<item>{fileTitle=产品合约, fileType=1,

filePath=http://197.3.155.144:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FS

DE00001B_1_20201218.pdf, controlFlag=000}</item>

<item>{fileTitle=风险揭示书, fileType=2,

filePath=http://197.3.155.144:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FS

DE00001B_2_20201218.pdf, controlFlag=000}</item>

</protocol>

</xDataBody>

</CMBC>

13 / 27

4. 持有大额存单查询(QryFincDtl)

民生银行银企直联

本部分更新日期:2024-11-25

用于客户查询购买的大额存单产品，含未到期、已冻结和全部产品。

4.1. 请求(QryFincDtl)

  标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <acNo>

查询账号(不输时，返回客户下持有的所有大额存单)

 <currentIndex> 起始笔数（★）

 <pageSize>

查询笔数（★）

</xDataBody>

4.2. 响应(QryFincDtl)

  标记

说明

<xDataBody>

 <trnId>

 <srvId>

客户端交易的唯一标志（★）

服务器该笔交易的标识

 <allNum>

总条数

 <totalNum>

当前记录数

 <List>

长度

64

长度

64

64

14 / 27

民生银行银企直联

  <Map>

   <TransDate>

份额申请日期(yyyyMMdd)

<CfmDate>

份额登记日期(yyyyMMdd)

   <PrdCode>

产品代码

   <Prdname>

产品名称

   <CurrType>

币种

   <CurrTypeName>

币种名称

   <Vol>

理财产品份额

   <UseVol>

可用份额

   <TradeFrozen>

交易冻结份额

   <OtherFrozen>

长期冻结份额

   <DivMode>

分红方式

   <DivModename>

分红方式名称

   <Cost>

买入成本

   <IncomeRate>

到期利率

   <TotIncome>

累计收益

   <TotPrinciple>

累计赎回本金

   <Income>

预期未兑付收益

   <OnwayAmt>

在途资金

   <BankAcc>

银行账号(份额所属账号)

15 / 27

   
   <CashFlag>

钞汇标识(0-钞；1-汇)

民生银行银企直联

   <NAV>

产品净值

   <CdDmAccount>

大额存单账号

   <ParentContractNo> 主合约编号

   <ContractNo>

合约编号

   <StartDate>

大额存单起息日

   <EndDate>

大额存单到息日

   <FincStatu>

状态(0-已销户,1-未到期)

  </xDataBody>

4.3. 例子

请求报文

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryFincDtl">

<requestHeader>

<dtClient>2015-01-08 10:46:20</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<cltcookie></cltcookie>

16 / 27

民生银行银企直联

<acNo>*********</acNo>

<currentIndex>1</currentIndex>

<pageSize>1</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryFincDtl" security="none" lang="chs" header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-10-11 15:35:37</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<svrId></svrId>

<allNum>2</allNum>

<totalNum>1</totalNum>

<List>

<Map>

<TransDate>********</TransDate>

<CfmDate>********</CfmDate>

<PrdCode>FGGA160007</PrdCode>

<Prdname></Prdname>

<CurrType>CNY</CurrType>

<CurrTypeName>人民币</CurrTypeName>

<Vol>10000.00</Vol>

<UseVol>10000.00</UseVol>

<TradeFrozen>0.00</TradeFrozen>

<OtherFrozen>0.00</OtherFrozen>

17 / 27

民生银行银企直联

<DivMode>1</DivMode>

<DivModename></DivModename>

<Cost>10000.00</Cost>

<IncomeRate>0.00</IncomeRate>

<TotIncome>0.00</TotIncome>

<TotPrinciple>0.00</TotPrinciple>

<Income>0.00</Income>

<OnwayAmt>0.00</OnwayAmt>

<BankAcc>*********</BankAcc>

<CashFlag>0</CashFlag>

<NAV>1.0000</NAV>

<CdDmAccount>*********</CdDmAccount>

<ParentContractNo></ParentContractNo>

<ContractNo>54000********0000000069</ContractNo>

<StartDate>********</StartDate>

<EndDate>********</EndDate>

<FincStatu>1</FincStatu>

</Map>

</List>

</xDataBody>

</CMBC>

5. 自有账户/授权账户持有大额存单查询(QryGroupFin

cDtl)

本部分更新日期:2024-12-26

支持查询自有活期账户及授权账户中的所有持有大额存单，包括未到期、已冻结和全部产
品。

5.1. 请求(QryGroupFincDtl)

  标记

说明

<xDataBody>

长度

18 / 27

 <trnId>

客户端产生的交易唯一标志（★）

64

民生银行银企直联

 <acNo>

查询账号(支持查询本企业及授权账户大额存单信息)

（★）

 上送企业活期账户时，返回该账户对应的大额存单

信息。

 上送客户号时，返回该客户号下所有账户大额存单

信息

 <currentIndex> 起始笔数（★）

 <pageSize>

查询笔数，最大查询笔数为 20（★）

</xDataBody>

5.2. 响应(QryGroupFincDtl)

  标记

说明

长度

<xDataBody>

 <trnId>

 <srvId>

客户端交易的唯一标志（★）

64

服务器该笔交易的标识

 <allNum>

总条数

 <totalNum>

当前记录数

 <List>

  <Map>

   <TransDate>

份额申请日期(yyyyMMdd)

   <CfmDate>

份额登记日期(yyyyMMdd)

19 / 27

   <PrdCode>

产品代码

   <Prdname>

产品名称

   <CurrType>

币种

   <CurrTypeName>

币种名称

   <Vol>

理财产品份额

   <UseVol>

可用份额

   <TradeFrozen>

交易冻结份额

   <OtherFrozen>

长期冻结份额

   <DivMode>

分红方式

   <DivModename>

分红方式名称

   <Cost>

买入成本

   <IncomeRate>

到期利率

   <TotIncome>

累计收益

   <TotPrinciple>

累计赎回本金

   <Income>

预期未兑付收益

   <IncomeRate>

浮动盈亏

   <OnwayAmt>

在途资金

   <BankAcc>

银行账号(份额所属账号)

   <CashFlag>

钞汇标识(0-钞；1-汇)

   <NAV>

产品净值

民生银行银企直联

20 / 27

民生银行银企直联

   <CdDmAccount>

大额存单账号

   <ParentContractNo> 主合约编号

   <ContractNo>

合约编号

   <StartDate>

大额存单起息日

   <EndDate>

大额存单到息日

   <FincStatu>

状态(0-已销户,1-未到期)

  </xDataBody>

5.3. 例子

请求报文

<?xml version="1.0" encoding="gb2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryGroupFincDtl">

<requestHeader>

<dtClient>2015-01-08 10:46:20</dtClient>

<clientId>**********</clientId>

<userId>**********001</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<cltcookie></cltcookie>

<acNo>*********</acNo>

<currentIndex>1</currentIndex>

<pageSize>1</pageSize>

21 / 27

民生银行银企直联

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryGroupFincDtl" security="none" lang="chs"

header="100"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2016-10-14 15:20:58</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<svrId></svrId>

<allNum>2</allNum>

<totalNum>1</totalNum>

<List>

<Map>

<TransDate>********</TransDate>

<CfmDate>********</CfmDate>

<PrdCode>FGGA161004</PrdCode>

<Prdname></Prdname>

<CurrType>CNY</CurrType>

<CurrTypeName>人民币</CurrTypeName>

<Vol>10000.00</Vol>

<UseVol>10000.00</UseVol>

<TradeFrozen>0.00</TradeFrozen>

<OtherFrozen>0.00</OtherFrozen>

<DivMode>1</DivMode>

<DivModename></DivModename>

22 / 27

民生银行银企直联

<Cost>10000.00</Cost>

<IncomeRate>0.00</IncomeRate>

<TotIncome>0.00</TotIncome>

<TotPrinciple>0.00</TotPrinciple>

<Income>0.00</Income>

<IncomeRate>0.030100</IncomeRate>

<OnwayAmt>0.00</OnwayAmt>

<BankAcc>*********</BankAcc>

<CashFlag>0</CashFlag>

<NAV>1.0000</NAV>

<CdDmAccount>*********</CdDmAccount>

<ParentContractNo>54000********0000000067</ParentContractNo>

<ContractNo>54000********0000000067</ContractNo>

<StartDate>********</StartDate>

<EndDate>********</EndDate>

<FincStatu>1</FincStatu>

</Map>

</List>

</xDataBody>

</CMBC>

6. 大额存单提前支取(B2eDepositRealtimeSell)

本部分更新日期:2021-10-15

大额存单提前支取。

6.1. 请求(B2eDepositRealtimeSell)

标记

说明

<xDataBody>

 <trnId>

客户端产生的交易唯一标志（★）

 <insId>

客户端交易的唯一标志

长度

64

64

23 / 27

民生银行银企直联

 <cltcookie>

 <transAccount> 购买帐号（★）

 <transAmount> 提支份额（★）

 <cdDmAccount> 大额存单账号（★）

</xDataBody>

6.2. 响应(B2eDepositRealtimeSell)

标记

说明

<xDataBody> 服务消息集

 <trnId>

客户端产生的交易唯一标志

 <insId>

客户端交易的唯一标志

长度

64

64

 <cltcookie>

 <prdCode>

产品代码

 <prdName> 产品名称

</xDataBody>

6.3. 例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eDepositRealtimeSell">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

24 / 27

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<transAccount>*********</transAccount>

<transAmount>50000</transAmount>

<cdDmAccount>0</cdDmAccount>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eDepositRealtimeSell"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

民生银行银企直联

25 / 27

<prdCode>50000</prdCode>

<prdName>50000</prdName>

</xDataBody>

</CMBC>

民生银行银企直联

26 / 27

