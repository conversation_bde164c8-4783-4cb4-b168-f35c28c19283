银企直联接口文档

（银企直联数字人民币）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 44

目录

民生银行银企直联

目录 .............................................................................................2

1. 对公钱包列表查询(B2EWALLETLISTSQUERYTRANS) ........................... 4

1.1. 请求(B2EWALLETLISTSQUERYTRANS) ............................................ 4

1.2. 响应(B2EWALLETLISTSQUERYTRANS) ............................................ 4

1.3. 例子 ...................................................................................... 5

2. 工行钱包详情查询(B2EICBCWALLETINFOQUERYTRANS) ..................... 7

2.1. 请求(B2EICBCWALLETINFOQUERYTRANS) ...................................... 7

2.2. 响应(B2EICBCWALLETINFOQUERYTRANS) ...................................... 8

2.3. 例子 .................................................................................... 10

3. 建行钱包详情查询(B2ECCBWALLETINFOQUERYTRANS) .....................12

3.1. 请求(B2ECCBWALLETINFOQUERYTRANS) ......................................12

3.2. 响应(B2ECCBWALLETINFOQUERYTRANS) ......................................13

3.3. 例子 .................................................................................... 15

4. 对公钱包交易明细查询(B2EWLTTRANSDETAILQRY) ........................... 17

4.1. 请求(B2EWLTTRANSDETAILQRY) ................................................ 17

4.2. 响应(B2EWLTTRANSDETAILQRY) ................................................ 18

4.3. 例子 .................................................................................... 20

5. 对公钱包绑定/解绑对公账户(我行)(B2EWLTBINDCARDTRANSFER) .........23

5.1. 请求(B2EWLTBINDCARDTRANSFER) .............................................23

5.2. 响应(B2EWLTBINDCARDTRANSFER) .............................................23

5.3. 例子 .................................................................................... 24

6. 对公钱包已绑定、已解绑列表查询（应答运营机构）(B2EWLTRESBINDQRY) 26

6.1. 请求(B2EWLTRESBINDQRY) ...................................................... 26

6.2. 响应(B2EWLTRESBINDQRY) ...................................................... 26

2 / 44

民生银行银企直联
6.3. 例子 .................................................................................... 27

7. 对公账户兑出到数字人民币钱包(B2EPAYINWLTTRANS) ........................ 29

7.1. 请求(B2EPAYINWLTTRANS) .......................................................29

7.2. 响应(B2EPAYINWLTTRANS) .......................................................30

7.3. 例子 .................................................................................... 32

8. 数字钱包兑回到银行账户(B2EPAYFORWLTTRANS) ............................. 33

8.1. 请求(B2EPAYFORWLTTRANS) .....................................................33

8.2. 响应(B2EPAYFORWLTTRANS) .....................................................34

8.3. 例子 .................................................................................... 36

9. 对公钱包转账(钱包转钱包)(B2EWLTTOWLTTRANSFER) ....................... 38

9.1. 请求(B2EWLTTOWLTTRANSFER) .................................................38

9.2. 响应(B2EWLTTOWLTTRANSFER) .................................................41

9.3. 例子 .................................................................................... 42

3 / 44

1.对公钱包列表查询(B2eWalletListsQueryTrans)

民生银行银企直联

本部分更新日期:2023-12-28

1.1.请求(B2eWalletListsQueryTrans)

  标记

是否非空 说明

长度

<xDataBody>

  <trnId>

N

交易唯一标识（字母或数字）

64

</xDataBody>

1.2.响应(B2eWalletListsQueryTrans)

  标记

是否

说明

非空

长度

<xDataBody>

服务消息集

  <walletList>

钱包列表 list

   <WalletId>

钱包 ID

   <WalletName>

钱包昵称

   <WltLvl>

钱包等级（详见 数据字典-钱包等级）

   <WltSts>

钱包账户状态（详见 数据字典-钱包状

   <balance>

态）

余额

   <WltAvlBal>

钱包可用余额

4 / 44

  标记

是否

说明

非空

民生银行银企直联

长度

   <flag>

账号类型（1 未经合作方平台实名认证）

   <MSApply>

民生开立标志（KL 代表民生开立）

   <fctvDt>

授权开始日期

   <orgNo>

钱包机构号： C1010211000012-工行

机构号 C1010511003703-建行机构号

   <idNo>

对公客户证件号码

   <certType>

对公客户证件类型（详见 数据字典-证件

类型）

   <idtypeName>

对公客户证件类型中文名

   <idtype>

对公客户证件类型（详见 数据字典-证件

类型）

   <CorprtnNm>

对公客户姓名

   <RspbPsnNm>

经办人名称

   <RspbPsnIDNo>

经办人证件号码

   <RspbPsnTel>

经办人手机号码

   <ContractSign>

母子钱包签约标志 1-签约

  </walletList>

</xDataBody>

1.3.例子

请求报文

5 / 44

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="B2eWalletListQueryTrans">

<requestHeader>

<dtClient>2023-08-18 10:17:49</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody></xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWalletListQueryTrans">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-18 10:18:00</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<walletList>

<item>

<WalletId>0052************</WalletId>

<WalletName>******公司</WalletName>

<WltLvl>WL01</WltLvl>

<WltSts>03</WltSts>

<balance>1901765.60</balance>

6 / 44

民生银行银企直联

<WltAvlBal>1901765.60</WltAvlBal>

<flag></flag>

<MSApply>KL</MSApply>

<fctvDt>20230614165406</fctvDt>

<orgNo>C1010511003703</orgNo>

<idNo>AT*****L-1</idNo>

<certType>IT11</certType>

<idtypeName>营业执照</idtypeName>

<idtype>IT11</idtype>

<CorprtnNm>******公司</CorprtnNm>

<RspbPsnNm>******公司</RspbPsnNm>

<RspbPsnIDNo>11000******6535</RspbPsnIDNo>

<RspbPsnTel>173***69888</RspbPsnTel>

<ContractSign>1</ContractSign>

</item>

</walletList>

</xDataBody>

</CMBC>

2.工行钱包详情查询(B2eICBCWalletInfoQueryTra

ns)

本部分更新日期:2023-12-28

2.1.请求(B2eICBCWalletInfoQueryTrans)

  标记

是否必输 说明

<xDataBody>

 <WltId>

 <trnId>

Y

N

</xDataBody>

钱包 ID

交易唯一标识（字母或数字）

长度

16

64

7 / 44

2.2.响应(B2eICBCWalletInfoQueryTrans)

民生银行银企直联

  标记

说明

是否

必输

<xDataBody>

服务消息集

  <wltInfoMap>

钱包基本信息集 Map

   <CorprtnCustNm>

对公客户名称

   <CorprtnIDNo>

对公客户证件号码

   <WltNm>

   <WltId>

   <wltCnclDt>

   <WltIssDt>

钱包昵称

钱包 ID

销户日期

开户日期

   <WltTp>

钱包类型（详见 数据字典-钱包

类型）

长度

64

64

64

64

64

64

64

   <CorprtnIDTp>

对公客户证件类型（详见 数据字

64

典-对公客户证件类型）

   <WltLvl>

钱包等级（详见 数据字典-钱包

64

等级）

   <WltSts>

钱包状态（详见 数据字典-钱包

64

状态）

  </wltInfoMap>

钱包基本信息集 Map

  <amountMap>

钱包金额结果集 Map

   <PerPyQot>

单笔转出限额

21

8 / 44

  标记

说明

是否

必输

   <WltAvlBal>

钱包可用余额

   <DayPyQot>

日支付限额

   <YrPyQot>

年累计支付限额

   <WltBalLmt>

钱包余额上限

民生银行银企直联

长度

21

21

21

21

  </amountMap>

钱包金额结果集 Map

  <SgnAcctList>

钱包已绑定账户列表 List

   <CorprtnIDTp>

对公客户证件类型（详见 数据字

典-对公客户证件类型）

   <LglRepNm>

法定代表人姓名

   <availableAmount>

可用余额

   <bindDateTime>

绑定日期时间

   <CorprtnIDNo>

对公客户证件号码

   <SgnAcctTp>

账户类型（详见 数据字典-账户

类型）

   <SgnAcctId>

绑定对公账户

   <CorprtnNm>

对公客户名称

   <sgnAcctNm>

对公账户名称

  <\SgnAcctList>

钱包已绑定账户列表 List

  <LglRepNm>

法人姓名

64

9 / 44

  标记

说明

是否

必输

  <RspbPsnNm>

对公客户名称

民生银行银企直联

长度

64

</xDataBody>

2.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eICBCWalletInfoQueryTrans">

<requestHeader>

<dtClient>2023-08-18 11:02:30</dtClient>

<clientId>2301******</clientId>

<userId>2301******001</userId>

<userPswd>*******</userPswd>

<language>chs</language>

<appId>ent</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<WltId>0022************</WltId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eICBCWalletInfoQueryTrans">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

10 / 44

<dtServer>2023-08-18 11:02:35</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<LglRepNm>邓**</LglRepNm>

<RspbPsnNm>*****公司</RspbPsnNm>

<amountMap>

<WltAvlBal>985211.33</WltAvlBal>

<WltBalLmt>2000000.00</WltBalLmt>

<PerPyQot>2000000.00</PerPyQot>

<YrPyQot>50000000.00</YrPyQot>

<DayPyQot>10000000.00</DayPyQot>

</amountMap>

<wltInfoMap>

<WltId>0052************</WltId>

<CorprtnCustNm>*****公司</CorprtnCustNm>

<CorprtnIDTp>IT11</CorprtnIDTp>

<CorprtnIDNo>AG*****C3-8</CorprtnIDNo>

<WltNm>*****公司</WltNm>

<WltTp>WT09</WltTp>

<WltLvl>WL01</WltLvl>

<RcvPyCtrlStCd>1</RcvPyCtrlStCd>

<WltIssDt>20221103</WltIssDt>

<WltSts>03</WltSts>

</wltInfoMap>

<SgnAcctList>

<List>

<CorprtnIDTp>IT11</CorprtnIDTp>

<LglRepNm>邓*****</LglRepNm>

<availableAmount>999999863.70</availableAmount>

<bindDateTime>20230807101819</bindDateTime>

<CorprtnIDNo>AM******0</CorprtnIDNo>

<SgnAcctTp>AT04</SgnAcctTp>

<SgnAcctId>629******</SgnAcctId>

<CorprtnNm>*******公司</CorprtnNm>

11 / 44

民生银行银企直联

<sgnAcctNm>*******公司</sgnAcctNm>

</List>

<List>

<CorprtnIDTp>IT11</CorprtnIDTp>

<LglRepNm>邓**</LglRepNm>

<availableAmount>1002436583.76</availableAmount>

<bindDateTime>20221104093811</bindDateTime>

<CorprtnIDNo>AM******-0</CorprtnIDNo>

<SgnAcctTp>AT04</SgnAcctTp>

<SgnAcctId>629****860</SgnAcctId>

<CorprtnNm>****公司</CorprtnNm>

<sgnAcctNm>****公司</sgnAcctNm>

</List>

</SgnAcctList>

</xDataBody>

</CMBC>

3.建行钱包详情查询(B2eCCBWalletInfoQueryTra

ns)

本部分更新日期:2023-12-28

3.1.请求(B2eCCBWalletInfoQueryTrans)

  标记

是否必输 说明

<xDataBody>

 <WltId>

 <trnId>

Y

N

</xDataBody>

钱包 ID

交易唯一标识（字母或数字）

长度

16

64

12 / 44

3.2.响应(B2eCCBWalletInfoQueryTrans)

民生银行银企直联

  标记

是否

说明

必输

<xDataBody>

服务消息集

  <wltInfoMap>

钱包基本信息集 Map

   <WltId>

钱包 ID

   <CorprtnCustNm>

对公客户名称

   <CorprtnIDTp>

对公客户证件类型（详见 4.2 数据字

典-对公客户证件类型）

   <CorprtnIDNo>

对公客户证件号码

   <WltNm>

钱包昵称

   <WltTp>

钱包类型（详见 数据字典-钱包类型）

   <WltLvl>

钱包等级（详见 数据字典-钱包等级）

   <RcvPyCtrlStCd>

收付控制状态（详见 数据字典-收付控

制状态）

   <WltIssDt>

开户日期 yyyyMMdd

   <WltCnclDt>

销户日期 yyyyMMdd

   <WltSts>

钱包状态（详见 数据字典-钱包状态）

  </wltInfoMap>

钱包基本信息集 Map

  <amountMap>

钱包金额结果集 Map

   <WltAvlBal>

钱包可用余额

长

度

64

64

64

64

64

64

21

13 / 44

  标记

是否

说明

必输

   <WltBalLmt>

钱包余额上限

   <PerPyQot>

单笔转出限额

   <YrPyQot>

年累计支付限额

   <SrplsDayPyQot>

剩余日支付限额

   <SrplsYrPyQot>

剩余年累计转出限额

   <DayLimitAmt>

每日支付限额

  </amountMap>

钱包金额结果集 Map

  <SgnAcctList>

钱包已绑定账户列表 List

民生银行银企直联

长

度

21

21

21

21

21

21

   <CorprtnIDTp>

对公客户证件类型（详见 数据字典-对

公客户证件类型）

   <LglRepNm>

法定代表人姓名

   <availableAmount>

可用余额

   <bindDateTime>

绑定日期时间

   <CorprtnIDNo>

对公客户证件号码

   <SgnAcctTp>

账户类型（详见 数据字典-账户类型）

   <SgnAcctId>

绑定对公账户

   <CorprtnNm>

对公客户名称

   <sgnAcctNm>

对公账户名称

  <\SgnAcctList>

钱包已绑定账户列表 List

14 / 44

  标记

是否

说明

必输

  <LglRepNm>

法人姓名

  <RspbPsnNm>

对公客户名称

民生银行银企直联

长

度

64

64

</xDataBody>

3.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eCCBWalletInfoQueryTrans">

<requestHeader>

<dtClient>2023-08-18 10:05:25</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>ent</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<WltId>0052************</WltId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eCCBWalletInfoQueryTrans">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

15 / 44

民生银行银企直联

<message>交易成功</message>

</status>

<dtServer>2023-08-18 10:05:38</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<LglRepNm>贺******</LglRepNm>

<RspbPsnNm>******公司</RspbPsnNm>

<amountMap>

<WltAvlBal>985211.33</WltAvlBal>

<WltBalLmt>2000000.00</WltBalLmt>

<PerPyQot>2000000.00</PerPyQot>

<YrPyQot>50000000.00</YrPyQot>

<SrplsDayPyQot>10000000.00</SrplsDayPyQot>

<SrplsYrPyQot>47470651.82</SrplsYrPyQot>

<DayPyQot>10000000.00</DayPyQot>

</amountMap>

<wltInfoMap>

<WltId>0052************</WltId>

<CorprtnCustNm>******公司</CorprtnCustNm>

<CorprtnIDTp>IT11</CorprtnIDTp>

<CorprtnIDNo>AG******-8</CorprtnIDNo>

<WltNm>******公司</WltNm>

<WltTp>WT09</WltTp>

<WltLvl>WL01</WltLvl>

<RcvPyCtrlStCd>1</RcvPyCtrlStCd>

<WltIssDt>20221103</WltIssDt>

<WltSts>03</WltSts>

</wltInfoMap>

<SgnAcctList>

<List>

<CorprtnIDTp>IT11</CorprtnIDTp>

<LglRepNm>贺******</LglRepNm>

<availableAmount>999999863.70</availableAmount>

<bindDateTime>20230807101819</bindDateTime>

16 / 44

民生银行银企直联

<CorprtnIDNo>AG******8</CorprtnIDNo>

<SgnAcctTp>AT04</SgnAcctTp>

<SgnAcctId>63******</SgnAcctId>

<CorprtnNm>******公司</CorprtnNm>

<sgnAcctNm>******公司</sgnAcctNm>

</List>

<List>

<CorprtnIDTp>IT11</CorprtnIDTp>

<LglRepNm>贺******</LglRepNm>

<availableAmount>1002436583.76</availableAmount>

<bindDateTime>20221104093811</bindDateTime>

<CorprtnIDNo>AG******</CorprtnIDNo>

<SgnAcctTp>AT04</SgnAcctTp>

<SgnAcctId>617******</SgnAcctId>

<CorprtnNm>******公司</CorprtnNm>

<sgnAcctNm>******公司</sgnAcctNm>

</List>

</SgnAcctList>

</xDataBody>

</CMBC>

4.对公钱包交易明细查询(B2eWltTransDetailQry)

本部分更新日期:2023-12-28

4.1.请求(B2eWltTransDetailQry)

  标记

是否必输 说明

<xDataBody>

 <orgNo>

 <WltId>

 <BegDt>

Y

Y

Y

机构号

对公钱包 ID，16 位纯数字

起始日期，yyyy-MM-dd 格式

长度

20

16

10

17 / 44

  标记

是否必输 说明

长度

民生银行银企直联

Y

Y

Y

N

 <EndDt>

 <startId>

 <pageSize>

 <trnId>

</xDataBody>

结束日期，yyyy-MM-dd 格式

10

当前页数

每页笔数， [1-20]包含 1 到 20

8

2

交易唯一标识（字母或数字）

64

4.2.响应(B2eWltTransDetailQry)

  标记

是否必输 说明

长度

<xDataBody>

服务消息集

  <TotalPage>

总页数（建行正常，工行页码为 1

8

时有数据其余页码无数据）

  <TotalRec>

总笔数（建行正常，工行页码为 1

2

时有数据其余页码无数据）

  <TransDlList>

交易明细列表 list

   <Map>

   <WltId>

   <TxnSerilNo>

钱包 ID

交易流水号

   <WltTxnCd>

钱包交易类型: （建行）

1-付款,

2-收款,

3-兑回,

4-兑出,

16

64

4

18 / 44

  标记

是否必输 说明

长度

民生银行银企直联

5-退款,

6-其它;

（工行）

111-充值,

112-提现,

113-转账

114-收款,

115-支付,

116-全额退款,

117-部分退款,

118-二维码收款,

119-扫一扫收款,

120-资金归集付,

121-资金归集收,

122-工资收入,

123-工资支出,

124-支付下单;

交易摘要

交易金额

   <TxnSmy>

   <TrxAmt>

   <WltCurAmt>

账户余额（建行有，工行无）

   <WltAvlBal>

可用余额（建行有，工行无）

   <SgnAcctId>

   <SgnAcctNm>

对方账号

对方户名

140

21

22

23

68

240

   <SgnAcctPtyId>

对方交易行号（建行有，工行无） 68

19 / 44

  标记

是否必输 说明

长度

   <SgnAcctBnkNm>

对方交易行名（建行有，工行无）

240

民生银行银企直联

（工行可根据对方交易账号匹配翻

译） 根据对方交易钱包账号前 3

位匹配机构： 000-中国人民银

行；001-中国人民银行数字货币

研究所；002-中国工商银行；

003-中国农业银行；004-中国银

行；005-中国建设银行；006-微

众银行；007-网商银行；008-中

国邮政储蓄银行；009-中国交通

银行；010-招商银行；011-兴业

银行；

   <TxnDt>

交易日期 yyyyMMdd

   <TxnTm>

交易时间 HHmmss

   <TrxSts>

交易状态（工行有，建行无）

8

6

2

PR00-处理成功, PR01-处理失败,

PR02-处理中, PR50-待处理,

PR51-退款中, PR52-已部分退款,

PR53-已全额退款, PR54-交易记

录不存在

   </Map>

</xDataBody>

4.3.例子

请求报文

20 / 44

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

民生银行银企直联

trnCode="B2eWltTransDetailQry">

<requestHeader>

<dtClient>2023-08-18 13:36:21</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>*******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<orgNo>C1010511003703</orgNo>

<WltId>0052************</WltId>

<BegDt>2023-06-01</BegDt>

<EndDt>2023-06-16</EndDt>

<startId>1</startId>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltTransDetailQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-18 13:36:39</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

21 / 44

<xDataBody>

<TotalPage>2</TotalPage>

<TransDlList>

<TxnSerilNo>81600******************816832166</TxnSerilNo>

民生银行银企直联

<WltTxnCd>3</WltTxnCd>

<TxnSmy>智能兑回</TxnSmy>

<TrxAmt>15.00</TrxAmt>

<WltCurAmt>910.00</WltCurAmt>

<WltAvlBal>910.00</WltAvlBal>

<SgnAcctId>617******</SgnAcctId>

<SgnAcctNm>******公司</SgnAcctNm>

<SgnAcctPtyId>C1030511000483</SgnAcctPtyId>

<SgnAcctBnkNm>C1030511000483</SgnAcctBnkNm>

<TxnDt>20230607</TxnDt>

<TxnTm>152610</TxnTm>

</item>

<item>

<WltId>0052************</WltId>

<TxnSerilNo>31300************1354AE03ML</TxnSerilNo>

<WltTxnCd>4</WltTxnCd>

<TxnSmy>数字人民币兑出</TxnSmy>

<TrxAmt>15.00</TrxAmt>

<WltCurAmt>925.00</WltCurAmt>

<WltAvlBal>925.00</WltAvlBal>

<SgnAcctId>61******</SgnAcctId>

<SgnAcctNm>******公司</SgnAcctNm>

<SgnAcctPtyId>C1030511000483</SgnAcctPtyId>

<SgnAcctBnkNm>C1030511000483</SgnAcctBnkNm>

<TxnDt>20230607</TxnDt>

<TxnTm>152135</TxnTm>

</item>

</TransDlList>

<TotalRec>15</TotalRec>

</xDataBody>

</CMBC>

22 / 44

民生银行银企直联
5.对公钱包绑定/解绑对公账户(我行)(B2eWltBindCar

dTransfer)

本部分更新日期:2023-08-18

5.1.请求(B2eWltBindCardTransfer)

  标记

是否必输 说明

长度

<xDataBody>

 <orgNo>

 <WltId>

 <PayType>

 <CardNo>

Y

Y

Y

Y

机构号

对公钱包 ID，16 位纯数字

绑卡类型，4-绑卡，5-解绑

钱包绑定结算户账号

 <CardName> Y

钱包绑定账户户名

N

N

Y

 <LglRepTel>

 <trnId>

 <insId>

</xDataBody>

法定代表人手机号，建行必输

交易唯一标识（字母或数字）

交易流水号（字母或数字，不能重复）

5.2.响应(B2eWltBindCardTransfer)

  标记

是否

说明

必输

<xDataBody>

服务消息集

20

16

1

32

60

11

64

64

长度

23 / 44

  标记

  <trnId>

  <insId>

是否

说明

必输

民生银行银企直联

长度

交易唯一标识（请求时给出的 trnId）

交易流水号（请求时给出的 insId）

  <_JnlNo>

流水号 list

  <type>

返回码类型，表示服务的执行状态，取

值范围如下：

E – 错误

S – 成功

R - 不一致

业务编码

业务信息

  <code>

  <message>

  <channelSerialNo>

交易流水号

</xDataBody>

5.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltBindCardTransfer">

<requestHeader>

<dtClient>2023-08-18 13:39:58</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

64

64

50

1

8

255

32

24 / 44

民生银行银企直联

</requestHeader>

<xDataBody>

<orgNo>C1010511003703</orgNo>

<trnId>CMBCTRN202308181338035</trnId>

<insId>CMBCINS202308181338036</insId>

<WltId>0052************</WltId>

<CardNo>617******</CardNo>

<CardName>******公司</CardName>

<PayType>4</PayType>

<LglRepTel>15822******</LglRepTel>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltBindCardTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-18 13:40:11</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<insId>CMBCINS202308181338036</insId>

<code>TPP00000</code>

<trnId>CMBCTRN202308181338035</trnId>

<_JnlNo>20230818130754027753</_JnlNo>

<channelSerialNo>T7765202308************AE006O</channelSerialNo>

<type>S</type>

<message>交易成功</message>

</xDataBody>

</CMBC>

25 / 44

民生银行银企直联
6.对公钱包已绑定、已解绑列表查询（应答运营机构）(B

2eWltResBindQry)

本部分更新日期:2023-08-18

6.1.请求(B2eWltResBindQry)

  标记

是否必输 说明

长度

<xDataBody>

 <feeNo>

 <startNo>

Y

Y

当前页数

 <paystatus> Y

每页笔数

</xDataBody>

6.2.响应(B2eWltResBindQry)

绑定状态 1004-已绑定列表 1005-已解绑列表 4

8

2

  标记

<xDataBody>

是否必输

说明

长度

服务消息集

  <totalSize>

总条数

8

  <resultList>

   <Map>

结果集合 list

    <protocolNo>

签约协议号

    <protocolBeginDate>

    <protocolEndDate>

绑定日期

解绑日期

40

8

8

26 / 44

  标记

是否必输

说明

长度

    <custUserId>

钱包开立所属运营机构

20

民生银行银企直联

编码

C1010211000012 工

行机构号

C1010511003703 建

行机构号

对公钱包 ID

    <promoName>

    <accNo>

钱包绑定结算户账号

    <name>

钱包绑定账户户名

80

34

62

    <effectDate>

签约渠道: SC02-柜面；

8

SC00-网银； SC01-手

机银行；

    <merchantNameOut>

对公钱包等级（详见 4.2

512

数据字典-钱包等级）

    <PurchaseNote>

对公客户名称

200

结果集合 list

   </Map>

  </resultList>

</xDataBody>

6.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltResBindQry">

<requestHeader>

27 / 44

<dtClient>2023-08-18 13:40:56</dtClient>

民生银行银企直联

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<feeNo>1004</feeNo>

<startNo>1</startNo>

<paystatus>10</paystatus>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltResBindQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-18 13:41:06</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<totalSize>62</totalSize>

<resultList>

<item>

<protocolNo>31011000******987</protocolNo>

<protocolBeginDate>20220114</protocolBeginDate>

<protocolEndDate></protocolEndDate>

<custUserId>C1030844001362</custUserId>

28 / 44

民生银行银企直联

<promoName>0102************</promoName>

<accNo>616******</accNo>

<name>********公司</name>

<effectDate></effectDate>

<merchantNameOut>WL01</merchantNameOut>

<PurchaseNote>*******公司</PurchaseNote>

</item>

<item>

<protocolNo>3101100************70000000865</protocolNo>

<protocolBeginDate>20211229</protocolBeginDate>

<protocolEndDate></protocolEndDate>

<custUserId>C1030844001362</custUserId>

<promoName>01020************</promoName>

<accNo>6158******</accNo>

<name>*******公司</name>

<effectDate></effectDate>

<merchantNameOut>WL01</merchantNameOut>

<PurchaseNote>*******公司</PurchaseNote>

</item>

</resultList>

</xDataBody>

</CMBC>

7.对公账户兑出到数字人民币钱包(B2ePayInWltTran

s)

本部分更新日期:2023-08-18

7.1.请求(B2ePayInWltTrans)

  标记

是否必

说明

长度

输

<xDataBody>

29 / 44

民生银行银企直联

长度

20

16

60

32

60

20

46

64

64

  标记

是否必

说明

 <orgNo>

 <PayeeAccNo>

 <PayeeAccName>

 <DraweeAccNo>

输

Y

Y

Y

Y

对公钱包机构号

对公钱包 ID

对公钱包昵称

转出账户账号

 <DraweeAccName> Y

转出账户名称

 <TranAmount>

 <postScript>

 <trnId>

 <insId>

</xDataBody>

Y

N

N

Y

兑出金额

交易摘要（建行必输，工行非必输）

（不能输入特殊字符）

交易唯一标识（字母或数字）

交易流水号（字母或数字，不能重

复）

7.2.响应(B2ePayInWltTrans)

  标记

<xDataBody>

  <trnId>

是否必输

说明

长度

服务消息集

交易唯一标识（请求时给

64

出的 trnId）

  <insId>

交易流水号（请求时给出

64

的 insId）

30 / 44

  标记

  <_JnlNo>

  <Date>

  <Time>

   <result>

    <domain>

    <type>

    <code>

    <message>

是否必输

说明

长度

民生银行银企直联

流水号

日期

时间

结果集 Map

系统编号

返回码类型: 返回码类

型，表示服务的执行状

态，取值范围如下： E –

错误, W – 警告（视作成

功）--暂时不返回, S – 成

功, R - 不一致;

业务编码

业务信息

    <draweeDate>

付款账号过账日期

    <draweeSeq>

付款账号过账流水号

    <tradeSeq>

支付交易序号

    <orderId>

支付平台订单号

    <draweeBalance>

付款账户余额

    <outExtendData>

扩展数据

   </result>

</xDataBody>

结果集 Map

31 / 44

民生银行银企直联

7.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ePayInWltTrans">

<requestHeader>

<dtClient>2023-08-18 10:07:29</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>ent</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>CMBCINS202308181007052</insId>

<orgNo>C1010511003703</orgNo>

<DraweeAccNo>617******</DraweeAccNo>

<DraweeAccName>******公司</DraweeAccName>

<TranAmount>8</TranAmount>

<PayeeAccNo>0052************</PayeeAccNo>

<PayeeAccName>******公司</PayeeAccName>

<PostScript>兑出 8</PostScript>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ePayInWltTrans">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

32 / 44

</status>

<dtServer>2023-08-18 10:07:50</dtServer>

民生银行银企直联

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<result>

<domain>544</domain>

<type>S</type>

<code>PES00000</code>

<message>交易成功</message>

<draweeDate>20230818</draweeDate>

<draweeSeq>00010062************00119563493</draweeSeq>

<tradeSeq></tradeSeq>

<orderId>20230************2232</orderId>

<draweeBalance>10033599942.56</draweeBalance>

<outExtendData>sndFlag=1|genTime=20230818100749|msgid=|businum=

T7772************54AE02HL|</outExtendData>

</result>

<insId>CMBCINS202308181007052</insId>

<_JnlNo>20230818100748824253</_JnlNo>

<Time>10:07:46</Time>

<Date>2023-08-18</Date>

</xDataBody>

</CMBC>

8.数字钱包兑回到银行账户(B2ePayforWltTrans)

本部分更新日期:2023-08-18

8.1.请求(B2ePayforWltTrans)

  标记

是否必输

说明

<xDataBody>

长度

33 / 44

民生银行银企直联

长度

  标记

是否必输

说明

 <orgNo>

 <DraweeAccNo>

Y

Y

对公钱包机构号

对公钱包 ID

 <DraweeAccName> Y

对公钱包昵称

Y

Y

Y

N

N

Y

 <PayeeAccNo>

 <PayeeAccName>

 <TranAmount>

 <postScript>

 <trnId>

 <insId>

</xDataBody>

转入账户账号

转入账户名称

兑回金额

交易摘要（建行必输，工行非必输）

（不能输入特殊字符）

交易唯一标识（字母或数字）

交易流水号（字母或数字，不能重

复）

8.2.响应(B2ePayforWltTrans)

  标记

是否必输

说明

20

16

60

32

60

20

46

64

64

长

度

<xDataBody>

  <trnId>

服务消息集

交易唯一标识（请求时

64

给出的 trnId）

  <insId>

交易流水号（请求时给

64

出的 insId）

34 / 44

  标记

是否必输

说明

民生银行银企直联

长

度

  <_JnlNo>

  <Date>

  <Time>

   <result>

    <domain>

    <type>

    <code>

    <message>

流水号

日期

时间

结果集 Map

系统编号

返回码类型: 返回码类

型，表示服务的执行状

态，取值范围如下： E

– 错误, W – 警告（视作

成功）--暂时不返回, S

– 成功, R - 不一致;

业务编码 一、本行：空

二、第三方支付通道：

第三方支付通道的流水

号或者订单号 三、E 支

付：空 四、金卡： 银联

流水号 五、微信二维

码：通道调用后端流水

号。

业务信息

    <draweeDate>

付款账号过账日期

    <draweeSeq>

付款账号过账流水号

    <tradeSeq>

支付交易序号

35 / 44

  标记

是否必输

说明

民生银行银企直联

长

度

    <orderId>

支付平台订单号

    <draweeAccType>

    <draweeBalance>

付款账户类型

付款账户余额

    <draweePaymentPath

付款方资金通道

>

    <orderId>

支付平台订单号

    <pathCommModeOut

通道处理方式

>

    <pathTransNo>

通道流水号

    <pathFeeAmount>

通道手续费金额

IC 卡的应用类型

专用输出信息 2

IC 卡 55 域

扩展数据

结果集 Map

    <speOut1>

    <speOut2>

    <speOut3>

    <outExtendData>

   </result>

</xDataBody>

8.3.例子

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ePayforWltTrans">

36 / 44

民生银行银企直联

<requestHeader>

<dtClient>2023-08-18 10:08:32</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>******</userPswd>

<language>chs</language>

<appId>ent</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>CMBCINS202308181007053</insId>

<orgNo>C1010511003703</orgNo>

<DraweeAccNo>0052************</DraweeAccNo>

<DraweeAccName>******公司</DraweeAccName>

<TranAmount>2.1</TranAmount>

<PayeeAccNo>617******</PayeeAccNo>

<PayeeAccName>******公司</PayeeAccName>

<PostScript>兑出 2.1</PostScript>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2ePayforWltTrans">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-18 10:08:52</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<result>

37 / 44

民生银行银企直联

<domain>544</domain>

<type>S</type>

<code>PES00000</code>

<message>交易成功</message>

<draweeDate>20230818</draweeDate>

<draweeSeq></draweeSeq>

<draweeAccType></draweeAccType>

<draweeBalance></draweeBalance>

<draweePaymentPath>12A3</draweePaymentPath>

<orderId>202308******272</orderId>

<pathCommModeOut>3</pathCommModeOut>

<pathTransNo></pathTransNo>

<pathFeeAmount></pathFeeAmount>

<speOut1></speOut1>

<speOut2></speOut2>

<speOut3></speOut3>

<outExtendData></outExtendData>

</result>

<insId>CMBCINS202308181007053</insId>

<_JnlNo>202308******1753</_JnlNo>

<Time>10:08:48</Time>

<Date>2023-08-18</Date>

</xDataBody>

</CMBC>

9.对公钱包转账(钱包转钱包)(B2eWltToWltTransfer)

本部分更新日期:2023-08-18

9.1.请求(B2eWltToWltTransfer)

  标记

是否必输

说明

<xDataBody>

 <orgNo>

Y

机构号

长度

20

38 / 44

  标记

 <WltId>

 <WalletName>

 <InWltId>

 <InWalletName>

是否必输

说明

长度

民生银行银企直联

Y

Y

Y

Y

16

60

16

48

付款钱包 ID，纯数字

付款钱包昵称

收款钱包 ID，纯数字

收款钱包昵称（付款钱包

为工行时必输，付款钱包

为建行时“收款钱包昵称、

收款客户名称”至少输一

项）

 <InWalletAccountName> Y

收款客户名称（付款钱包

75

 <TranAmount>

 <PostScript>

 <verifyType>

Y

N

N

为建行时“收款钱包昵称、

收款客户名称”至少输一

项）

交易金额

交易摘要，付款钱包为建

行时必输（不能输入特殊

字符）

20

46

验证短信标志 付款钱包为

1

工行时不涉及； 付款钱包

为建行时可能涉及，取值

空或 1；

●首次调用传空；

●当首次调用应答的交易

状态 status 值为 05，需

要二次调用时传固定值

1；

39 / 44

  标记

是否必输

说明

长度

 <identityCode>

N

短信验证码，纯数字 付款

10

民生银行银企直联

钱包为工行时不涉及； 付

款钱包为建行时可能涉

及；

●首次调用传空；

●当首次调用应答的交易

状态 status 值为 05，需

要二次调用时传短信验证

码；

 <oldChannelSerialNo>

N

原交易流水号 付款钱包为

32

工行时不涉及； 付款钱包

为建行时可能涉及；

●首次调用传空；

●当首次调用应答的交易

状态 status 值为 05，需

要二次调用时传首次调用

返回的

oldChannelSerialNo-原

交易流水号字段值；

 <trnId>

 <insId>

</xDataBody>

N

Y

交易唯一标识（字母或数

64

字）

交易流水号（字母或数

64

字，不能重复）

40 / 44

9.2.响应(B2eWltToWltTransfer)

民生银行银企直联

  标记

是否必输 说明

长度

<xDataBody>

服务消息集

  <trnId>

交易唯一标识（请求时给出的

64

trnId）

  <insId>

交易流水号（请求时给出的

  <_JnlNo>

  <type>

  <code>

  <message>

  <status>

insId）

流水号

返回码类型，表示服务的执行状

态，取值范围如下：

E – 错误

S – 成功

R - 不一致

业务编码

业务信息

交易状态

00-成功

05-短信已发送

当返回 05 时，说明该笔交易需

要验证短信，要进行二次调用。

64

50

1

8

255

2

  <oldChannelSerialNo>

原交易流水号（status=05 时

32

  <tdSerialNo>

  <tdDate>

做为二次调用入参）

通道流水号

通道日期

50

8

41 / 44

是否必输 说明

长度

民生银行银企直联

  标记

  <orderId>

  <genDateTime>

订单号

20

  <voucherNo>

交易凭证号

</xDataBody>

9.3.例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltToWltTransfer">

<requestHeader>

<dtClient>2023-08-18 13:42:19</dtClient>

<clientId>2300******</clientId>

<userId>2300******001</userId>

<userPswd>*******</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<insId>CMBCINS2023081813380310</insId>

<trnId>CMBCTRN202308181338039</trnId>

<orgNo>C1010511003703</orgNo>

<WltId>0052************</WltId>

<WalletName>******公司</WalletName>

<TranAmount>6.89</TranAmount>

<InWltId>0052************</InWltId>

<InWalletName>******公司</InWalletName>

<InWalletAccountName></InWalletAccountName>

<PostScript>转账测试摘要</PostScript>

14

60

42 / 44

</xDataBody>

</CMBC>

响应报文

民生银行银企直联

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eWltToWltTransfer">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-08-18 13:42:34</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<code>PCIP0000</code>

<orderId></orderId>

<_JnlNo>20230818130754081053</_JnlNo>

<oldChannelSerialNo></oldChannelSerialNo>

<type>S</type>

<message>交易成功</message>

<tdDate>********</tdDate>

<voucherNo>101057************************</voucherNo>

<genDateTime>**************</genDateTime>

<tdSerialNo>T77722023************9354AE007B</tdSerialNo>

<insId>CMBCINS2023081813380310</insId>

<trnId>CMBCTRN202308181338039</trnId>

<status>00</status>

</xDataBody>

</CMBC>

43 / 44

