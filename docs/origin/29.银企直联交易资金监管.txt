银企直联接口文档

（银企直联交易资金监管）

邮箱：<EMAIL>

版本

日期

说明

编写者 审核者

文档修改记录

民生银行银企直联

V1.0.0 2021-03-

定义接口文档

18

V2.0.0 2024-08-

划转状态查询

14

(B2eFundSupTransResQry)返回字段

<status>、<statusDesc>添加说明

1 / 12

目录

民生银行银企直联

目录 .............................................................................................2

1. 资金划转(B2EFUNDSUPTRANSFER) ................................................ 3

1.1. 请求参数 ................................................................................. 3

1.2. 响应参数 ................................................................................. 4

1.3. 报文示例 ................................................................................. 5

2. 划转状态查询(B2EFUNDSUPTRANSRESQRY) .....................................6

2.1. 请求参数 ................................................................................. 6

2.2. 响应参数 ................................................................................. 7

2.3. 报文示例 ................................................................................. 9

2 / 12

1.资金划转(B2eFundSupTransfer)

民生银行银企直联

本部分更新日期:2023-06-13

1.1.请求参数

  标记

说明

长度

 <xDataBody>

  <trnId>

客户端产生的交易唯一标志（必输，但无作

64

用）(★)

  <insId>

指令 ID，一条转帐指令在客户端的唯一标识

64

(★)

  <PayerAcNo>

指付款账号(★)

  <PayerAcName>

付款账号名称(★)

  <PayeeAcNo>

收款账号(★)

  <PayeeAcName>

收款人账号名称(★)

32

80

32

80

  <PayeeBankNo>

收款人开户行行号(收款人为他行时必输，本行

12

时无需输入，以账号及户名在系统中查回的对

应信息为准)(定向支付无需输入，以系统中维

护的白名单信息为准)

  <PayeeDeptName> 收款人开户行名称

80

(收款人为本行时无需输入，以账号及户名在系

统中查回的对应信息为准；收款人为他行时非

必输，以行名行号表查询结果为准)(定向支付

无需输入，以系统中维护的白名单信息为准)

3 / 12

民生银行银企直联

  <EntPerFlag>

对公对私标识(★)

(0-对公，1-对私)

(定向支付无需输入，以系统中维护的白名单信

  <SysFlag>

息为准)

本他行标识(★)

(0-本行，1-他行)

(定向支付无需输入，以系统中维护的白名单信

  <Amount>

息为准)

金额(★)

  <Usage>

用途(★) 对公：

1

1

12

"网下申购","贷款","往来结算款","差旅费","租

赁费","办公费","合同费","水电费","运费","工

程款","劳务费","通讯费","交通费","报刊费","

餐费","投资款","增资款","其他" 对私："代发

工资奖金","财务报销","拆迁款","律师费","租

金"

  <Remark>

备注（用途为其他时必输）（用途+备注长度

最大为 50,一个汉字=2）

 <xDataBody>

1.2.响应参数

  标记

说明

长度

 <xDataBody>

  <message>

信息

  <PayeeBankNo>

收款人开户行行号

4 / 12

  <PayeeDeptName> 收款人开户行名称

民生银行银企直联

 </xDataBody>

1.3.报文示例

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eFundSupTransfer">

<requestHeader>

<dtClient>2023-05-05 11:50:06</dtClient>

<clientId>2200****53</clientId>

<userId>2200****53003</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>ent</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>CMBCTRN2023042515342916</trnId>

<insId>CMBCINS2023042515342917</insId>

<PayerAcNo>617***26</PayerAcNo>

<PayerAcName>资金***化三</PayerAcName>

<PayeeAcNo>6141***19</PayeeAcNo>

<PayeeAcName>资金***化二</PayeeAcName>

<PayeeBankNo></PayeeBankNo>

<PayeeDeptName></PayeeDeptName>

<EntPerFlag>0</EntPerFlag>

<SysFlag>0</SysFlag>

<Amount>100.00</Amount>

<Usage>网下申购</Usage>

<Remark>网下申购</Remark>

</xDataBody>

</CMBC>

响应报文

5 / 12

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eFundSupTransfer">

民生银行银企直联

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-05-05 14:51:24</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<message>交易成功</message>

<PayeeBankNo>0105</PayeeBankNo>

<PayeeDeptName>中国民生银行股份有限公司北京中关村支行

</PayeeDeptName>

</xDataBody>

</CMBC>

2.划转状态查询(B2eFundSupTransResQry)

本部分更新日期:2023-06-13

2.1.请求参数

  标记

说明

 <xDataBody>

长度

  <QryType>

用户角色类型（★）（0-监管方角色，1-被监管方角

1

色）

  <AcNo>

账号（★）

32

6 / 12

  <BeginDate> 查询起始时间（★）YYYY-MM-DD

  <EndDate>

查询结束时间（★）

  <pageNo>

页码

  <pageSize>

页面大小

 </xDataBody>

2.2.响应参数

民生银行银企直联

10

10

5

5

  标记

说明

长度

 <xDataBody>

  <pageNumber>

页码

  <recordNumber>

记录总数

  <pageSize>

页面大小

  <approvingTotalAmt>

未审批总金额

  <List>

   <Map>

   <accountNo>

签约账号

   <accountName>

被监管账号名称

   <accountBankName>

被监管账号开户行名称

   <payeeAcctNo>

收款账号

   <payeeAcctName>

收款账号名称

   <payeePayBankNo>

收款人开户行支付行号

5

5

5

5

32

40

40

32

40

32

7 / 12

民生银行银企直联

40

16,2

1

30

6

16,2

3

50

1

   <payeePayBankName> 收款人开户行支付行名称

   <transAmt>

转账金额

   <paymentPath>

汇路（网银汇款方式 0-本行，1-他行）

   <voucherNo>

付款凭证号

   <voucherType>

凭证类型

   <feeAmt>

手续费金额

   <currency>

币种

   <postScript>

摘要（网银用途）

   <status>

状态：

0：待审批

1：成功

2：失败

3：审批拒绝

4：交易结果未知

5：失效

   <statusDesc>

状态描述（如果失败返回失败原因，其

60

他状态返回状态名称）

   <createTime>

转账申请时间

   <approveMessage>

审批信息

   <creatDate>

转账申请日期

   </Map>

  </List>

19

80

12

5

8 / 12

民生银行银企直联

 </xDataBody>

2.3.报文示例

请求报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eFundSupTransResQry">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>2200****53</clientId>

<userId>2200****53003</userId>

<userPswd>03bh3g</userPswd>

<language>UTF-8</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<QryType>1</QryType>

<AcNo>615****03</AcNo>

<BeginDate>2023-02-05</BeginDate>

<EndDate>2023-05-05</EndDate>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="UTF-8"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eFundSupTransResQry">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>交易成功</message>

</status>

<dtServer>2023-05-05 14:00:10</dtServer>

<userKey>N</userKey>

9 / 12

民生银行银企直联

<dtDead>

</dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<recordNumber>1</recordNumber>

<pageNumber>1</pageNumber>

<approvingTotalAmt>0</approvingTotalAmt>

<pageSize>10</pageSize>

<List>

<Map>

<accountNo>615****03</accountNo>

<accountName>资金***化三</accountName>

<accountBankName>中国民生银行股份有限公司北京中关村支行

</accountBankName>

<payeeAcctNo>2910</payeeAcctNo>

<payeeAcctName>哈哈 291 三</payeeAcctName>

<payeePayBankNo>************</payeePayBankNo>

<payeePayBankName>中国工商银行股份有限公司北京通州支行新华分理处

</payeePayBankName>

<transAmt>162.00</transAmt>

<paymentPath>1</paymentPath>

<voucherNo>*************</voucherNo>

<voucherType>4098000</voucherType>

<feeAmt>34.00</feeAmt>

<currency>RMB</currency>

<postScript>投资款测试一下可以不可以使用</postScript>

<status>1</status>

<statusDesc>成功</statusDesc>

<createTime>2023-03-21 14:58:25</createTime>

<approveMessage>

</approveMessage>

<creatDate>2023-03-21</creatDate>

</Map>

</List>

</xDataBody>

</CMBC>

10 / 12

民生银行银企直联

11 / 12

