银企直联接口文档

（理财持仓查询）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 32

目录

民生银行银企直联

目录 .............................................................................................2

1. 持有理财查询 ( B2EQUERYMYFINANCE ) ...........................................4

1.1. 请求( B2EQUERYMYFINANCE ) ..................................................... 4

1.2. 响应( B2EQUERYMYFINANCE ) ..................................................... 4

1.3. 例子 ...................................................................................... 7

2. 理财产品历史收益查询 (B2EQRYFINANCEINCOMERATEHIS) ................12

2.1. 请求( B2EQRYFINANCEINCOMERATEHIS) .......................................12

2.2. 响应( B2EQRYFINANCEINCOMERATEHIS) .......................................13

2.3. 例子 .................................................................................... 14

3. 理财产品基本信息查询 ( B2EQUERYPRDBUYINFO) ............................. 15

3.1. 请求( B2EQUERYPRDBUYINFO ) ..................................................15

3.2. 响应(B2EQUERYPRDBUYINFO ) .................................................. 16

3.3. 例子 .................................................................................... 18

4. 理财历史收益明细查询 (B2EQUERYMYFINANCEHIS) ...........................20

4.1. 请求(B2EQUERYMYFINANCEHIS) .................................................21

4.2. 响应(B2EQUERYMYFINANCEHIS) .................................................21

4.3. 例子 .................................................................................... 22

5. 理财持有产品详细信息查询 (B2EQUERYPRDDETAILINFO) .................... 24

5.1. 请求(B2EQUERYPRDDETAILINFO) ................................................ 24

5.2. 响应(B2EQUERYPRDDETAILINFO) ................................................ 24

5.3. 例子 .................................................................................... 26

6. 理财文件下载 (B2EQRYENTPRDPROTOCOL) .................................... 29

6.1. 请求(B2EQRYENTPRDPROTOCOL) .................................................... 29

2 / 32

民生银行银企直联
6.2. 响应(B2EQRYENTPRDPROTOCOL) ............................................... 29

6.3. 例子 .................................................................................... 30

3 / 32

1.持有理财查询 ( B2eQueryMyFinance )

民生银行银企直联

本部分更新日期:2022-03-16

持有理财查询

1.1.请求( B2eQueryMyFinance )

标记

说明

长度

<xDataBody>

</xDataBody>

1.2.响应( B2eQueryMyFinance )

标记

说明

<xDataBody>

服务消息集

 <todayImpPrd>

预约中的

  <prdCode>

产品代码

  <prdType>

产品类型：

0：每日型

1：定期开放型

2：封闭型

3：收益型

4：净值类周期型

5：活期型

6：净值类封闭型

  <prdName>

产品名称

长

度

4 / 32

民生银行银企直联

长

度

标记

说明

  <currType>

币种：

156:人民币

  <amt>

在途资金/交易金额

  <entStatus>

产品状态 entStatus 如果返回就用 entStatus，没返

回就用 status

  <status>

产品状态：

0:预约中

1:持仓中

2:赎回中

3:资金在途 1

4:资金在途 2

5:冻结中

6:部分赎回中或转让中

7:转让中

  <dbIncomeRate> 预期收益率

  <cardNo>

交易账号

  <assoDate>

预计资金到账日期/下一开放日

  <issDate>

净值日期

  <NAV>

最新净值(净值类产品)

 </todayImpPrd>

 <list>

该查询条件下持有理财信息列表

  <prdCode>

产品代码

5 / 32

标记

说明

民生银行银企直联

长

度

  <prdType>

产品类型：

0：每日型

1：定期开放型

2：封闭型

3：收益型

4：净值类周期型

5：活期型

6：净值类封闭型

  <prdName>

产品名称

  <currType>

币种：

156 人民币

  <amtValue>

产品份额（活期型总份额）

  <onwayAmt>

在途资金可用份额

  <useVol>

useVol

  <btaType>

产品类型

  <totIncome>

累计收益：

btaType 为空或 prdType 为 5 时，累计收益取

incomeRate，其它情况取 totIncome

  <incomeRate>

累计收益：

btaType 为空或 prdType 为 5 时，累计收益取

incomeRate，其它情况取 totIncome

  <income>

预期未分配收益

  <cardNo>

交易账号

6 / 32

标记

说明

  <status>

产品的状态：

民生银行银企直联

长

度

0:预约中

1:持仓中

2:赎回中

3:资金在途 1

4:资金在途 2

5:冻结中

6:部分赎回中或转让中

7:转让中

 </list>

</xDataBody>

1.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryMyFinance">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

</xDataBody>

</CMBC>

7 / 32

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQueryMyFinance"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<todayImpPrd>

<item>

<NAV>1.0</NAV>

<nextEndDate>********</nextEndDate>

<benchMarkMin>0.0</benchMarkMin>

<preIncomeRate>0.0</preIncomeRate>

<cardNo>*********</cardNo>

<cfmDate>null</cfmDate>

<protocol>null</protocol>

<prdType>1</prdType>

<tssTransCode>100412</tssTransCode>

<transCode>

<item>100201</item>

<item>100203</item>

</transCode>

<transAccount>*********</transAccount>

<workDateList>

民生银行银企直联

8 / 32

民生银行银企直联

<item>2021-11-03</item>

<item>2021-11-04</item>

<item>2021-11-05</item>

<item>2021-11-08</item>

</workDateList>

<prdName>资产周期对公 0180</prdName>

<incomeType>1</incomeType>

<phiDate>null</phiDate>

<nextIncomeRate>0.0</nextIncomeRate>

<dbIncomeRate>0.0014</dbIncomeRate>

<serialNo>54002********0000000297</serialNo>

<channels>019d</channels>

<renegeInterTypeName></renegeInterTypeName>

<prdNextDate>********</prdNextDate>

<btaType></btaType>

<permitTransferCode></permitTransferCode>

<amtValue>0.0</amtValue>

<forceMode></forceMode>

<startDate>********</startDate>

<status>3</status>

<firstAmt>0.0</firstAmt>

<cfmAmt>null</cfmAmt>

<cancleFlag>2</cancleFlag>

<riskLevel>5</riskLevel>

<clientName>null</clientName>

<contractNo>null</contractNo>

<bmType></bmType>

<currType>156</currType>

<amt>3.84</amt>

<cfmVol>null</cfmVol>

<nDays>1</nDays>

<prdCode>FGAB150180</prdCode>

<bankAcc>*********</bankAcc>

<trsState>0</trsState>

<prdAttr>A</prdAttr>

<vol>0.00</vol>

<controlFlag>*************** 00

0001000100000000010420000020010</controlFlag>

9 / 32

<statusName>资金在途</statusName>

<transDate>********</transDate>

<closeTime>090000</closeTime>

<liveTime>1</liveTime>

<openTime>090000</openTime>

<summary>null</summary>

<transName>null</transName>

<assoDate>********</assoDate>

<useVol>1000000.0</useVol>

<transCodeName>自动付息</transCodeName>

<prdShortName></prdShortName>

<issDate>********</issDate>

<livTimeUnitName>1 天</livTimeUnitName>

<entStatus>资金在途</entStatus>

<riskLevelName>高风险(五级)</riskLevelName>

<benchMarkMax>0.0</benchMarkMax>

</item>

</todayImpPrd>

<list>

<item>

<NAV>1.000000</NAV>

<nextEndDate>********</nextEndDate>

<benchMarkMin>0.0</benchMarkMin>

<preIncomeRate>0.0</preIncomeRate>

<incomeRate>391.98</incomeRate>

<tAClient>TSS000662926</tAClient>

<cardNo>*********</cardNo>

<protocol></protocol>

<prdType>1</prdType>

<divModeName>现金分红</divModeName>

<transCode>

<item>100201</item>

<item>100203</item>

</transCode>

<workDateList>

<item>2021-09-13</item>

<item>2021-09-16</item>

民生银行银企直联

10 / 32

<item>2021-09-22</item>

</workDateList>

<totIncome>364.47</totIncome>

<prdName>非凡资产管理 3 天开放式非保 A</prdName>

<incomeType>1</incomeType>

<nextIncomeRate>0.0</nextIncomeRate>

<dbIncomeRate>0.0251</dbIncomeRate>

<channels>0179</channels>

<renegeInterTypeName></renegeInterTypeName>

<prdNextDate>20210916</prdNextDate>

<nearEndDate>20210916</nearEndDate>

<btaType></btaType>

<permitTransferCode></permitTransferCode>

<investTimes></investTimes>

<amtValue>100000.0</amtValue>

<forceMode></forceMode>

<startDate>********</startDate>

<status>1</status>

<income>27.51</income>

<firstAmt>0.0</firstAmt>

<transferFlag>0</transferFlag>

<riskLevel>2</riskLevel>

<totPrinciple>100000.0</totPrinciple>

<contractNo>54000202107200000001094</contractNo>

<bmType></bmType>

<currType>156</currType>

<clientNo>**********</clientNo>

<nDays>2</nDays>

<prdCode>FGAB17027A</prdCode>

<bankAcc>*********</bankAcc>

<prdAttr>A</prdAttr>

<vol>100000.0</vol>

<controlFlag>*************** 00 0001000 04000

****************</controlFlag>

<cashFlag>0</cashFlag>

<orderInDate>3</orderInDate>

<onwayAmt>130.66</onwayAmt>

<tradeFrozen>0.0</tradeFrozen>

民生银行银企直联

11 / 32

民生银行银企直联

<otherFrozen>0.0</otherFrozen>

<closeTime>163000</closeTime>

<liveTime>3</liveTime>

<openTime>090000</openTime>

<transFrom>0000</transFrom>

<amount>100158.17</amount>

<cost>200000.0</cost>

<useVol>100000.0</useVol>

<prdShortName></prdShortName>

<divMode>1</divMode>

<issDate>********</issDate>

<prdValue>100027.51</prdValue>

<livTimeUnitName>3 天</livTimeUnitName>

<riskLevelName>较低风险(二级)</riskLevelName>

<traButton>1</traButton>

<benchMarkMax>0.0</benchMarkMax>

</item>

</list>

</xDataBody>

</CMBC>

2.理财产品历史收益查询 (B2eQryFinanceIncomeR

ateHis)

本部分更新日期:2022-03-16

查询理财产品历史收益率(购买页产品详情查看历史收益)

2.1.请求( B2eQryFinanceIncomeRateHis)

标记

说明

长度

<xDataBody>

 <prdCode>

产品编号（★）

 <startDate> 开始日期（★）：

12 / 32

标记

说明

长度

民生银行银企直联

yyyy-mm-dd，起始日期

不能超过 2 年

 <endDate>

结束日期（★）：

yyyy-mm-dd，查询日期

间隔不能大于 90 天

 <lineType>

类型（★）：

2-历史收益率

</xDataBody>

2.2.响应( B2eQryFinanceIncomeRateHis)

标记

说明

长度

<xDataBody>

服务消息集

 <totalSize>

数据条数

 <list>

列表

  <issDate>

产品生效日期

  <preIncomeRate> 产品收益率

  <NAV>

最新净值

  <totNav>

累计净值

 </list>

</xDataBody>

13 / 32

民生银行银企直联

2.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryFinanceIncomeRateHis">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<prdCode>FGAE000139</prdCode>

<startDate>2021-10-10</startDate>

<endDate>2021-10-27</endDate>

<lineType>2</lineType>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQryFinanceIncomeRateHis"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

14 / 32

民生银行银企直联

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<totalSize>10</totalSize>

<list>

<prdMap>

<issDate>2021-09-27</issDate>

<preIncomeRate></preIncomeRate>

<NAV>1.234567</NAV>

<totNav>1.234567</totNav>

</prdMap>

</list>

</xDataBody>

</CMBC>

3.理财产品基本信息查询 ( B2eQueryPrdBuyInfo)

本部分更新日期:2022-03-16

理财产品基本信息查询（在销详情页面）

3.1.请求( B2eQueryPrdBuyInfo )

标记

说明

长度

<xDataBody>

 <prdCode>

产品编号（★）

</xDataBody>

15 / 32

3.2.响应(B2eQueryPrdBuyInfo )

民生银行银企直联

标记

说明

长度

<xDataBody>

服务消息集

 <prdCode>

产品代码

 <prdName>

产品名称

 <prdType>

产品类型

 <liveTime>

产品期限

 <prdAttrName>

产品类别

 <riskLevel>

风险等级

 <riskLevelName>

风险等级名称

 <incomeRate>

预期收益率/年化收益率

 <warmTipsMap>

温馨提示

  <tipsTitle>

提示抬头

  <tipsContent>

提示内容

 </warmTipsMap>

 <currType>

币种

 <currTypeName>

产品币种

 <ipoStartDate>

募集开始日期：

YYYY-MM-DD

 <ipoEndDate>

募集结束日期：

YYYY-MM-DD

16 / 32

民生银行银企直联

长度

标记

说明

 <startDate>

产品成立日：

YYYY-MM-DD

 <endDate>

产品到期日：

YYYY-MM-DD

 <livTimeUnitName> 投资周期

 <cashDay>

收益兑付日

 <prdNextDate>

下一开放日

 <interstTypeName> 计息模式/计息标准

 <openTime>

开市时间

 <closeTime>

闭市时间

 <redCloseTime>

赎回闭市时间

 <ofirstAmt>

机构最小认购单位

 <omaxAmt>

单笔购买上限

 <odaymax>

当日购买上限

 <ominRed>

最小赎回单位

 <omaxRed>

单笔赎回上限

 <ominHold>

最低持仓份额

 <protocol>

协议:产品说明书、产品合约、风险揭示书

  <fileName>

文件名称

  <fileType>

文件类型：

0：产品说明书，

17 / 32

标记

说明

民生银行银企直联

长度

1：产品合约

2:风险揭示书

  <fileTitle>

文件类型抬头：

产品说明书/产品合约/风险揭示书

  <filePath>

文件下载地址：

文件下载链接

 </protocol>

</xDataBody>

3.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryPrdBuyInfo">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<prdCode>FGAC000003</prdCode>

</xDataBody>

</CMBC>

响应报文

18 / 32

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQueryPrdBuyInfo"

民生银行银企直联

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<prdCode>FGAC000003</prdCode>

<prdName>资产每日浮动 03</prdName>

<prdType>0</prdType>

<prdAttrName>资产类</prdAttrName>

<riskLevel>FGAC121701</riskLevel>

<riskLevelName>中等风险(三级)</riskLevelName>

<incomeRate>0.05</incomeRate>

<prdTypeName>每日型</prdTypeName>

<currType>156</currType>

<currTypeName>人民币</currTypeName>

<openTime>090000</openTime>

<closeTime>180000</closeTime>

<redCloseTime>180000</redCloseTime>

<startDate>2014-11-26</startDate>

<endDate>2099-12-31</endDate>

<ipoStartDate>2014-11-24</ipoStartDate>

<ipoEndDate>2014-11-25</ipoEndDate>

<prdNextDate>2021-10-19</prdNextDate>

<ofirstAmt>0.0</ofirstAmt>

<omaxAmt>0.0</omaxAmt>

19 / 32

民生银行银企直联

<ominRed>0.0</ominRed>

<omaxRed>0.0</omaxRed>

<ominHold>0.0</ominHold>

<odaymax>0.0</odaymax>

<liveTime>0.05</liveTime>

<livTimeUnitName>1 天</livTimeUnitName>

<interstTypeName>ACT/365</interstTypeName>

<cashDay>28</cashDay>

<warmTipsMap>

<tipsTitle>温馨提示</tipsTitle>

<tipsContent>本产品为每日申购赎回型理财产品，在产品存续期内，客户可以在每个

工作日的实时交易时段进行实时购买/实时赎回，或在非工作日、工作日的非实时交易时

段进行约定购买/约定赎回。若客户未赎回全部理财产品份额，则客户剩余理财产品份额

所对应的理财本金自动进入下一工作日参与投资。</tipsContent>

</warmTipsMap>

<protocol>

<item>{fileName=FGAC121701_0_********.pdf, fileTitle=产品说明

书,fileType=0,

filePath=http:*************:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FGA

C121701_0_********.pdf}</item>

<item>{fileName=FGAC121701_1_********.pdf, fileTitle=产品合约,

fileType=1,

filePath=http:*************:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FGA

C121701_1_********.pdf}</item>

<item>{fileName=FGAC121701_2_********.pdf, fileTitle=风险揭示书,

fileType=2,

filePath=http:*************:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FGA

C121701_2_********.pdf}</item>

</protocol>

</xDataBody>

</CMBC>

4.理财历史收益明细查询 (B2eQueryMyFinanceHis)

本部分更新日期:2022-03-16

20 / 32

查询我的历史持仓理财列表

4.1.请求(B2eQueryMyFinanceHis)

民生银行银企直联

标记

说明

长度

<xDataBody>

 <pageNo>

页码

 <pageSize> 页面容量

</xDataBody>

4.2.响应(B2eQueryMyFinanceHis)

只支持 10

或 20 条查

询

标记

说明

长度

<xDataBody>

服务消息集

 <totalSize>

总记录数

 <list>

预期收益率列表

  <prdCode>

产品代码

  <prdName>

产品名称

  <prdType>

产品类型

  <income>

预期未分配收益

  <currType>

币种

  <useVol>

可用份额

21 / 32

民生银行银企直联

长度

标记

  <vol>

说明

份额

  <cost>

累计买入

  <totPrinciple> 累计赎回

  <totIncome>

累计收益

  <bankAcc>

交易账号

 </list>

</xDataBody>

4.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQueryMyFinanceHis">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文

22 / 32

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQueryMyFinanceHis"

民生银行银企直联

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<totalSize></totalSize>

<list>

<prdCode>FSAF12345A</prdCode>

<prdName>uat - 货基 test</prdName>

<prdType>5</prdType>

<income>0.0</income>

<currType>156</currType>

<useVol>0.0</useVol>

<vol>0.0</vol>

<cost>120000.0</cost>

<totPrinciple>0.0</totPrinciple>

<totIncome>18328.77</totIncome>

<bankAcc>*********</bankAcc>

</list>

</xDataBody>

</CMBC>

23 / 32

民生银行银企直联
5.理财持有产品详细信息查询 (B2eQueryPrdDetailI

nfo)

本部分更新日期:2022-03-16

理财持有产品详细信息查询（持仓详情页面）

5.1.请求(B2eQueryPrdDetailInfo)

标记

说明

长度

<xDataBody>

 <prdCode>

产品代码（★）

 <transAccount> 卡号（★）

</xDataBody>

5.2.响应(B2eQueryPrdDetailInfo)

标记

说明

长度

<xDataBody>

服务消息集

 <prdCode>

产品代码

 <prdName>

产品名称

 <bankAcc>

交易账号

 <income>

预期未兑付收益

 <vol>

产品份额

 <useVol>

可用份额

24 / 32

标记

说明

 <trandeFrozen>

交易冻结份额

 <otherFrozen>

长期冻结份额

 <cost>

买入成本（累计买入）

 <totPrinciple>

累计赎回本金（累计赎回）

 <onwayAmt>

在途资金

 <totIncome>

累计收益（累计收益）

 <prdType>

产品类型

 <prdTypeName>

产品类型名称（每日型）

 <prdAttr>

产品属性

 <prdAttrName>

产品属性名称

 <currType>

币种

 <currTypeName>

币种名称

 <ipoStartDate>

募集开始日期

 <ipoEndDate>

募集结束日期

 <startDate>

产品成立日

 <endDate>

产品到期日

 <livTimeUnitName> 产品名义周期

 <cashDay>

收益兑付日

 <workDateList>

开放日列表

民生银行银企直联

长度

25 / 32

民生银行银企直联

长度

标记

说明

 <riskLevel>

风险等级代码

 <riskLevelName>

风险等级名称

 <incomeRate>

年化收益率

 <interestType>

计息模式

 <interstTypeName> 计息模式

 <openTime>

开市时间

 <closeTime>

闭式时间

 <redCloseTime>

赎回闭市时间

 <ofirstAmt>

机构首次最低投资金额

 <osubUnit>

机构最小购买单位

 <ominRed>

机构单笔最小赎回份额

 <omaxAmt>

机构单笔最大购买金额

 <omaxRed>

机构单笔最大赎回份

 <odaymax>

机构当日最高限额

 <ominHold>

机构最低持有份额

</xDataBody>

5.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

26 / 32

trnCode="B2eQueryPrdDetailInfo">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<prdCode>FGAC000003</prdCode>

<transAccount>*********</transAccount>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQueryPrdDetailInfo"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<trnId>********</trnId>

<insId>*********</insId>

<cltcookie>123</cltcookie>

<prdCode>FGAC000003</prdCode>

民生银行银企直联

27 / 32

民生银行银企直联

<prdName>资产每日浮动 03</prdName>

<bankAcc>*********</bankAcc>

<income>147.95</income>

<vol>120000.0</vol>

<useVol>120000.0</useVol>

<trandeFrozen>0.0</trandeFrozen>

<otherFrozen>0.0</otherFrozen>

<cost>120000.0</cost>

<totPrinciple>0.0</totPrinciple>

<onwayAmt>2547.95</onwayAmt>

<totIncome>18739.73</totIncome>

<prdType>0</prdType>

<prdTypeName>每日型</prdTypeName>

<prdAttr>A</prdAttr>

<prdAttrName>资产类</prdAttrName>

<currType>156</currType>

<currTypeName>人民币</currTypeName>先查 tss 维护文件信息 fileInfo，若未

返回，再查后管维护文件内容 content

<ipoEndDate>2014-12-23</ipoEndDate>

<startDate>2014-12-24</startDate>

<endDate>2099-12-31</endDate>

<livTimeUnitName>1 天</livTimeUnitName>

<cashDay>8</cashDay>

<workDateList>

<item>2021-09-16</item>

<item>2021-09-17</item>

<item>2021-09-18</item>

<item>2021-09-22</item>

</workDateList>

<riskLevel>1</riskLevel>

<riskLevelName>低风险(一级)</riskLevelName>

<incomeRate>0.05</incomeRate>

<interestType>1</interestType>

<interstTypeName>ACT/365</interstTypeName>

<openTime>090000</openTime>

<closeTime>180000</closeTime>

<redCloseTime>180000</redCloseTime>

28 / 32

民生银行银企直联

<ofirstAmt>0.0</ofirstAmt>

<osubUnit>0.0</osubUnit>

<ominRed>0.0</ominRed>

<omaxAmt>0.0</omaxAmt>

<omaxRed>0.0</omaxRed>

<odaymax>0.0</odaymax>

<ominHold>0.0</ominHold>

</xDataBody>

</CMBC>

6.理财文件下载 (B2eQryEntPrdProtocol)

本部分更新日期:2022-03-16

查询产品的协议内容

6.1.请求(B2eQryEntPrdProtocol)

标记

说明

长度

<xDataBody>

 <prdCode>

产品码（★）

 <infoType> 文件类型（★） 0：产品说明书、

1：产品合约、

2：风险提示书

</xDataBody>

6.2.响应(B2eQryEntPrdProtocol)

标记

说明

<xDataBody>

服务消息集

长

度

29 / 32

标记

说明

 <fileInfo>

文件信息：

民生银行银企直联

长

度

先查 tss 维护文件信息 fileInfo，若未返回，再查后管维护

文件内容 content

  <fileName> 文件名

  <fileTitle>

文件标题

  <filePath>

文件下载路径

  <fileType> 文件类型

 </fileInfo>

  <content>

协议内容：

先查 tss 维护文件信息 fileInfo，若未返回，再查后管维护

文件内容 content

</xDataBody>

6.3.例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="B2eQryEntPrdProtocol">

<requestHeader>

<dtClient>2008-03-13 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>**********01</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

30 / 32

民生银行银企直联

</requestHeader>

<xDataBody>

<prdCode>FGAC121701</prdCode>

<infoType>1</infoType>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" lang="chs" security="none"

trnCode="B2eQryEntPrdProtocol"

version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2008-09-01 15:14:10</dtServer>

<userKey>N</userKey>

<dtDead>

</dtDead>

<lanaguge>chs</lanaguge>

</responseHeader>

<xDataBody>

<fileInfo>

<fileName>FGAC121701_1_********.pdf</fileName>

<fileTitle>产品合约</fileTitle>

<filePath>http:*************:8001/wwwroot/cmbc/upload/mb/tssInfoFile/FG

AC121701_0_********.pdf</filePath>

</fileInfo>

<content></content>

</xDataBody>

</CMBC>

31 / 32

