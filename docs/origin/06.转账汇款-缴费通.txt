银企直连接口文档

（转账汇款-缴费通）

邮箱：<EMAIL>

民生银行银企直联

版本

日期

说明

编写者 审核者

文档修改记录

V1.0.0 2021-03-

定义接口文档

18

1 / 22

目录

民生银行银企直联

目录 .............................................................................................2

１ 缴费通客户签约信息查询(QRYCUSTOMERDETAIL) .................................3

1.1. 请求(QRYCUSTOMERDETAIL) ........................................................ 3

1.2. 响应(QRYCUSTOMERDETAIL) ........................................................ 4

1.3. 例子 ...................................................................................... 5

２ 缴费通缴费结果查询(QRYPAYMENTRESULT) ....................................... 7

2.1. 请求(QRYPAYMENTRESULT) ......................................................... 7

2.2. 响应(QRYPAYMENTRESULT) ......................................................... 7

３ 充值明细查询(QUERYPAYDTL) ........................................................10

3.1. 请求(QUERYPAYDTL) ............................................................... 11

3.2. 响应(QUERYPAYDTL) ............................................................... 11

3.3. 例子 .................................................................................... 14

４ 缴费通单笔代扣(T+1 入账)(SINGLEFEECOLLECTION) ......................... 15

4.1. 请求(SINGLEFEECOLLECTION) ..................................................... 16

4.2. 响应(SINGLEFEECOLLECTION) ..................................................... 16

4.3. 例子 .................................................................................... 17

５ 缴费通时时扣款实时入账(SINGLEFEECOLLECTIONSYNC) ..................... 18

5.1. 请求(SINGLEFEECOLLECTIONSYNC) .............................................. 19

5.2. 响应(SINGLEFEECOLLECTIONSYNC) .............................................. 19

5.3. 例子 .................................................................................... 20

2 / 22

1. 缴费通客户签约信息查询(QryCusto

民生银行银企直联

merDetail)

本部分更新日期:2021-04-02

根据用户签约号码可查询该号码下的签约和解约收款项目

1.1. 请求(QryCustomerDetail)

标记

说明

长度

<xDataBody>

<trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

64

<pageNo>

页码(★)

<pageSize>

每页条数(★)

<signType>

签约类型(★)（01-签约 02-解约 03-全部）

<payNumber> 缴费号码

<qryStartDate> 查询起始日期((★)

<qryEndDate> 查询结束日期((★)

<extFields>

备用字段（未启用）

</xDataBody>

3

3

2

20

10

10

3 / 22

  
 
 
 
 
 
 
 
 
1.2. 响应(QryCustomerDetail)

民生银行银企直联

标记

说明

<xDataBody>

<trnId>

<List>

<Map>

客户端产生的交易标志，原值返回

<signDate>

签约日期 （yyyy-mm-dd HH:mm:ss）

<payNumber>

客户识别号(缴费号码)

<clientName>

客户名称

<feeProject>

收费项目

<signStatus>

签约状态：（01-签约 02-解约）

<payNumberType> 签约号码类型：（01-客户识别号 02-手机

号 03-身份证号）

<signAccount>

签约账户账号

<merId>

商户号

<merName>

商户名称

<extFields1>

备用字段（未启用）

长

度

20

20

60

255

32

02

64

30

60

4 / 22

  
 
 
  
   
   
   
   
   
   
   
   
   
   
标记

说明

<extFields2>

备用字段（未启用）

民生银行银企直联

长

度

<Map>

<List>

</xDataBody>

1.3. 例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryCustomerDetail">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<signType>01</signType>

<payNumber></payNumber>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

<qryStartDate>2019-04-01</qryStartDate>

<qryEndDate>2019-06-24</qryEndDate>

</xDataBody>

</CMBC>

5 / 22

  
   
  
 
响应报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryCustomerDetail" security="none" lang="chs"

民生银行银企直联

header="100" version="100" >

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-04-24 17:34:43</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<List>

<Map>

<signDate>2019-04-24 11:08:08</signDate>

<payNumber>**********</payNumber>

<clientName>花无缺 1</clientName>

<signStatus>01</signStatus>

<payNumberType>01</payNumberType>

<signAccount>************2256</signAccount>

<merId>01201807301406160011</merId>

<merName>福田法院</merName>

</Map>

</List>

</xDataBody>

</CMBC>

6 / 22

2. 缴费通缴费结果查询(QryPayment

民生银行银企直联

Result)

本部分更新日期:2021-04-02

2.1. 请求(QryPaymentResult)

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识（★）

<insId>

客户端交易的唯一标志（★）

<extFields> 备用字段（未启用）

</xDataBody>

2.2. 响应(QryPaymentResult)

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识（★）

<insId>

客户端交易的唯一标志（★）

长度

64

64

长度

64

64

7 / 22

  
 
 
 
  
 
 
标记

说明

<channelSerialNo> 渠道流水号（★）

<merId>

签约编号（★）

<feeType>

收费类型（★）

民生银行银企直联

长度

32

<paymentType>

缴费号码类型，01-客户识别号（★）

<paymentNo>

第三方业务编号（★）

<orderId>

订单号

<orderType>

交易类型 00-支付 01-退款（★）

<payStatus>

支付状态 01-处理中 02-失败 03-成功 04-未知

（★）

<payTime>

支付时间，yyyyMMddHHmmss（★）

<paymentAmount> 订单金额/支付金额（★）

<payNo>

支付号码（★）

<paymentMode>

支付方式 1-本行缴费 2-本行代扣 3-微信缴费

4-POS 缴费（★）

<billType>

账单类型 1-账单缴费 2-账单充值 3-账单退款

2

2

2

1

1

（★）

<clientName>

客户姓名

<feeProject>

收费项目描述

8 / 22

  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
标记

说明

<payMessage>

支付信息

<extFields>

备用字段（未启用）

民生银行银企直联

长度

</xDataBody>

2.3. 例子

请求报文

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QryPaymentResult">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<insId>2019062507240000002</insId>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QryPaymentResult" security="none" lang="chs"

header="100" version="100" >

<responseHeader>

<status>

<code>0</code>

9 / 22

  
 
 
民生银行银企直联

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-06-13 10:23:50</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<insId>201906110000010</insId>

<merId>01201807301406160011</merId>

<feeType>2</feeType>

<paymentType>01</paymentType>

<paymentNo>**********</paymentNo>

<orderId>31301201906110962508961329900011</orderId>

<payStatus>02</payStatus>

<payTime>20190611180216</payTime>

<paymentAmount>1.00</paymentAmount>

<paymentMode>2</paymentMode>

<billType>2</billType>

<clientName>花无缺 1</clientName>

<feeProject></feeProject>

<payMessage>转账成功</payMessage>

</xDataBody>

</CMBC>

3. 充值明细查询(QueryPayDtl)

本部分更新日期:2021-04-02

10 / 22

3.1. 请求(QueryPayDtl)

民生银行银企直联

标记

说明

长度

<xDataBody>

<trnId>

客户端产生的交易唯一标志（必输，但无作用）(★)

<payNumber> 缴费号码

<qryStartDate> 缴费开始日期(★)（格式：yyyy-MM-dd）

<qryEndDate> 缴费结束日期(★)（格式：yyyy-MM-dd ）

<pageNo>

页码(★)

<pageSize>

查询笔数(★)

<extFields1>

备用字段（未启用）

</xDataBody>

3.2. 响应(QueryPayDtl)

64

20

3

3

标记

说明

长度

<xDataBody>

<trnId>

客户端产生的交易标志，原值返回

<totalCount>

总笔数

<List>

11 / 22

  
 
 
 
 
 
 
 
  
 
 
 
标记

<Map>

说明

<billType>

账单类型：

1-账单缴费

2-账单充值

3-账单退款

<billId>

账单编号

<FeeType>

收费类别：

01-校园缴费

02-企业缴费

03-物业缴费

04-其他缴费

05-党费缴费

<clientName>

客户姓名

<address>

地址

<identityCard>

身份证

<phone>

手机号

<cardNo>

卡号后四位

<amount>

账单金额

<merId>

商户编号

<payNumber>

客户识别号

民生银行银企直联

长度

1

32

2

60

255

18

11

4

20

20

20

12 / 22

  
  
   
   
   
   
   
   
   
   
   
   
   
标记

说明

<feeProject>

收费项目描述

<payBranch>

支付机构

<createDate>

账单创建日期

<writeOffDate>

缴费时间

<companyCodeId> 企业自制编号

<paymentMode>

支付方式：

1-本行缴费

2-本行代扣

3-微信缴费

4-POS 缴费

<secondMerName> 二级商户名称

<payOrderNo>

支付流水号

<effectTime>

账单生效日期

<expireTime>

账单失效日期

<billInfo>

账期或说明

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

<Map>

<List>

民生银行银企直联

长度

255

4

10

20

40

1

60

32

10

10

255

13 / 22

  
   
   
   
   
   
   
   
   
   
   
   
   
   
  
 
标记

说明

民生银行银企直联

长度

</xDataBody>

3.3. 例子

请求报文：

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="QueryPayDtl">

<requestHeader>

<dtClient>2018-12-19 18:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>111111</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>201</appVer>

</requestHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<payNumber>201906240001</payNumber>

<qryStartDate>2019-06-25</qryStartDate>

<qryEndDate>2019-06-25</qryEndDate>

<pageNo>1</pageNo>

<pageSize>10</pageSize>

</xDataBody>

</CMBC>

响应报文:

<?xml version="1.0" encoding="GB2312"?>

<CMBC trnCode="QueryPayDtl" security="none" lang="chs" header="100"

version="100" >

<responseHeader>

<status>

<code>0</code>

14 / 22

  
民生银行银企直联

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-04-24 11:30:43</dtServer>

<userKey>N</userKey>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>vvvvv555</trnId>

<totalCount>1</totalCount>

<List>

<Map>

<billType>2</billType>

<billId>32900201904241119540030820532900</billId>

<FeeType>02</FeeType>

<cardNo>2256</cardNo>

<amount>1.00</amount>

<merId>01201807301406160011</merId>

<payNumber>**********</payNumber>

<payBranch>1800</payBranch>

<createDate>20190424</createDate>

<writeOffDate>2019-04-24 11:19:49</writeOffDate>

<paymentMode>2</paymentMode>

<secondMerName>福田法院</secondMerName>

<payOrderNo>31301201904240961895058329715007</payOrderNo>

</Map>

</List>

</xDataBody>

</CMBC>

4. 缴费通单笔代扣(T+1 入账)(Single

FeeCollection)

本部分更新日期:2021-04-02

15 / 22

4.1. 请求(singleFeeCollection)

民生银行银企直联

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识（★）

<insId>

指令 ID，一条转账指令在客户端的唯一标识

（★）

<merId>

签约编号（★）

<paymentType>

缴费号码类型（★）：01-客户识别号

<paymentNo>

缴费号码（★）

<paymentAmount> 充值金额（★）

<clientName>

客户姓名

<feeProject>

收费项目描述

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

</xDataBody>

4.2. 响应(singleFeeCollection)

标记

说明

长

度

64

64

20

2

20

13,2

150

150

长度

16 / 22

  
 
 
 
 
 
 
 
 
 
 
  
民生银行银企直联

长度

64

64

32

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识（★）

<insId>

客户端交易的唯一标志（★）

<svrId>

客户端交易的唯一标志（★）

<extFields> 备用字段（未启用）

</xDataBody>

4.3. 例子

请求报文

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="SingleFeeCollection">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>1146asd72</trnId>

<cltcookie>609797800</cltcookie>

<insId>20190625072500001</insId>

<merId>01201906241432220001</merId>

<paymentType>01</paymentType>

<paymentNo>201906240001</paymentNo>

<paymentAmount>89.00</paymentAmount>

17 / 22

  
 
 
 
 
民生银行银企直联

<clientName></clientName>

<feeProject></feeProject>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="SingleFeeCollection" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-04-24 17:52:19</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>1146asd72</trnId>

<insId>10sadsdacad143222</insId>

<svrId>31301201905060962021852313000000</svrId>

</xDataBody>

</CMBC>

5. 缴费通时时扣款实时入账(SingleFee

CollectionSync)

本部分更新日期:2021-04-02

18 / 22

5.1. 请求(SingleFeeCollectionSync)

民生银行银企直联

标记

说明

<xDataBody>

<trnId>

客户端的唯一标识（★）

<insId>

指令 ID，一条转账指令在客户端的唯一标识

（★）

<merId>

签约编号（★）

<paymentType>

缴费号码类型（★）：01-客户识别号

<paymentNo>

缴费号码（★）

<paymentAmount> 充值金额（★）

<clientName>

客户姓名

<feeProject>

收费项目描述

<extFields1>

备用字段（未启用）

<extFields2>

备用字段（未启用）

</xDataBody>

5.2. 响应(SingleFeeCollectionSync)

标记

说明

长度

长

度

64

64

20

2

20

13,2

150

150

19 / 22

  
 
 
 
 
 
 
 
 
 
 
  
标记

说明

长度

民生银行银企直联

<xDataBody>

<trnId>

客户端的唯一标识（★）

64

<insId>

客户端交易的唯一标志（★） 64

<svrId>

客户端交易的唯一标志（★） 32

<extFields> 备用字段（未启用）

</xDataBody>

5.3. 例子

请求报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC header="100" version="100" security="none" lang="chs"

trnCode="SingleFeeCollectionSync">

<requestHeader>

<dtClient>2010-03-13 17:44:12</dtClient>

<clientId>**********</clientId>

<userId>*************</userId>

<userPswd>123123</userPswd>

<language>chs</language>

<appId>nsbdes</appId>

<appVer>203</appVer>

</requestHeader>

<xDataBody>

<trnId>1146asd72</trnId>

<cltcookie>609797800</cltcookie>

<insId>2019062507240000002</insId>

<merId>01201906241432220001</merId>

<paymentType>01</paymentType>

<paymentNo>201906240001</paymentNo>

20 / 22

  
 
 
 
 
民生银行银企直联

<paymentAmount>90.00</paymentAmount>

<clientName></clientName>

<feeProject></feeProject>

</xDataBody>

</CMBC>

响应报文

<?xml version="1.0" encoding="GB2312"?>

<CMBC security="none" trnCode="SingleFeeCollectionSync" header="100"

lang="chs" version="100">

<responseHeader>

<status>

<code>0</code>

<severity>info</severity>

<message>ok</message>

</status>

<dtServer>2019-06-21 09:51:52</dtServer>

<userKey>N</userKey>

<dtDead></dtDead>

<language>chs</language>

</responseHeader>

<xDataBody>

<trnId>1146asd72</trnId>

<insId>201906210000001</insId>

<svrId>31301201906210962633870313000000</svrId>

</xDataBody>

</CMBC>

21 / 22

