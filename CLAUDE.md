# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a documentation repository for China Minsheng Bank's Direct Corporate Banking (银企直联) API system. It contains comprehensive API documentation for enterprise banking integrations, including transaction processing, account management, cash management, and financial services.

## Project Structure

```
workspace/
├── docs/
│   ├── origin/          # Original API documentation files
│   ├── clean/           # Cleaned/processed documentation
│   └── used/            # Frequently used documentation
└── .claude/             # Claude-specific configuration
```

## Key Documentation Areas

### Core Banking APIs
- **Transfer Services**: Single and batch transfers, payment processing
- **Account Management**: Balance inquiries, transaction details, statements
- **Cash Management**: Pooling, virtual accounts, fund collection
- **Wealth Management**: Fixed deposits, wealth products, certificates
- **Electronic Bills**: New generation electronic bills, bill management
- **Digital Currency**: Digital RMB (CBDC) integration

### API Communication Protocol
- **Protocol**: HTTP 1.0/1.1 with XML 1.0
- **Content-Type**: `application/x-NS-BDES`
- **Encoding**: UTF-8, GBK, or GB2312 (UTF-8 recommended)
- **Endpoint**: `/eweb/b2e/connect.do`
- **Method**: POST requests only

### Message Structure
```xml
<?xml version="1.0" encoding="UTF-8"?>
<CMBC header="100" version="100" security="none" lang="UTF-8" trnCode="[transaction_code]">
  <requestHeader>
    <dtClient>YYYY-MM-DD HH:MM:SS</dtClient>
    <clientId>[enterprise_id]</clientId>
    <userId>[operator_id]</userId>
    <userPswd>[password]</userPswd>
    <language>UTF-8</language>
    <appId>nsbdes</appId>
    <appVer>201</appVer>
  </requestHeader>
  <xDataBody>
    <trnId>[technical_sequence]</trnId>
    <insId>[business_sequence]</insId>
    [transaction_specific_data]
  </xDataBody>
</CMBC>
```

## Common Transaction Codes

### Account Operations
- `qryBalNew` - Balance inquiry
- `qryTrsDtl` - Transaction details
- `qryHistoryBal` - Historical balance
- `qryAcctList` - Account list query

### Transfer Operations
- `Xfer` - Single transfer
- `batchXfer` - Batch transfer
- `TransferXfer` - Transfer with draft
- `qryXfer` - Transfer result query

### Cash Management
- `B2EVirtualAcctMngt` - Virtual account management
- `B2ECashPoolUpDown` - Cash pool up/down transfer
- `CashPoolTransDetailQry` - Cash pool transaction details

## Development Commands

### Documentation Processing
```bash
# Check documentation structure
ls -la docs/

# View specific documentation
cat docs/origin/01.银企直联接口文档公共说明.txt

# Compare cleaned vs original documentation
diff docs/origin/01.银企直联接口文档公共说明.txt docs/clean/01.银企直联接口文档公共说明.txt
```

### File Encoding Handling
```bash
# Check file encoding
file -I docs/origin/*.txt

# Convert encoding if needed
iconv -f GB2312 -t UTF-8 input.txt > output.txt
```

## Key Contacts
- **Email**: <EMAIL>
- **Phone**: 010-********

## Testing Requirements
- CFCA digital certificate required
- Test environment access via enterprise application
- Security proxy logs required for test reports
- All transactions must have unique trnId values

## Common Error Codes
- `0`: Success
- `DM004006`: Account not found
- `WEC32`: Query condition error
- `WEC02`: Network exception during transfer
- `E1602`: Transaction not accepted by bank