"""
民生银行银企直联 Python SDK 客户端
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
import requests
import hashlib
import base64
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
import logging

from .models import *
from .exceptions import *


class CMBCClient:
    """民生银行银企直联客户端"""
    
    def __init__(self, 
                 client_id: str,
                 user_id: str, 
                 user_password: str,
                 app_id: str = "nsbdes",
                 app_version: str = "201",
                 base_url: str = "https://corporbank.cmbc.com.cn",
                 timeout: int = 30,
                 debug: bool = False):
        """
        初始化客户端
        
        Args:
            client_id: 客户号
            user_id: 用户ID
            user_password: 用户密码
            app_id: 应用ID
            app_version: 应用版本
            base_url: 服务器地址
            timeout: 超时时间（秒）
            debug: 是否开启调试模式
        """
        self.client_id = client_id
        self.user_id = user_id
        self.user_password = user_password
        self.app_id = app_id
        self.app_version = app_version
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.debug = debug
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        if debug:
            logging.basicConfig(level=logging.DEBUG)
            
        # 会话信息
        self.session = requests.Session()
        self.user_key = None
        
    def _build_request_xml(self, trn_code: str, data_body: Dict[str, Any]) -> str:
        """构建请求XML"""
        root = ET.Element("CMBC")
        root.set("header", "100")
        root.set("version", "100") 
        root.set("security", "none")
        root.set("lang", "chs")
        root.set("trnCode", trn_code)
        
        # 请求头
        request_header = ET.SubElement(root, "requestHeader")
        ET.SubElement(request_header, "dtClient").text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ET.SubElement(request_header, "clientId").text = self.client_id
        ET.SubElement(request_header, "userId").text = self.user_id
        ET.SubElement(request_header, "userPswd").text = self.user_password
        ET.SubElement(request_header, "language").text = "UTF-8"
        ET.SubElement(request_header, "appId").text = self.app_id
        ET.SubElement(request_header, "appVer").text = self.app_version
        
        # 数据体
        x_data_body = ET.SubElement(root, "xDataBody")
        self._dict_to_xml(data_body, x_data_body)
        
        # 格式化XML
        rough_string = ET.tostring(root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ", encoding=None).replace('<?xml version="1.0" ?>\n', 
                                                                        '<?xml version="1.0" encoding="GB2312"?>\n')
    
    def _dict_to_xml(self, data: Dict[str, Any], parent: ET.Element):
        """将字典转换为XML元素"""
        for key, value in data.items():
            if isinstance(value, dict):
                child = ET.SubElement(parent, key)
                self._dict_to_xml(value, child)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        child = ET.SubElement(parent, key)
                        self._dict_to_xml(item, child)
                    else:
                        ET.SubElement(parent, key).text = str(item) if item is not None else ""
            else:
                ET.SubElement(parent, key).text = str(value) if value is not None else ""
    
    def _parse_response_xml(self, xml_str: str) -> Dict[str, Any]:
        """解析响应XML"""
        try:
            root = ET.fromstring(xml_str)
            result = {}
            
            # 解析响应头
            response_header = root.find("responseHeader")
            if response_header is not None:
                result["responseHeader"] = self._xml_to_dict(response_header)
                
            # 解析数据体
            x_data_body = root.find("xDataBody")
            if x_data_body is not None:
                result["xDataBody"] = self._xml_to_dict(x_data_body)
                
            return result
        except ET.ParseError as e:
            raise CMBCException(f"XML解析失败: {e}")
    
    def _xml_to_dict(self, element: ET.Element) -> Dict[str, Any]:
        """将XML元素转换为字典"""
        result = {}
        
        for child in element:
            if len(child) == 0:
                # 叶子节点
                result[child.tag] = child.text
            else:
                # 有子节点
                if child.tag in result:
                    # 已存在同名节点，转换为列表
                    if not isinstance(result[child.tag], list):
                        result[child.tag] = [result[child.tag]]
                    result[child.tag].append(self._xml_to_dict(child))
                else:
                    result[child.tag] = self._xml_to_dict(child)
                    
        return result
    
    def _send_request(self, trn_code: str, data_body: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求"""
        xml_request = self._build_request_xml(trn_code, data_body)
        
        if self.debug:
            self.logger.debug(f"请求XML: {xml_request}")
            
        try:
            response = self.session.post(
                f"{self.base_url}/corporbank/servlet/BankXMLServlet",
                data=xml_request.encode('gb2312'),
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded; charset=GB2312',
                    'User-Agent': 'CMBC-SDK/1.0'
                },
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # 解析响应
            response_data = self._parse_response_xml(response.text)
            
            if self.debug:
                self.logger.debug(f"响应数据: {response_data}")
                
            # 检查业务错误
            exception = create_exception_from_response(response_data)
            if exception:
                raise exception
                
            return response_data
            
        except requests.RequestException as e:
            raise CMBCNetworkException(f"网络请求失败: {e}")
        except Exception as e:
            if isinstance(e, CMBCException):
                raise
            raise CMBCException(f"请求处理失败: {e}")
    
    # 薪福通相关接口
    def query_signed_accounts(self, ac_permit: str = "2") -> List[Dict[str, Any]]:
        """
        查询签约账号列表
        
        Args:
            ac_permit: 账户权限 1:查询权限 2:查询并转账
            
        Returns:
            签约账号列表
        """
        data_body = {"acPermit": ac_permit}
        response = self._send_request("B2eQryCustomerSignInfoList", data_body)
        
        sign_ac_no_list = response.get("xDataBody", {}).get("signAcNoList", [])
        if isinstance(sign_ac_no_list, dict):
            sign_ac_no_list = [sign_ac_no_list]
            
        return sign_ac_no_list
    
    def query_virtual_accounts(self, customer_sign_id: str, account_no: str) -> Dict[str, Any]:
        """
        查询项目子账簿与特殊子账簿列表
        
        Args:
            customer_sign_id: 客户签约编码
            account_no: 实账户账号
            
        Returns:
            子账簿信息
        """
        data_body = {
            "customerSignId": customer_sign_id,
            "accountNo": account_no
        }
        response = self._send_request("B2eQryAllVirtualAcNoBySignNo", data_body)
        return response.get("xDataBody", {})
    
    def query_virtual_account_balance(self, customer_sign_id: str, vacct_no: str) -> Decimal:
        """
        查询子账簿余额
        
        Args:
            customer_sign_id: 客户签约编码
            vacct_no: 子账户
            
        Returns:
            子账户余额
        """
        data_body = {
            "customerSignId": customer_sign_id,
            "vacctNo": vacct_no
        }
        response = self._send_request("B2eQrySalaryVacctBalance", data_body)
        balance_str = response.get("xDataBody", {}).get("vacctBalance", "0")
        return Decimal(balance_str)
    
    def create_virtual_account(self, customer_sign_id: str, acct_no: str, 
                             drawee_acct_name: str, vacct_busi_name: str,
                             is_trans_approve: str = "0",
                             contacts_name: Optional[str] = None,
                             contacts_mobile_no: Optional[str] = None,
                             customer_register_no: Optional[str] = None,
                             customer_register_name: Optional[str] = None) -> Dict[str, str]:
        """
        新增子账簿
        
        Args:
            customer_sign_id: 客户签约编码
            acct_no: 签约实账户账号
            drawee_acct_name: 用工单位付款名称
            vacct_busi_name: 用工简称
            is_trans_approve: 是否用工单位审批 0:否 1:是
            contacts_name: 联系人名称
            contacts_mobile_no: 用工单位联系人手机号
            customer_register_no: 现金盈客户注册号
            customer_register_name: 客户名称
            
        Returns:
            包含vacctNo和vacctName的字典
        """
        data_body = {
            "customerSignId": customer_sign_id,
            "acctNo": acct_no,
            "draweeAcctName": drawee_acct_name,
            "vacctBusiName": vacct_busi_name,
            "isTransApprove": is_trans_approve
        }
        
        if contacts_name:
            data_body["contactsName"] = contacts_name
        if contacts_mobile_no:
            data_body["contactsMobileNo"] = contacts_mobile_no
        if customer_register_no:
            data_body["customerRegisterNo"] = customer_register_no
        if customer_register_name:
            data_body["customerRegisterName"] = customer_register_name
            
        response = self._send_request("B2eVirtualAccountAdd", data_body)
        return response.get("xDataBody", {})

    def single_transfer(self, customer_sign_id: str, drawee_vacct_no: str,
                       payer_acct: str, payer_name: str, payee_acct: str,
                       payee_name: str, amount: Decimal, currency: str = "RMB",
                       remark: Optional[str] = None) -> Dict[str, Any]:
        """
        子账簿单笔出金

        Args:
            customer_sign_id: 客户签约编码
            drawee_vacct_no: 付款子账户
            payer_acct: 付款账户
            payer_name: 付款人名称
            payee_acct: 收款账户
            payee_name: 收款人名称
            amount: 转账金额
            currency: 币种
            remark: 备注

        Returns:
            转账结果
        """
        data_body = {
            "CustomerSignId": customer_sign_id,
            "DraweeVacctNo": drawee_vacct_no,
            "payerAcct": payer_acct,
            "payerName": payer_name,
            "payeeAcct": payee_acct,
            "payeeName": payee_name,
            "amount": str(amount),
            "currency": currency
        }

        if remark:
            data_body["Remark"] = remark

        response = self._send_request("B2eSalaryMgnTransfer", data_body)
        return response.get("xDataBody", {})

    def batch_transfer(self, customer_sign_id: str, drawee_vacct_no: str,
                      payer_acct: str, payer_name: str,
                      transfer_items: List[BatchTransferItem],
                      usage: Optional[str] = None) -> Dict[str, Any]:
        """
        子账簿批量出金

        Args:
            customer_sign_id: 客户签约编码
            drawee_vacct_no: 付款子账户
            payer_acct: 付款账户
            payer_name: 付款人名称
            transfer_items: 转账明细列表
            usage: 用途

        Returns:
            批量转账结果
        """
        total_amount = sum(item.amount for item in transfer_items)
        total_count = len(transfer_items)

        # 构建文件内容
        file_content_lines = []
        for item in transfer_items:
            file_content_lines.append(item.to_file_content_line())
        file_content = "^".join(file_content_lines)

        data_body = {
            "CustomerSignId": customer_sign_id,
            "DraweeVacctNo": drawee_vacct_no,
            "payerAcct": payer_acct,
            "payerName": payer_name,
            "totNum": str(total_count),
            "totAmt": str(total_amount),
            "fileContentType": "1",  # 明文
            "fileContent": file_content
        }

        if usage:
            data_body["usage"] = usage

        response = self._send_request("B2eSalaryMgnBatchTransfer", data_body)
        return response.get("xDataBody", {})

    def virtual_account_transfer(self, customer_sign_id: str, drawee_vacct_no: str,
                               payer_acct: str, payer_name: str, payee_acct: str,
                               payee_name: str, amount: Decimal, currency: str = "RMB",
                               remark: Optional[str] = None) -> Dict[str, Any]:
        """
        子账簿间转账

        Args:
            customer_sign_id: 客户签约编码
            drawee_vacct_no: 付款子账户
            payer_acct: 付款账户
            payer_name: 付款人名称
            payee_acct: 收款账户
            payee_name: 收款人名称
            amount: 转账金额
            currency: 币种
            remark: 备注

        Returns:
            转账结果
        """
        data_body = {
            "CustomerSignId": customer_sign_id,
            "DraweeVacctNo": drawee_vacct_no,
            "payerAcct": payer_acct,
            "payerName": payer_name,
            "payeeAcct": payee_acct,
            "payeeName": payee_name,
            "amount": str(amount),
            "currency": currency
        }

        if remark:
            data_body["Remark"] = remark

        response = self._send_request("B2eVirtualAccountEachTransfer", data_body)
        return response.get("xDataBody", {})

    def query_transfer_detail(self, customer_sign_id: str, vacct_no: str,
                            date_from: str, date_to: str, page_no: int = 1,
                            page_size: int = 50) -> Dict[str, Any]:
        """
        查询子账簿交易明细

        Args:
            customer_sign_id: 客户签约编码
            vacct_no: 子账簿账号
            date_from: 开始日期(yyyyMMdd)
            date_to: 结束日期(yyyyMMdd)
            page_no: 当前页
            page_size: 查询行数

        Returns:
            交易明细
        """
        data_body = {
            "customerSignId": customer_sign_id,
            "vacctNo": vacct_no,
            "dateFrom": date_from,
            "dateTo": date_to,
            "pageNo": str(page_no),
            "pageSize": str(page_size)
        }

        response = self._send_request("B2eVirtualAccountTransferDetail", data_body)
        return response.get("xDataBody", {})

    def query_batch_transfer_list(self, customer_sign_id: str, drawee_vacct_no: str,
                                date_from: str, date_to: str, page_no: int = 1,
                                page_size: int = 50) -> Dict[str, Any]:
        """
        查询子账簿批量出金结果

        Args:
            customer_sign_id: 客户签约编码
            drawee_vacct_no: 付款子账户
            date_from: 开始日期(yyyyMMdd)
            date_to: 结束日期(yyyyMMdd)
            page_no: 当前页
            page_size: 查询行数

        Returns:
            批量出金结果列表
        """
        data_body = {
            "customerSignId": customer_sign_id,
            "draweeVacctNo": drawee_vacct_no,
            "dateFrom": date_from,
            "dateTo": date_to,
            "pageNo": str(page_no),
            "pageSize": str(page_size)
        }

        response = self._send_request("B2eQrySalaryMgnBatchList", data_body)
        return response.get("xDataBody", {})

    def query_batch_transfer_detail(self, customer_sign_id: str, apply_id: str,
                                  trans_status: str, page_no: int = 1,
                                  page_size: int = 50) -> Dict[str, Any]:
        """
        查询子账簿批量转账明细

        Args:
            customer_sign_id: 客户签约编码
            apply_id: 原请求流水号
            trans_status: 交易状态
            page_no: 当前页
            page_size: 查询条数

        Returns:
            批量转账明细
        """
        data_body = {
            "customerSignId": customer_sign_id,
            "applyId": apply_id,
            "transStatus": trans_status,
            "pageNo": str(page_no),
            "pageSize": str(page_size)
        }

        response = self._send_request("B2eQrySalaryMgnBatchTransferDetail", data_body)
        return response.get("xDataBody", {})

    # 高级业务方法
    def expense_reimbursement(self, customer_sign_id: str, drawee_vacct_no: str,
                            payer_acct: str, payer_name: str,
                            reimbursement_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        费用报销

        Args:
            customer_sign_id: 客户签约编码
            drawee_vacct_no: 付款子账户
            payer_acct: 付款账户
            payer_name: 付款人名称
            reimbursement_items: 报销明细列表，每项包含:
                - payee_acct_no: 收款账号
                - payee_acct_name: 收款户名
                - amount: 金额
                - purpose: 用途代码（参考PaymentPurpose类）
                - remark: 备注
                - bank_flag: 本他行标志 0:同行 1:他行
                - bank_name: 收款行行名（他行时必填）
                - bank_code: 收款行行号（他行时必填）

        Returns:
            报销结果
        """
        transfer_items = []
        for item in reimbursement_items:
            transfer_item = BatchTransferItem(
                payee_acct_no=item['payee_acct_no'],
                payee_acct_name=item['payee_acct_name'],
                amount=Decimal(str(item['amount'])),
                purpose=item.get('purpose', PaymentPurpose.DAILY_REIMBURSEMENT),
                remark=item.get('remark', ''),
                bank_flag=item.get('bank_flag', '0'),
                bank_name=item.get('bank_name'),
                bank_code=item.get('bank_code'),
                route=item.get('route', '9')  # 自动计算汇路
            )
            transfer_items.append(transfer_item)

        return self.batch_transfer(
            customer_sign_id=customer_sign_id,
            drawee_vacct_no=drawee_vacct_no,
            payer_acct=payer_acct,
            payer_name=payer_name,
            transfer_items=transfer_items,
            usage="费用报销"
        )

    def salary_payment(self, customer_sign_id: str, drawee_vacct_no: str,
                      payer_acct: str, payer_name: str,
                      salary_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        代发工资

        Args:
            customer_sign_id: 客户签约编码
            drawee_vacct_no: 付款子账户
            payer_acct: 付款账户
            payer_name: 付款人名称
            salary_items: 工资明细列表，每项包含:
                - payee_acct_no: 收款账号
                - payee_acct_name: 收款户名
                - amount: 金额
                - purpose: 用途代码（参考SalaryPurpose类）
                - remark: 备注
                - bank_flag: 本他行标志 0:同行 1:他行
                - bank_name: 收款行行名（他行时必填）
                - bank_code: 收款行行号（他行时必填）

        Returns:
            工资发放结果
        """
        transfer_items = []
        for item in salary_items:
            transfer_item = BatchTransferItem(
                payee_acct_no=item['payee_acct_no'],
                payee_acct_name=item['payee_acct_name'],
                amount=Decimal(str(item['amount'])),
                purpose=item.get('purpose', SalaryPurpose.SALARY),
                remark=item.get('remark', ''),
                bank_flag=item.get('bank_flag', '0'),
                bank_name=item.get('bank_name'),
                bank_code=item.get('bank_code'),
                route=item.get('route', '9')  # 自动计算汇路
            )
            transfer_items.append(transfer_item)

        return self.batch_transfer(
            customer_sign_id=customer_sign_id,
            drawee_vacct_no=drawee_vacct_no,
            payer_acct=payer_acct,
            payer_name=payer_name,
            transfer_items=transfer_items,
            usage="代发工资"
        )

    def transfer_between_accounts(self, customer_sign_id: str, from_vacct_no: str,
                                to_vacct_no: str, amount: Decimal,
                                remark: Optional[str] = None) -> Dict[str, Any]:
        """
        子账簿间转账

        Args:
            customer_sign_id: 客户签约编码
            from_vacct_no: 转出子账户
            to_vacct_no: 转入子账户
            amount: 转账金额
            remark: 备注

        Returns:
            转账结果
        """
        # 先查询转出账户信息
        virtual_accounts = self.query_virtual_accounts(customer_sign_id, from_vacct_no)

        # 获取实账户信息
        signed_accounts = self.query_signed_accounts()
        if not signed_accounts:
            raise CMBCBusinessException("未找到签约账户")

        payer_acct = signed_accounts[0]['acctNo']
        payer_name = signed_accounts[0]['acctName']

        return self.virtual_account_transfer(
            customer_sign_id=customer_sign_id,
            drawee_vacct_no=from_vacct_no,
            payer_acct=payer_acct,
            payer_name=payer_name,
            payee_acct=to_vacct_no,
            payee_name="子账簿转账",
            amount=amount,
            remark=remark
        )

    def get_account_balance(self, customer_sign_id: str, vacct_no: str) -> Decimal:
        """
        获取账户余额

        Args:
            customer_sign_id: 客户签约编码
            vacct_no: 子账户号

        Returns:
            账户余额
        """
        return self.query_virtual_account_balance(customer_sign_id, vacct_no)

    def get_transaction_history(self, customer_sign_id: str, vacct_no: str,
                              start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        获取交易历史

        Args:
            customer_sign_id: 客户签约编码
            vacct_no: 子账户号
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)

        Returns:
            交易历史列表
        """
        result = self.query_transfer_detail(
            customer_sign_id=customer_sign_id,
            vacct_no=vacct_no,
            date_from=start_date,
            date_to=end_date
        )

        transactions = result.get('list', [])
        if isinstance(transactions, dict):
            transactions = [transactions]

        return transactions
