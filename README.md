# 民生银行银企直联 Python SDK

这是一个用于民生银行银企直联系统的Python SDK，支持报销、转账、发工资等操作。

## 功能特性

- ✅ 签约账户查询
- ✅ 子账簿管理（查询、创建、余额查询）
- ✅ 单笔转账
- ✅ 批量转账
- ✅ 费用报销
- ✅ 代发工资
- ✅ 子账簿间转账
- ✅ 交易明细查询
- ✅ 批量转账结果查询
- ✅ 完整的错误处理和异常管理
- ✅ 调试模式支持

## 安装

```bash
pip install requests
```

将SDK代码复制到你的项目中：

```
your_project/
├── cmbc_sdk/
│   ├── __init__.py
│   ├── client.py
│   ├── models.py
│   └── exceptions.py
└── your_code.py
```

## 快速开始

### 1. 初始化客户端

```python
from cmbc_sdk import CMBCClient

client = CMBCClient(
    client_id="your_client_id",      # 客户号
    user_id="your_user_id",          # 用户ID
    user_password="your_password",    # 用户密码
    debug=True                       # 开启调试模式（可选）
)
```

### 2. 查询签约账户

```python
# 查询签约账户列表
signed_accounts = client.query_signed_accounts()
for account in signed_accounts:
    print(f"账户: {account['acctNo']} - {account['acctName']}")
```

### 3. 费用报销

```python
from decimal import Decimal
from cmbc_sdk import PaymentPurpose

# 准备报销明细
reimbursement_items = [
    {
        'payee_acct_no': '6228480018536951272',
        'payee_acct_name': '张三',
        'amount': Decimal('1000.00'),
        'purpose': PaymentPurpose.TRAVEL_EXPENSE,  # 差旅费
        'remark': '出差费用报销',
        'bank_flag': '1',  # 他行
        'bank_name': '中国工商银行股份有限公司北京东铁匠营支行',
        'bank_code': '************'
    }
]

# 执行报销
result = client.expense_reimbursement(
    customer_sign_id="your_customer_sign_id",
    drawee_vacct_no="your_vacct_no",
    payer_acct="your_payer_account",
    payer_name="your_company_name",
    reimbursement_items=reimbursement_items
)
```

### 4. 代发工资

```python
from cmbc_sdk import SalaryPurpose

# 准备工资明细
salary_items = [
    {
        'payee_acct_no': '6228480018536951274',
        'payee_acct_name': '王五',
        'amount': Decimal('5000.00'),
        'purpose': SalaryPurpose.SALARY,  # 工资
        'remark': '2024年1月工资',
        'bank_flag': '1',  # 他行
        'bank_name': '中国工商银行股份有限公司北京东铁匠营支行',
        'bank_code': '************'
    }
]

# 执行工资发放
result = client.salary_payment(
    customer_sign_id="your_customer_sign_id",
    drawee_vacct_no="your_vacct_no",
    payer_acct="your_payer_account",
    payer_name="your_company_name",
    salary_items=salary_items
)
```

### 5. 单笔转账

```python
result = client.single_transfer(
    customer_sign_id="your_customer_sign_id",
    drawee_vacct_no="your_vacct_no",
    payer_acct="your_payer_account",
    payer_name="your_company_name",
    payee_acct='6228480018536951276',
    payee_name='收款人姓名',
    amount=Decimal('100.00'),
    remark='转账备注'
)
```

### 6. 查询账户余额

```python
balance = client.get_account_balance(
    customer_sign_id="your_customer_sign_id",
    vacct_no="your_vacct_no"
)
print(f"账户余额: {balance}")
```

### 7. 查询交易历史

```python
transactions = client.get_transaction_history(
    customer_sign_id="your_customer_sign_id",
    vacct_no="your_vacct_no",
    start_date='********',  # YYYYMMDD格式
    end_date='********'
)

for trans in transactions:
    print(f"交易日期: {trans.get('transDate')}, "
          f"金额: {trans.get('creditAmount') or trans.get('debitAmount')}, "
          f"摘要: {trans.get('summary')}")
```

## 用途代码

### 费用报销用途代码 (PaymentPurpose)

```python
from cmbc_sdk import PaymentPurpose

PaymentPurpose.TRAVEL_EXPENSE      # 差旅费
PaymentPurpose.OFFICE_EXPENSE      # 办公费
PaymentPurpose.UTILITY_EXPENSE     # 水电费
PaymentPurpose.COMMUNICATION_EXPENSE # 通讯费
PaymentPurpose.TRANSPORT_EXPENSE   # 交通费
PaymentPurpose.MEAL_EXPENSE        # 餐费
PaymentPurpose.DAILY_REIMBURSEMENT # 日常报销费用
# ... 更多用途代码请参考 models.py
```

### 代发工资用途代码 (SalaryPurpose)

```python
from cmbc_sdk import SalaryPurpose

SalaryPurpose.SALARY              # 工资
SalaryPurpose.BONUS               # 奖金
SalaryPurpose.HOLIDAY_FEE         # 过节费
SalaryPurpose.ALLOWANCE           # 补贴
SalaryPurpose.SERVICE_FEE         # 劳务费
SalaryPurpose.PERFORMANCE_SALARY  # 绩效工资
# ... 更多用途代码请参考 models.py
```

## 异常处理

```python
from cmbc_sdk.exceptions import (
    CMBCException, 
    CMBCAuthException, 
    CMBCNetworkException, 
    CMBCBusinessException
)

try:
    result = client.expense_reimbursement(...)
except CMBCAuthException as e:
    print(f"认证失败: {e}")
except CMBCNetworkException as e:
    print(f"网络错误: {e}")
except CMBCBusinessException as e:
    print(f"业务错误: {e}")
except CMBCException as e:
    print(f"其他错误: {e}")
```

## 完整示例

请参考 `examples/basic_usage.py` 文件，其中包含了所有主要功能的使用示例。

## 注意事项

1. **安全性**: 请妥善保管您的客户号、用户ID和密码，不要在代码中硬编码敏感信息
2. **测试环境**: 建议先在测试环境中验证所有功能
3. **错误处理**: 请务必添加适当的异常处理逻辑
4. **日志记录**: 生产环境中建议关闭调试模式，并添加适当的日志记录
5. **金额精度**: 使用 `Decimal` 类型处理金额，避免浮点数精度问题

## 支持的银行

目前支持民生银行银企直联系统，基于以下接口文档：
- 银企直联接口文档公共说明
- 转账汇款-转账基础服务
- 银企直联薪福通

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个SDK。

## 联系方式

如有问题，请联系：<EMAIL>
