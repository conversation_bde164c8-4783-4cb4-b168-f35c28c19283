"""
民生银行银企直联 SDK 基本使用示例
"""

from decimal import Decimal
from cmbc_sdk import CMBCClient, PaymentPurpose, SalaryPurpose
from cmbc_sdk.exceptions import CMBCException


def main():
    # 初始化客户端
    client = CMBCClient(
        client_id="**********",  # 客户号
        user_id="**********605",  # 用户ID
        user_password="your_password",  # 用户密码
        debug=True  # 开启调试模式
    )
    
    try:
        # 1. 查询签约账户
        print("=== 查询签约账户 ===")
        signed_accounts = client.query_signed_accounts()
        print(f"签约账户数量: {len(signed_accounts)}")
        for account in signed_accounts:
            print(f"账户: {account['acctNo']} - {account['acctName']}")
        
        if not signed_accounts:
            print("未找到签约账户")
            return
            
        # 使用第一个签约账户
        main_account = signed_accounts[0]
        customer_sign_id = main_account['customerSignId']
        account_no = main_account['acctNo']
        account_name = main_account['acctName']
        
        print(f"使用账户: {account_no} - {account_name}")
        
        # 2. 查询子账簿
        print("\n=== 查询子账簿 ===")
        virtual_accounts = client.query_virtual_accounts(customer_sign_id, account_no)
        
        vacct_list = virtual_accounts.get('vacctNoList', [])
        if isinstance(vacct_list, dict):
            vacct_list = [vacct_list]
            
        print(f"子账簿数量: {len(vacct_list)}")
        for vacct in vacct_list:
            print(f"子账簿: {vacct['vacctNo']} - {vacct['vacctBusiName']}")
        
        if not vacct_list:
            print("未找到子账簿，创建一个新的子账簿")
            # 创建子账簿
            new_vacct = client.create_virtual_account(
                customer_sign_id=customer_sign_id,
                acct_no=account_no,
                drawee_acct_name="测试公司",
                vacct_busi_name="测试子账簿",
                is_trans_approve="0",
                contacts_name="张三",
                contacts_mobile_no="***********"
            )
            print(f"创建子账簿成功: {new_vacct}")
            vacct_no = new_vacct['vacctNo']
        else:
            vacct_no = vacct_list[0]['vacctNo']
        
        # 3. 查询子账簿余额
        print(f"\n=== 查询子账簿余额 ===")
        balance = client.get_account_balance(customer_sign_id, vacct_no)
        print(f"子账簿 {vacct_no} 余额: {balance}")
        
        # 4. 费用报销示例
        print(f"\n=== 费用报销示例 ===")
        reimbursement_items = [
            {
                'payee_acct_no': '6228480018536951272',
                'payee_acct_name': '张三',
                'amount': Decimal('1000.00'),
                'purpose': PaymentPurpose.TRAVEL_EXPENSE,
                'remark': '出差费用报销',
                'bank_flag': '1',  # 他行
                'bank_name': '中国工商银行股份有限公司北京东铁匠营支行',
                'bank_code': '************'
            },
            {
                'payee_acct_no': '6228480018536951273',
                'payee_acct_name': '李四',
                'amount': Decimal('500.00'),
                'purpose': PaymentPurpose.OFFICE_EXPENSE,
                'remark': '办公用品采购',
                'bank_flag': '1',  # 他行
                'bank_name': '中国工商银行股份有限公司北京东铁匠营支行',
                'bank_code': '************'
            }
        ]
        
        try:
            reimbursement_result = client.expense_reimbursement(
                customer_sign_id=customer_sign_id,
                drawee_vacct_no=vacct_no,
                payer_acct=account_no,
                payer_name=account_name,
                reimbursement_items=reimbursement_items
            )
            print(f"费用报销提交成功: {reimbursement_result}")
        except CMBCException as e:
            print(f"费用报销失败: {e}")
        
        # 5. 代发工资示例
        print(f"\n=== 代发工资示例 ===")
        salary_items = [
            {
                'payee_acct_no': '6228480018536951274',
                'payee_acct_name': '王五',
                'amount': Decimal('5000.00'),
                'purpose': SalaryPurpose.SALARY,
                'remark': '2024年1月工资',
                'bank_flag': '1',  # 他行
                'bank_name': '中国工商银行股份有限公司北京东铁匠营支行',
                'bank_code': '************'
            },
            {
                'payee_acct_no': '6228480018536951275',
                'payee_acct_name': '赵六',
                'amount': Decimal('6000.00'),
                'purpose': SalaryPurpose.SALARY,
                'remark': '2024年1月工资',
                'bank_flag': '1',  # 他行
                'bank_name': '中国工商银行股份有限公司北京东铁匠营支行',
                'bank_code': '************'
            }
        ]
        
        try:
            salary_result = client.salary_payment(
                customer_sign_id=customer_sign_id,
                drawee_vacct_no=vacct_no,
                payer_acct=account_no,
                payer_name=account_name,
                salary_items=salary_items
            )
            print(f"代发工资提交成功: {salary_result}")
        except CMBCException as e:
            print(f"代发工资失败: {e}")
        
        # 6. 单笔转账示例
        print(f"\n=== 单笔转账示例 ===")
        try:
            transfer_result = client.single_transfer(
                customer_sign_id=customer_sign_id,
                drawee_vacct_no=vacct_no,
                payer_acct=account_no,
                payer_name=account_name,
                payee_acct='6228480018536951276',
                payee_name='测试收款人',
                amount=Decimal('100.00'),
                remark='测试转账'
            )
            print(f"单笔转账成功: {transfer_result}")
        except CMBCException as e:
            print(f"单笔转账失败: {e}")
        
        # 7. 查询交易历史
        print(f"\n=== 查询交易历史 ===")
        try:
            transactions = client.get_transaction_history(
                customer_sign_id=customer_sign_id,
                vacct_no=vacct_no,
                start_date='********',
                end_date='********'
            )
            print(f"交易记录数量: {len(transactions)}")
            for trans in transactions[:5]:  # 只显示前5条
                print(f"交易日期: {trans.get('transDate')}, "
                      f"金额: {trans.get('creditAmount') or trans.get('debitAmount')}, "
                      f"摘要: {trans.get('summary')}")
        except CMBCException as e:
            print(f"查询交易历史失败: {e}")
        
        # 8. 查询批量转账结果
        print(f"\n=== 查询批量转账结果 ===")
        try:
            batch_list = client.query_batch_transfer_list(
                customer_sign_id=customer_sign_id,
                drawee_vacct_no=vacct_no,
                date_from='********',
                date_to='********'
            )
            
            batch_items = batch_list.get('list', [])
            if isinstance(batch_items, dict):
                batch_items = [batch_items]
                
            print(f"批量转账记录数量: {len(batch_items)}")
            for batch in batch_items[:3]:  # 只显示前3条
                print(f"申请ID: {batch.get('applyId')}, "
                      f"状态: {batch.get('transStatus')}, "
                      f"总金额: {batch.get('totAmt')}, "
                      f"成功笔数: {batch.get('sucNum')}")
        except CMBCException as e:
            print(f"查询批量转账结果失败: {e}")
            
    except CMBCException as e:
        print(f"操作失败: {e}")
    except Exception as e:
        print(f"未知错误: {e}")


if __name__ == "__main__":
    main()
