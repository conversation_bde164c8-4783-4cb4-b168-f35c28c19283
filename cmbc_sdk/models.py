"""
民生银行银企直联 SDK 数据模型定义
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from decimal import Decimal
from datetime import datetime
from enum import Enum


class PaymentType(Enum):
    """付款类型"""
    SALARY = "1"  # 代发工资
    EXPENSE = "2"  # 费用报销


class PaymentModel(Enum):
    """转账模式"""
    ONE_TO_ONE = "0"  # 一借一贷
    ONE_TO_MANY = "1"  # 一借多贷


class LocalFlag(Enum):
    """汇路类型"""
    INTERNAL = "0"  # 本行转账
    SMALL_AMOUNT = "2"  # 跨行转账-小额支付系统
    LARGE_AMOUNT = "3"  # 跨行转账-大额支付系统
    ONLINE_BANKING = "5"  # 跨行转账-网银互联
    AUTO_ROUTE = "9"  # 跨行转账-自动计算汇路


class ReceiverType(Enum):
    """收款人账户类型"""
    CORPORATE = "1"  # 对公
    PERSONAL = "2"  # 对私


class ExternBank(Enum):
    """跨行标识"""
    INTERNAL = "0"  # 本行
    EXTERNAL = "1"  # 跨行


# 费用报销用途代码
class PaymentPurpose:
    """费用报销用途代码"""
    TRAVEL_EXPENSE = "311"  # 差旅费
    OFFICE_EXPENSE = "312"  # 办公费
    UTILITY_EXPENSE = "313"  # 水电费
    COMMUNICATION_EXPENSE = "314"  # 通讯费
    TRANSPORT_EXPENSE = "315"  # 交通费
    NEWSPAPER_EXPENSE = "316"  # 报刊费
    MEAL_EXPENSE = "317"  # 餐费
    MEDICAL_EXPENSE = "318"  # 医药费
    MEETING_EXPENSE = "319"  # 会议费
    LEGAL_FEE = "374"  # 律师费
    REFUND = "375"  # 资金退还
    RELOCATION_COMPENSATION = "376"  # 拆迁补偿款
    ADVANCE_PAYMENT = "377"  # 垫付资金
    COUNTER_SALES = "378"  # 柜台销售结算款
    ONLINE_TRANSACTION = "379"  # 网上交易款
    SMALL_GOODS_SALES = "380"  # 小件商品销售收入
    PRODUCTION_FEE = "381"  # 制片费
    SCHOLARSHIP = "383"  # 奖助学金
    TUITION_REFUND = "384"  # 退学费
    DAILY_REIMBURSEMENT = "385"  # 日常报销费用
    DAILY_OPERATION = "386"  # 日常经营费用
    CASE_PAYMENT = "388"  # 案件款
    INVESTMENT_REDEMPTION = "389"  # 投资赎回款
    INVESTMENT_REFUND = "390"  # 投资退款
    DIVIDEND = "391"  # 分红
    PROJECT_PAYMENT = "392"  # 工程款
    RENT = "393"  # 租赁费
    FREIGHT = "394"  # 运费
    HOUSING_FUND = "395"  # 公积金
    COLLECTION_PAYMENT = "396"  # 代收货款


# 代发工资用途代码
class SalaryPurpose:
    """代发工资用途代码"""
    SALARY_BONUS = "4111"  # 工资奖金
    SALARY = "11"  # 工资
    BONUS = "12"  # 奖金
    HOLIDAY_FEE = "13"  # 过节费
    ALLOWANCE = "14"  # 补贴
    SERVICE_FEE = "15"  # 劳务费
    SALARY_PAY = "16"  # 薪资
    PERFORMANCE_SALARY = "17"  # 绩效工资
    PERFORMANCE_FEE = "171"  # 演出费
    SUBSIDY = "172"  # 津贴
    HOLIDAY_ALLOWANCE = "173"  # 节日慰问
    HOLIDAY_SUBSIDY = "174"  # 节日补助
    TRAVEL_SUBSIDY = "175"  # 旅游补贴
    LECTURE_FEE = "176"  # 讲课酬金
    INTERNSHIP_SUBSIDY = "177"  # 实习补贴
    MATERNITY_INSURANCE = "178"  # 生育保险
    DISABILITY_SUBSIDY = "179"  # 伤残补助金
    REIMBURSEMENT = "18"  # 报销
    YEAR_END_BONUS_DIFF = "180"  # 年终奖轧差


@dataclass
class TransferRequest:
    """单笔转账请求"""
    trn_id: str  # 客户端技术流水号
    ins_id: str  # 客户业务流水号
    acnt_no: str  # 付款账号
    acnt_name: str  # 付款人名称
    acnt_to_no: str  # 收款账号
    acnt_to_name: str  # 收款人名称
    extern_bank: ExternBank  # 跨行标识
    local_flag: LocalFlag  # 汇路
    rcv_cust_type: ReceiverType  # 收款人账户类型
    amount: Decimal  # 转账金额
    explain: str  # 用途说明
    bank_code: Optional[str] = None  # 收款人开户行行号
    bank_name: Optional[str] = None  # 收款人开户行名称
    bank_addr: Optional[str] = None  # 收款人开户行地址
    area_code: Optional[str] = None  # 收款行地区编号
    cert_no: Optional[str] = None  # 企业自制凭证号
    clt_cookie: Optional[str] = None  # 客户端cookie


@dataclass
class TransferResponse:
    """单笔转账响应"""
    trn_id: str  # 客户端交易的唯一标志
    ins_id: str  # 原值返回
    svr_id: Optional[str] = None  # 银行渠道流水号
    balance: Optional[Decimal] = None  # 余额
    route: Optional[str] = None  # 实际使用的汇路
    clt_cookie: Optional[str] = None  # 原值返回


@dataclass
class BatchTransferItem:
    """批量转账明细项"""
    ref_no: Optional[str] = None  # 交易参考号
    payee_acct_no: str = ""  # 收款账号
    payee_acct_name: str = ""  # 收款户名
    purpose: Optional[str] = None  # 用途
    remark: Optional[str] = None  # 备注
    bank_flag: str = ""  # 本他行标志 0:同行 1:他行
    route: Optional[str] = None  # 汇路
    bank_name: Optional[str] = None  # 收款行行名
    bank_code: Optional[str] = None  # 收款行行号
    cert_no: Optional[str] = None  # 企业自制凭证号
    amount: Decimal = Decimal('0')  # 金额
    
    def to_file_content_line(self) -> str:
        """转换为文件内容行格式"""
        return "|".join([
            self.ref_no or "",
            self.payee_acct_no,
            self.payee_acct_name,
            self.purpose or "",
            self.remark or "",
            self.bank_flag,
            self.route or "",
            self.bank_name or "",
            self.bank_code or "",
            self.cert_no or "",
            "",  # 备用字段1
            "",  # 备用字段2
            "",  # 备用字段3
            str(self.amount)
        ])


@dataclass
class BatchTransferRequest:
    """批量转账请求"""
    trn_id: str  # 客户端技术流水号
    ins_id: str  # 流水号
    payer_ac_no: str  # 付款账户
    pay_type: PaymentType  # 付款类型
    pay_model: PaymentModel  # 转账模式
    total_row: int  # 总记录数
    total_amt: Decimal  # 总金额数
    items: List[BatchTransferItem]  # 转账明细
    usage: Optional[str] = None  # 常用用途代码|备注
    file_content_type: str = "1"  # 文件内容类型 1:明文 2:SM4加密
    sec_key_enc: Optional[str] = None  # 加密会话密钥
    sec_key_index: Optional[str] = None  # 银行公钥索引
    clt_cookie: Optional[str] = None  # 客户端cookie
    
    def get_file_content(self) -> str:
        """获取文件内容"""
        lines = [item.to_file_content_line() for item in self.items]
        return "^".join(lines)


@dataclass
class BatchTransferResponse:
    """批量转账响应"""
    trn_id: str  # 原值返回
    ins_id: str  # 原值返回


@dataclass
class QueryTransferRequest:
    """转账结果查询请求"""
    trn_id: str  # 客户端技术流水号
    ins_id: str  # 原业务流水号
    clt_cookie: Optional[str] = None  # 客户端cookie


@dataclass
class QueryTransferResponse:
    """转账结果查询响应"""
    trn_id: str  # 客户端交易的唯一标志
    ins_id: str  # 原值返回
    status: str  # 交易状态
    status_desc: str  # 状态描述
    amount: Optional[Decimal] = None  # 交易金额
    balance: Optional[Decimal] = None  # 账户余额
    trans_date: Optional[str] = None  # 交易日期
    trans_time: Optional[str] = None  # 交易时间
    error_code: Optional[str] = None  # 错误码
    error_msg: Optional[str] = None  # 错误信息


@dataclass
class ExpenseReimbursementRequest(BatchTransferRequest):
    """费用报销请求（继承批量转账请求）"""
    
    def __post_init__(self):
        self.pay_type = PaymentType.EXPENSE


@dataclass
class SalaryPaymentRequest(BatchTransferRequest):
    """代发工资请求（继承批量转账请求）"""
    
    def __post_init__(self):
        self.pay_type = PaymentType.SALARY


@dataclass
class AccountInfo:
    """账户信息"""
    account_no: str  # 账号
    account_name: str  # 账户名称
    balance: Optional[Decimal] = None  # 余额
    currency: str = "CNY"  # 币种
    status: Optional[str] = None  # 账户状态


@dataclass
class BankInfo:
    """银行信息"""
    bank_code: str  # 银行代码
    bank_name: str  # 银行名称
    bank_addr: Optional[str] = None  # 银行地址
