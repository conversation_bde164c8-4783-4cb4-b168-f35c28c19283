"""
民生银行银企直联 SDK 异常类定义
"""


class CMBCException(Exception):
    """CMBC SDK 基础异常类"""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}
        
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {super().__str__()}"
        return super().__str__()


class CMBCAuthException(CMBCException):
    """认证相关异常"""
    pass


class CMBCNetworkException(CMBCException):
    """网络连接异常"""
    pass


class CMBCBusinessException(CMBCException):
    """业务逻辑异常"""
    pass


class CMBCValidationException(CMBCException):
    """参数验证异常"""
    pass


class CMBCTimeoutException(CMBCException):
    """超时异常"""
    pass


# 常见错误码映射
ERROR_CODE_MAPPING = {
    # 登录与权限相关
    'W3317': '该客户不存在',
    '111': '您的银企直联功能没有开通',
    'ELG20': '该客户或操作员尚未开户注册',
    'WF001': '操作员与主管不属于同一客户',
    'ELG23': '您不是企业银行客户操作员',
    'ELG21': '该客户已经取消网上银行系统服务',
    'ELG22': '该操作员已经被冻结',
    'ELG28': '您的证书信息和柜台签约时登记的证书信息不符',
    'ELG33': '对不起，您没有使用有效证书',
    'W3076': '该操作员对该账号没有操作权限',
    'W3170': '银企直联暂不支持外币账户',
    'W3075': '该操作员对该账号没有操作权限',
    'E0004': '系统错误：请与网银管理中心联系',
    'E1703': '该授权服务未签约',
    'W3780': '数据库操作错',
    
    # 转账相关
    'W3140': '转出账号不存在',
    'W3389': '转出账号账户已经注销',
    'W3390': '转出账号已经冻结',
    'W0001': '您的账号可能还未签约网银，或者账号有误',
    'W3888': '下级公司未授权此服务',
    'W3092': '没有可操作的账号',
    'W3065': '转出账号不存在（行内转账）',
    '10109': '重复流水号码',
    'W3155': '预约日期应大于今天',
    'W3128': '转出账号不是人民币账号',
    'W3462': '转出账号、币种或开户机构不存在',
    'E5001': '五万以上超出网银互联支付范围',
    'E5002': '五万以上超出小额支付范围',
    'E6032': '操作员一栏不能为空',
    'E6033': '{0}长度不能超过{1}',
    'E9115': '汇路不支持',
    'W3006': '行内转账不支持公转私',
    'W3007': '未找到该笔流水对应的交易{0}',
    'W3009': '用户没有行内转账功能权限',
    'W3010': '用户没有跨行转账功能权限',
    'W3015': '收款人姓名不能为空',
    'W3016': '收款人开户行行号不能为空',
    'W3017': '收款人开户行不能为空',
    'W3021': '收款人账号不能为空',
    'W3022': '收款人账号长度太长',
    'W3023': '收款人姓名长度太长',
    'W3024': '行内转账无此汇路',
    'W3025': '收款账户类型不能为空',
    'W3310': '用途太长，请重新填写',
    
    # 批量转账相关
    'E3301': '第{0}行,数据列数不正确',
    'E3302': '账号{0}是出款控制账号,不允许批量转账',
    'E3303': '上传明细数据总笔数{0}与输入的总笔数{1}不符',
    'E3304': '交易金额不能为0',
    'E3305': '上传明细数据总金额{0}与输入的总金额{1}不符',
    'E3306': '总笔数超过最大限制数',
    'E3307': '发放职工工资、奖金需通过基本存款账户办理',
    'E3308': '汇路不支持',
    'E3309': '企业自制凭证号为空',
    'E3310': '企业自制凭证号超过8位',
    'E3401': '{0}格式不正确',
    
    # 费用报销相关
    'E3311': '授权账户不可以做此交易',
    'W3001': '付款用途不能为空',
    'W3008': '不支持该项用途',
    'W3078': '账号没开通企业财务室',
    'E3312': '非授权账户不可以做此交易',
    'W3079': '此账号不是子公司账号',
}


def get_error_message(error_code):
    """根据错误码获取错误信息"""
    return ERROR_CODE_MAPPING.get(error_code, f"未知错误码: {error_code}")


def create_exception_from_response(response_data):
    """根据响应数据创建相应的异常"""
    status = response_data.get('responseHeader', {}).get('status', {})
    code = status.get('code', '0')
    message = status.get('message', '未知错误')
    
    if code == '0':
        return None
        
    # 根据错误码类型创建不同的异常
    if code in ['W3317', '111', 'ELG20', 'ELG21', 'ELG22', 'ELG23', 'ELG28', 'ELG33']:
        return CMBCAuthException(message, error_code=code)
    elif code in ['W3076', 'W3075', 'E1703', 'W3888']:
        return CMBCAuthException(message, error_code=code)
    elif code in ['E0004', 'W3780', 'W3020']:
        return CMBCNetworkException(message, error_code=code)
    else:
        return CMBCBusinessException(message, error_code=code)
